#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Split-Second Spark 单游戏部署打包工具
支持单独打包和部署某个游戏目录
"""

import os
import sys
import shutil
import zipfile
import json
import re
from pathlib import Path
from datetime import datetime

class SingleGamePackager:
    def __init__(self):
        self.root_dir = Path.cwd()
        self.available_games = ['时空织梦者', '瞬光捕手', '量子共鸣者']
        self.game_info = {
            '时空织梦者': {
                'name': 'Temporal Dream Weaver',
                'description': '时间操控解谜游戏',
                'features': ['时间操控', '策略解谜', '梦境编织']
            },
            '瞬光捕手': {
                'name': 'Split-Second Spark',
                'description': '反应速度挑战游戏',
                'features': ['精准时机', '连击系统', '反应挑战']
            },
            '量子共鸣者': {
                'name': 'Quantum Resonance',
                'description': '音乐节奏物理模拟游戏',
                'features': ['量子共鸣', '音乐节奏', '物理模拟']
            }
        }
        
        # 需要排除的文件模式
        self.exclude_patterns = [
            # 测试和调试文件
            r'.*test.*\.html$',
            r'.*debug.*\.html$',
            r'.*demo.*\.html$',
            r'.*测试.*\.html$',
            r'.*修复.*\.html$',
            r'.*验证.*\.html$',
            r'.*诊断.*\.html$',
            r'.*检查.*\.html$',
            r'.*fix.*\.html$',
            r'.*verification.*\.html$',
            r'.*diagnostic.*\.html$',
            
            # JavaScript测试和调试文件
            r'.*test.*\.js$',
            r'.*debug.*\.js$',
            r'.*fix.*\.js$',
            r'.*verification.*\.js$',
            r'.*diagnostic.*\.js$',
            r'.*测试.*\.js$',
            r'.*修复.*\.js$',
            r'.*验证.*\.js$',
            r'.*compatibility.*\.js$',
            r'.*performance.*\.js$',
            
            # 文档文件
            r'.*\.md$',
            r'.*/docs/.*',
            
            # 开发工具
            r'.*\.py$',
            r'.*\.bat$',
            r'.*\.sh$',
            
            # 临时文件和缓存
            r'.*/__pycache__/.*',
            r'.*\.log$',
            r'.*\.tmp$',
            r'.*\.cache$',
            
            # 版本控制
            r'.*\.git/.*',
            r'.*\.gitignore$',
            
            # IDE文件
            r'.*\.vscode/.*',
            r'.*\.idea/.*',
            r'.*\.DS_Store$',
            
            # 特定的开发文件
            r'.*create-.*\.html$',
            r'.*generate-.*\.html$',
            r'.*generate-.*\.js$',
            r'.*优化示例.*\.html$',
        ]

    def should_exclude(self, file_path):
        """检查文件是否应该被排除"""
        file_str = str(file_path)
        
        # 检查排除模式
        for pattern in self.exclude_patterns:
            if re.search(pattern, file_str, re.IGNORECASE):
                return True
        
        return False

    def list_available_games(self):
        """列出可用的游戏"""
        print("🎮 可用的游戏:")
        for i, game in enumerate(self.available_games, 1):
            game_dir = self.root_dir / game
            if game_dir.exists():
                print(f"  {i}. {game} ✅")
            else:
                print(f"  {i}. {game} ❌ (目录不存在)")

    def select_game(self):
        """选择要打包的游戏"""
        self.list_available_games()
        
        while True:
            try:
                choice = input("\n请选择要打包的游戏 (输入数字 1-3): ").strip()
                if choice.isdigit():
                    index = int(choice) - 1
                    if 0 <= index < len(self.available_games):
                        selected_game = self.available_games[index]
                        game_dir = self.root_dir / selected_game
                        if game_dir.exists():
                            return selected_game
                        else:
                            print(f"❌ 游戏目录 '{selected_game}' 不存在!")
                    else:
                        print("❌ 请输入有效的数字 (1-3)")
                else:
                    print("❌ 请输入数字")
            except KeyboardInterrupt:
                print("\n👋 用户取消操作")
                sys.exit(0)

    def copy_game_files(self, game_name, output_dir):
        """复制游戏文件到输出目录"""
        game_dir = self.root_dir / game_name
        target_dir = output_dir / game_name
        
        print(f"📁 复制游戏文件: {game_name}")
        
        copied_files = 0
        excluded_files = 0
        
        for root, dirs, files in os.walk(game_dir):
            # 跳过隐藏目录
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            for file in files:
                file_path = Path(root) / file
                relative_path = file_path.relative_to(game_dir)
                
                if self.should_exclude(file_path):
                    excluded_files += 1
                    continue
                
                # 创建目标目录
                target_file = target_dir / relative_path
                target_file.parent.mkdir(parents=True, exist_ok=True)
                
                # 复制文件
                shutil.copy2(file_path, target_file)
                copied_files += 1
                print(f"✅ 复制: {relative_path}")
        
        print(f"📊 复制统计: 复制 {copied_files} 个文件, 排除 {excluded_files} 个文件")
        return copied_files, excluded_files

    def create_standalone_index(self, game_name, output_dir):
        """创建独立的入口页面"""
        game_info = self.game_info[game_name]
        
        index_content = f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{game_name} - {game_info['name']}</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }}
        
        .container {{
            text-align: center;
            max-width: 600px;
        }}
        
        .game-title {{
            font-size: 3rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }}
        
        .game-description {{
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }}
        
        .features {{
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }}
        
        .feature {{
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }}
        
        .play-button {{
            display: inline-block;
            padding: 15px 40px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            text-decoration: none;
            border-radius: 30px;
            font-size: 1.2rem;
            font-weight: bold;
            transition: transform 0.3s ease;
        }}
        
        .play-button:hover {{
            transform: translateY(-3px);
        }}
        
        .info {{
            margin-top: 40px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }}
        
        @media (max-width: 768px) {{
            .game-title {{
                font-size: 2rem;
            }}
            
            .features {{
                flex-direction: column;
                align-items: center;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1 class="game-title">{game_name}</h1>
        <p class="game-description">{game_info['description']}</p>
        
        <div class="features">
            {' '.join([f'<span class="feature">{feature}</span>' for feature in game_info['features']])}
        </div>
        
        <a href="{game_name}/index.html" class="play-button">🎮 开始游戏</a>
        
        <div class="info">
            <h3>🚀 部署信息</h3>
            <p>这是 {game_name} 的独立部署版本</p>
            <p>可以直接部署到任何静态服务器</p>
            <p>支持PWA、离线游戏和响应式设计</p>
        </div>
    </div>
    
    <script>
        // 简单的性能监控
        window.addEventListener('load', () => {{
            const loadTime = performance.now();
            console.log(`🚀 {game_name} 独立版本加载完成，耗时: ${{loadTime.toFixed(2)}}ms`);
        }});
    </script>
</body>
</html>'''
        
        index_file = output_dir / 'index.html'
        with open(index_file, 'w', encoding='utf-8') as f:
            f.write(index_content)
        
        print(f"✅ 创建独立入口页面: index.html")

    def create_deployment_info(self, game_name, output_dir, stats):
        """创建部署信息文件"""
        game_info = self.game_info[game_name]
        
        deployment_info = {
            "name": f"{game_name} - 独立部署版",
            "game": {
                "chinese_name": game_name,
                "english_name": game_info['name'],
                "description": game_info['description'],
                "features": game_info['features']
            },
            "version": "1.0.0",
            "build_time": datetime.now().isoformat(),
            "deployment": {
                "type": "single_game_static",
                "entry_point": "/index.html",
                "game_entry": f"/{game_name}/index.html",
                "requirements": {
                    "web_server": "任何静态文件服务器",
                    "https": "推荐使用HTTPS (PWA和音频功能需要)",
                    "browser": "Chrome 60+, Firefox 55+, Safari 12+, Edge 79+"
                }
            },
            "build_stats": {
                "copied_files": stats['copied'],
                "excluded_files": stats['excluded'],
                "total_files": stats['copied'] + stats['excluded']
            },
            "build_info": {
                "packager": "Single Game Python Packager",
                "build_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "platform": os.name
            }
        }
        
        info_file = output_dir / 'deployment-info.json'
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(deployment_info, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建部署信息: deployment-info.json")

    def create_readme(self, game_name, output_dir):
        """创建README文件"""
        game_info = self.game_info[game_name]
        
        readme_content = f'''# {game_name} - 独立部署版

> {game_info['description']}

## 🎮 游戏特性

{chr(10).join([f"- **{feature}**" for feature in game_info['features']])}

## 🚀 部署说明

### 快速部署
1. 将整个文件夹上传到Web服务器
2. 访问 `http://your-domain.com/index.html`
3. 点击"开始游戏"按钮进入游戏

### 直接游戏
也可以直接访问游戏页面：
`http://your-domain.com/{game_name}/index.html`

## 📋 系统要求

- **Web服务器**: 任何静态文件服务器 (Nginx, Apache, IIS等)
- **HTTPS**: 推荐使用 (PWA和音频功能需要)
- **浏览器**: Chrome 60+, Firefox 55+, Safari 12+, Edge 79+

## 🌐 部署平台

### 免费平台
- **GitHub Pages**: 上传到GitHub仓库，启用Pages
- **Vercel**: 连接GitHub或直接上传
- **Netlify**: 拖拽文件夹到Netlify
- **Firebase Hosting**: Google提供的静态托管

### 商业平台
- **阿里云OSS**: 国内访问优化
- **腾讯云COS**: 国内高速访问
- **AWS S3**: 全球覆盖

## 🔧 本地测试

```bash
# 使用Python启动本地服务器
python -m http.server 8000

# 或使用Python 3
python3 -m http.server 8000

# 然后访问 http://localhost:8000
```

## 📁 文件结构

```
/
├── index.html              # 独立入口页面
├── deployment-info.json    # 部署信息
├── README.md              # 说明文档
└── {game_name}/           # 游戏文件
    ├── index.html         # 游戏主页面
    ├── js/               # JavaScript文件
    ├── styles/           # 样式文件
    └── assets/           # 资源文件 (如果有)
```

## 🎯 使用说明

1. **访问入口页面**: 打开 `index.html` 查看游戏介绍
2. **开始游戏**: 点击"开始游戏"按钮
3. **直接游戏**: 也可以直接访问 `{game_name}/index.html`

## 🔍 故障排除

### 常见问题
1. **页面空白**: 检查文件路径和服务器配置
2. **游戏无法启动**: 查看浏览器控制台错误
3. **资源加载失败**: 确保所有文件都已上传

### 调试方法
- 打开浏览器开发者工具 (F12)
- 查看Console标签页的错误信息
- 检查Network标签页的资源加载情况

---

**构建时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}  
**游戏版本**: 1.0.0  
**部署类型**: 单游戏独立部署
'''
        
        readme_file = output_dir / 'README.md'
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"✅ 创建说明文档: README.md")

    def create_zip_package(self, game_name, output_dir):
        """创建压缩包"""
        zip_filename = f"{game_name}-standalone.zip"
        zip_path = self.root_dir / zip_filename
        
        print(f"📦 正在创建压缩包: {zip_filename}")
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zf:
            for root, dirs, files in os.walk(output_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_name = file_path.relative_to(output_dir)
                    zf.write(file_path, arc_name)
        
        zip_size = zip_path.stat().st_size
        print(f"✅ 压缩包创建完成: {zip_filename} ({zip_size / 1024:.1f} KB)")
        
        return zip_path

    def run(self):
        """运行打包程序"""
        print("=" * 60)
        print("🎮 Split-Second Spark 单游戏部署打包工具")
        print("=" * 60)
        
        # 选择游戏
        game_name = self.select_game()
        print(f"\n🎯 选择的游戏: {game_name}")
        
        # 创建输出目录
        output_dir = self.root_dir / f"{game_name}-standalone"
        if output_dir.exists():
            shutil.rmtree(output_dir)
        output_dir.mkdir()
        
        try:
            # 复制游戏文件
            copied, excluded = self.copy_game_files(game_name, output_dir)
            
            # 创建独立入口页面
            self.create_standalone_index(game_name, output_dir)
            
            # 创建部署信息
            stats = {'copied': copied, 'excluded': excluded}
            self.create_deployment_info(game_name, output_dir, stats)
            
            # 创建README
            self.create_readme(game_name, output_dir)
            
            # 创建压缩包
            zip_path = self.create_zip_package(game_name, output_dir)
            
            # 显示完成信息
            print("\n" + "=" * 60)
            print("🎉 单游戏打包完成！")
            print("=" * 60)
            print(f"📁 部署目录: {output_dir.name}")
            print(f"📦 压缩包: {zip_path.name}")
            print(f"📊 文件统计: 复制 {copied} 个文件, 排除 {excluded} 个文件")
            
            print(f"\n📋 部署说明:")
            print(f"   1. 解压 {zip_path.name} 到Web服务器")
            print(f"   2. 访问 http://your-domain.com/index.html")
            print(f"   3. 或直接访问 http://your-domain.com/{game_name}/index.html")
            
            print(f"\n🌐 支持的部署平台:")
            print(f"   • GitHub Pages")
            print(f"   • Vercel")
            print(f"   • Netlify")
            print(f"   • 任何静态文件服务器")
            
        except Exception as e:
            print(f"\n❌ 打包过程中出现错误: {e}")
            return False
        
        return True

if __name__ == "__main__":
    packager = SingleGamePackager()
    success = packager.run()
    sys.exit(0 if success else 1)
