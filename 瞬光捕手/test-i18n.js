// i18n 服务单元测试
// 在 Node.js 环境中运行: node test-i18n.js

// 模拟浏览器环境
global.window = global;
global.document = {
    addEventListener: () => {},
    querySelectorAll: () => [],
    title: ''
};
global.console = console;

// 模拟存储服务
global.storageService = {
    initialized: true,
    get: async (key, defaultValue) => defaultValue,
    set: async (key, value) => {}
};

// 加载 i18n 服务
const fs = require('fs');
const path = require('path');

// 读取 i18n.js 文件内容
const i18nPath = path.join(__dirname, 'js/utils/i18n.js');
const i18nCode = fs.readFileSync(i18nPath, 'utf8');

// 执行代码
eval(i18nCode);

// 测试函数
async function runTests() {
    console.log('🧪 开始 i18n 服务单元测试\n');
    
    // 测试 1: 服务创建
    console.log('测试 1: 服务创建');
    const service = new I18nService();
    console.log(`✅ 服务创建成功: ${!!service}`);
    console.log(`✅ 默认语言: ${service.getCurrentLanguage()}`);
    console.log(`✅ 翻译数据存在: ${!!service.translations}`);
    console.log(`✅ 中文翻译数量: ${Object.keys(service.translations['zh-CN'] || {}).length}`);
    console.log(`✅ 英文翻译数量: ${Object.keys(service.translations['en-US'] || {}).length}\n`);
    
    // 测试 2: 中文翻译
    console.log('测试 2: 中文翻译');
    const zhTitle = service.t('game.title');
    const zhStart = service.t('menu.start');
    console.log(`game.title (zh): "${zhTitle}"`);
    console.log(`menu.start (zh): "${zhStart}"`);
    console.log(`✅ 中文翻译正常: ${zhTitle === '瞬光捕手' && zhStart === '开始游戏'}\n`);
    
    // 测试 3: 语言切换到英文
    console.log('测试 3: 切换到英文');
    await service.setLanguage('en-US');
    const currentLang = service.getCurrentLanguage();
    console.log(`当前语言: ${currentLang}`);
    
    const enTitle = service.t('game.title');
    const enStart = service.t('menu.start');
    console.log(`game.title (en): "${enTitle}"`);
    console.log(`menu.start (en): "${enStart}"`);
    console.log(`✅ 英文翻译正常: ${enTitle === 'Split-Second Spark' && enStart === 'Start Game'}\n`);
    
    // 测试 4: 切换回中文
    console.log('测试 4: 切换回中文');
    await service.setLanguage('zh-CN');
    const zhTitle2 = service.t('game.title');
    console.log(`game.title (zh): "${zhTitle2}"`);
    console.log(`✅ 切换回中文正常: ${zhTitle2 === '瞬光捕手'}\n`);
    
    // 测试 5: 不存在的翻译键
    console.log('测试 5: 不存在的翻译键');
    const nonExistent = service.t('non.existent.key');
    console.log(`不存在的键: "${nonExistent}"`);
    console.log(`✅ 返回键本身: ${nonExistent === 'non.existent.key'}\n`);
    
    // 测试 6: 参数替换
    console.log('测试 6: 参数替换');
    // 先添加一个带参数的翻译
    service.translations['zh-CN']['test.param'] = '你好 {{name}}，欢迎来到 {{game}}！';
    service.translations['en-US']['test.param'] = 'Hello {{name}}, welcome to {{game}}!';
    
    const paramZh = service.t('test.param', { name: '玩家', game: '瞬光捕手' });
    await service.setLanguage('en-US');
    const paramEn = service.t('test.param', { name: 'Player', game: 'Split-Second Spark' });
    
    console.log(`参数替换 (zh): "${paramZh}"`);
    console.log(`参数替换 (en): "${paramEn}"`);
    console.log(`✅ 参数替换正常: ${paramZh.includes('玩家') && paramEn.includes('Player')}\n`);
    
    console.log('🎉 所有测试完成！');
}

// 运行测试
runTests().catch(console.error);
