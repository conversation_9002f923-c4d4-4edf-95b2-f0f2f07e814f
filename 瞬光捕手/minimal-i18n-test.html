<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>最小化 i18n 测试</title>
</head>
<body>
    <h1>最小化 i18n 测试</h1>
    
    <div>
        <p>测试文本: <span id="test-text" data-i18n="game.title">瞬光捕手</span></p>
        <p>当前语言: <span id="current-lang">-</span></p>
    </div>
    
    <div>
        <button onclick="testSwitch()">切换到英文并测试</button>
        <button onclick="testSwitchBack()">切换回中文</button>
        <button onclick="manualApply()">手动应用翻译</button>
    </div>
    
    <div>
        <h3>调试信息:</h3>
        <pre id="debug-info"></pre>
    </div>

    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>

    <script>
        function updateDebugInfo() {
            const info = {
                storageService: !!window.storageService,
                storageInitialized: window.storageService?.initialized,
                i18nService: !!window.i18nService,
                i18nInitialized: window.i18nService?.initialized,
                currentLanguage: window.i18nService?.getCurrentLanguage(),
                hasZhTranslations: !!window.i18nService?.translations?.['zh-CN'],
                hasEnTranslations: !!window.i18nService?.translations?.['en-US'],
                zhTitleTranslation: window.i18nService?.translations?.['zh-CN']?.['game.title'],
                enTitleTranslation: window.i18nService?.translations?.['en-US']?.['game.title'],
                currentTranslation: window.i18nService?.t('game.title')
            };
            
            document.getElementById('debug-info').textContent = JSON.stringify(info, null, 2);
            document.getElementById('current-lang').textContent = info.currentLanguage || '-';
        }

        async function testSwitch() {
            console.log('=== 开始切换测试 ===');
            
            if (!window.i18nService) {
                console.error('i18nService 不存在');
                return;
            }
            
            console.log('切换前语言:', window.i18nService.getCurrentLanguage());
            console.log('切换前翻译:', window.i18nService.t('game.title'));
            
            try {
                await window.i18nService.setLanguage('en-US');
                console.log('切换后语言:', window.i18nService.getCurrentLanguage());
                console.log('切换后翻译:', window.i18nService.t('game.title'));
                
                updateDebugInfo();
                
            } catch (error) {
                console.error('切换失败:', error);
            }
        }

        async function testSwitchBack() {
            if (!window.i18nService) {
                console.error('i18nService 不存在');
                return;
            }
            
            try {
                await window.i18nService.setLanguage('zh-CN');
                updateDebugInfo();
            } catch (error) {
                console.error('切换失败:', error);
            }
        }

        function manualApply() {
            if (window.i18nService) {
                window.i18nService.applyTranslations();
                updateDebugInfo();
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                console.log('页面加载完成，开始测试');
                updateDebugInfo();
                
                // 每秒更新一次调试信息，直到服务初始化完成
                const interval = setInterval(() => {
                    updateDebugInfo();
                    if (window.i18nService?.initialized) {
                        clearInterval(interval);
                        console.log('i18n 服务初始化完成');
                    }
                }, 1000);
                
            }, 500);
        });
    </script>
</body>
</html>
