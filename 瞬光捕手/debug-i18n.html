<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>i18n 调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .debug-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #000;
            color: #00ff00;
            font-family: monospace;
            padding: 10px;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            font-size: 12px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>🔧 i18n 服务调试页面</h1>
    
    <div class="debug-panel">
        <h3>服务状态</h3>
        <div id="service-status"></div>
        <button onclick="checkServices()">检查服务状态</button>
        <button onclick="reinitServices()">重新初始化服务</button>
    </div>

    <div class="debug-panel">
        <h3>语言切换测试</h3>
        <div>
            <button onclick="switchToEnglish()">切换到英文</button>
            <button onclick="switchToChinese()">切换到中文</button>
            <button onclick="getCurrentLang()">获取当前语言</button>
        </div>
        <div id="language-status"></div>
    </div>

    <div class="debug-panel">
        <h3>翻译测试</h3>
        <div class="test-item">
            <strong>游戏标题:</strong> <span id="test-title" data-i18n="game.title">瞬光捕手</span>
        </div>
        <div class="test-item">
            <strong>开始游戏:</strong> <span id="test-start" data-i18n="menu.start">开始游戏</span>
        </div>
        <div class="test-item">
            <strong>设置:</strong> <span id="test-settings" data-i18n="settings.title">设置</span>
        </div>
        <button onclick="testTranslations()">测试翻译</button>
        <button onclick="applyTranslations()">应用翻译</button>
    </div>

    <div class="debug-panel">
        <h3>调试日志</h3>
        <button onclick="clearLog()">清空日志</button>
        <div id="debug-log" class="log"></div>
    </div>

    <!-- 加载脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>

    <script>
        let debugLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            debugLog.push(logEntry);
            
            console.log(logEntry);
            updateDebugLog();
            
            // 同时显示在状态区域
            if (type === 'error') {
                console.error(message);
            } else if (type === 'warn') {
                console.warn(message);
            }
        }

        function updateDebugLog() {
            const logDiv = document.getElementById('debug-log');
            logDiv.innerHTML = debugLog.slice(-20).join('\n');
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            debugLog = [];
            updateDebugLog();
        }

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function checkServices() {
            log('🔍 检查服务状态...');
            
            // 检查 storageService
            if (window.storageService) {
                const storageStatus = window.storageService.initialized ? '✅ 已初始化' : '⏳ 初始化中';
                log(`存储服务: ${storageStatus}`);
            } else {
                log('❌ 存储服务未找到', 'error');
            }
            
            // 检查 i18nService
            if (window.i18nService) {
                const i18nStatus = window.i18nService.initialized ? '✅ 已初始化' : '⏳ 初始化中';
                log(`国际化服务: ${i18nStatus}`);
                
                if (window.i18nService.initialized) {
                    const currentLang = window.i18nService.getCurrentLanguage();
                    log(`当前语言: ${currentLang}`);
                    
                    const supportedLangs = window.i18nService.getSupportedLanguages();
                    log(`支持的语言: ${supportedLangs.map(l => l.name).join(', ')}`);
                }
            } else {
                log('❌ 国际化服务未找到', 'error');
            }
            
            showStatus('service-status', '服务状态检查完成，请查看日志', 'success');
        }

        async function reinitServices() {
            log('🔄 重新初始化服务...');
            
            try {
                if (window.storageService) {
                    await window.storageService.init();
                    log('✅ 存储服务重新初始化完成');
                }
                
                if (window.i18nService) {
                    await window.i18nService.init();
                    log('✅ 国际化服务重新初始化完成');
                }
                
                showStatus('service-status', '服务重新初始化完成', 'success');
            } catch (error) {
                log(`❌ 服务重新初始化失败: ${error.message}`, 'error');
                showStatus('service-status', `重新初始化失败: ${error.message}`, 'error');
            }
        }

        async function switchToEnglish() {
            log('🌍 切换到英文...');
            
            if (!window.i18nService) {
                log('❌ i18nService 不存在', 'error');
                showStatus('language-status', 'i18nService 不存在', 'error');
                return;
            }
            
            try {
                await window.i18nService.setLanguage('en-US');
                const currentLang = window.i18nService.getCurrentLanguage();
                log(`✅ 语言切换成功，当前语言: ${currentLang}`);
                showStatus('language-status', `当前语言: ${currentLang}`, 'success');
                
                // 手动应用翻译
                window.i18nService.applyTranslations();
                
            } catch (error) {
                log(`❌ 语言切换失败: ${error.message}`, 'error');
                showStatus('language-status', `切换失败: ${error.message}`, 'error');
            }
        }

        async function switchToChinese() {
            log('🌍 切换到中文...');
            
            if (!window.i18nService) {
                log('❌ i18nService 不存在', 'error');
                showStatus('language-status', 'i18nService 不存在', 'error');
                return;
            }
            
            try {
                await window.i18nService.setLanguage('zh-CN');
                const currentLang = window.i18nService.getCurrentLanguage();
                log(`✅ 语言切换成功，当前语言: ${currentLang}`);
                showStatus('language-status', `当前语言: ${currentLang}`, 'success');
                
                // 手动应用翻译
                window.i18nService.applyTranslations();
                
            } catch (error) {
                log(`❌ 语言切换失败: ${error.message}`, 'error');
                showStatus('language-status', `切换失败: ${error.message}`, 'error');
            }
        }

        function getCurrentLang() {
            if (!window.i18nService) {
                log('❌ i18nService 不存在', 'error');
                showStatus('language-status', 'i18nService 不存在', 'error');
                return;
            }
            
            const currentLang = window.i18nService.getCurrentLanguage();
            log(`当前语言: ${currentLang}`);
            showStatus('language-status', `当前语言: ${currentLang}`, 'success');
        }

        function testTranslations() {
            log('🧪 测试翻译功能...');
            
            if (!window.i18nService) {
                log('❌ i18nService 不存在', 'error');
                return;
            }
            
            const testKeys = ['game.title', 'menu.start', 'settings.title'];
            testKeys.forEach(key => {
                const translation = window.i18nService.t(key);
                log(`翻译 "${key}": "${translation}"`);
            });
        }

        function applyTranslations() {
            log('🔄 应用翻译到页面...');
            
            if (!window.i18nService) {
                log('❌ i18nService 不存在', 'error');
                return;
            }
            
            window.i18nService.applyTranslations();
            log('✅ 翻译已应用到页面');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async () => {
            log('🚀 页面加载完成，开始初始化...');
            
            // 等待一段时间让服务初始化
            setTimeout(async () => {
                await checkServices();
                
                // 监听语言变更事件
                window.addEventListener('languageChanged', (event) => {
                    log(`🌍 语言变更事件: ${event.detail.language}`);
                });
                
                log('✅ 调试页面初始化完成');
            }, 1000);
        });
    </script>
</body>
</html>
