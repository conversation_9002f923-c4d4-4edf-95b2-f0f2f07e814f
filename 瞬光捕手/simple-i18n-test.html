<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单 i18n 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .demo-text {
            font-size: 18px;
            margin: 10px 0;
            padding: 10px;
            background: #e9ecef;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🧪 简单 i18n 测试</h1>
    
    <div class="test-section">
        <h3>1. 服务状态检查</h3>
        <button onclick="checkStatus()">检查状态</button>
        <div id="status-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 直接翻译测试</h3>
        <button onclick="testDirectTranslation()">测试直接翻译</button>
        <div id="translation-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 语言切换测试</h3>
        <button onclick="switchToEnglish()">切换到英文</button>
        <button onclick="switchToChinese()">切换到中文</button>
        <div id="switch-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. DOM 翻译演示</h3>
        <div class="demo-text" data-i18n="game.title">瞬光捕手</div>
        <div class="demo-text" data-i18n="menu.start">开始游戏</div>
        <div class="demo-text" data-i18n="settings.title">设置</div>
        <button onclick="applyDOMTranslations()">应用 DOM 翻译</button>
    </div>

    <!-- 加载脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>

    <script>
        function log(elementId, message) {
            document.getElementById(elementId).innerHTML += message + '<br>';
            console.log(message);
        }

        function clearLog(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        function checkStatus() {
            clearLog('status-result');
            
            log('status-result', '=== 服务状态检查 ===');
            
            // 检查 window 对象上的服务
            log('status-result', `storageService 存在: ${!!window.storageService}`);
            log('status-result', `i18nService 存在: ${!!window.i18nService}`);
            
            if (window.storageService) {
                log('status-result', `storageService 已初始化: ${window.storageService.initialized}`);
            }
            
            if (window.i18nService) {
                log('status-result', `i18nService 已初始化: ${window.i18nService.initialized}`);
                log('status-result', `当前语言: ${window.i18nService.getCurrentLanguage()}`);
                
                // 检查翻译数据
                const translations = window.i18nService.translations;
                log('status-result', `中文翻译数据存在: ${!!translations['zh-CN']}`);
                log('status-result', `英文翻译数据存在: ${!!translations['en-US']}`);
                
                if (translations['zh-CN']) {
                    log('status-result', `中文翻译键数量: ${Object.keys(translations['zh-CN']).length}`);
                }
                if (translations['en-US']) {
                    log('status-result', `英文翻译键数量: ${Object.keys(translations['en-US']).length}`);
                }
            }
        }

        function testDirectTranslation() {
            clearLog('translation-result');
            
            if (!window.i18nService) {
                log('translation-result', '❌ i18nService 不存在');
                return;
            }
            
            log('translation-result', '=== 直接翻译测试 ===');
            
            const testKeys = ['game.title', 'menu.start', 'settings.title'];
            const currentLang = window.i18nService.getCurrentLanguage();
            
            log('translation-result', `当前语言: ${currentLang}`);
            
            testKeys.forEach(key => {
                const translation = window.i18nService.t(key);
                log('translation-result', `${key}: "${translation}"`);
            });
        }

        async function switchToEnglish() {
            clearLog('switch-result');
            
            if (!window.i18nService) {
                log('switch-result', '❌ i18nService 不存在');
                return;
            }
            
            log('switch-result', '=== 切换到英文 ===');
            log('switch-result', `切换前语言: ${window.i18nService.getCurrentLanguage()}`);
            
            try {
                await window.i18nService.setLanguage('en-US');
                log('switch-result', `切换后语言: ${window.i18nService.getCurrentLanguage()}`);
                
                // 测试翻译
                const title = window.i18nService.t('game.title');
                const start = window.i18nService.t('menu.start');
                log('switch-result', `game.title: "${title}"`);
                log('switch-result', `menu.start: "${start}"`);
                
                // 应用到 DOM
                window.i18nService.applyTranslations();
                log('switch-result', '✅ DOM 翻译已应用');
                
            } catch (error) {
                log('switch-result', `❌ 切换失败: ${error.message}`);
            }
        }

        async function switchToChinese() {
            clearLog('switch-result');
            
            if (!window.i18nService) {
                log('switch-result', '❌ i18nService 不存在');
                return;
            }
            
            log('switch-result', '=== 切换到中文 ===');
            log('switch-result', `切换前语言: ${window.i18nService.getCurrentLanguage()}`);
            
            try {
                await window.i18nService.setLanguage('zh-CN');
                log('switch-result', `切换后语言: ${window.i18nService.getCurrentLanguage()}`);
                
                // 测试翻译
                const title = window.i18nService.t('game.title');
                const start = window.i18nService.t('menu.start');
                log('switch-result', `game.title: "${title}"`);
                log('switch-result', `menu.start: "${start}"`);
                
                // 应用到 DOM
                window.i18nService.applyTranslations();
                log('switch-result', '✅ DOM 翻译已应用');
                
            } catch (error) {
                log('switch-result', `❌ 切换失败: ${error.message}`);
            }
        }

        function applyDOMTranslations() {
            if (!window.i18nService) {
                alert('i18nService 不存在');
                return;
            }
            
            window.i18nService.applyTranslations();
            alert('DOM 翻译已应用');
        }

        // 页面加载完成后等待服务初始化
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                console.log('页面加载完成，开始检查服务状态');
                checkStatus();
            }, 1000);
        });
    </script>
</body>
</html>
