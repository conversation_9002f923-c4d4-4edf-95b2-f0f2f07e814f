<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终 i18n 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .demo-text {
            font-size: 24px;
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            text-align: center;
            transition: all 0.3s ease;
        }
        .demo-text:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 8px;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        button:active {
            transform: translateY(0);
        }
        .status {
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: rgba(46, 204, 113, 0.3);
            border: 1px solid #2ecc71;
        }
        .status.error {
            background: rgba(231, 76, 60, 0.3);
            border: 1px solid #e74c3c;
        }
        .status.info {
            background: rgba(52, 152, 219, 0.3);
            border: 1px solid #3498db;
        }
        .log {
            background: rgba(0, 0, 0, 0.5);
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 15px;
            border-radius: 8px;
            max-height: 200px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.4;
        }
        h1, h2, h3 {
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .current-lang {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 瞬光捕手 - 国际化测试</h1>
        <p>当前语言: <span id="current-language" class="current-lang">加载中...</span></p>
        
        <div class="test-section">
            <h3>🎮 游戏界面演示</h3>
            <div class="demo-text" data-i18n="game.title">瞬光捕手</div>
            <div class="demo-text" data-i18n="game.subtitle">捕捉决定性瞬间，引燃无限可能</div>
            <div class="demo-text" data-i18n="menu.start">开始游戏</div>
            <div class="demo-text" data-i18n="menu.settings">设置</div>
            <div class="demo-text" data-i18n="settings.language">语言设置</div>
        </div>

        <div class="test-section">
            <h3>🔄 语言切换控制</h3>
            <button onclick="switchLanguage('zh-CN')">🇨🇳 中文</button>
            <button onclick="switchLanguage('en-US')">🇺🇸 English</button>
            <button onclick="testAllTranslations()">🧪 测试所有翻译</button>
            <div id="switch-status"></div>
        </div>

        <div class="test-section">
            <h3>📊 服务状态</h3>
            <button onclick="checkServiceStatus()">检查状态</button>
            <button onclick="reinitializeServices()">重新初始化</button>
            <div id="service-status"></div>
        </div>

        <div class="test-section">
            <h3>📝 操作日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="operation-log" class="log"></div>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>

    <script>
        let operationLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            operationLog.push(logEntry);
            
            console.log(logEntry);
            updateLog();
            
            if (type === 'error') {
                console.error(message);
            } else if (type === 'warn') {
                console.warn(message);
            }
        }

        function updateLog() {
            const logDiv = document.getElementById('operation-log');
            logDiv.innerHTML = operationLog.slice(-15).join('\n');
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            operationLog = [];
            updateLog();
        }

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function updateCurrentLanguage() {
            const langElement = document.getElementById('current-language');
            if (window.i18nService && window.i18nService.initialized) {
                const currentLang = window.i18nService.getCurrentLanguage();
                const langName = currentLang === 'zh-CN' ? '中文' : 'English';
                langElement.textContent = `${langName} (${currentLang})`;
            } else {
                langElement.textContent = '服务未初始化';
            }
        }

        async function switchLanguage(language) {
            log(`🌍 开始切换语言到: ${language}`);
            
            if (!window.i18nService) {
                log('❌ i18nService 不存在', 'error');
                showStatus('switch-status', 'i18nService 不存在', 'error');
                return;
            }

            if (!window.i18nService.initialized) {
                log('⏳ 等待 i18nService 初始化...', 'warn');
                showStatus('switch-status', '等待服务初始化...', 'info');
                
                // 等待初始化
                let attempts = 0;
                while (!window.i18nService.initialized && attempts < 10) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                    attempts++;
                }
                
                if (!window.i18nService.initialized) {
                    log('❌ 服务初始化超时', 'error');
                    showStatus('switch-status', '服务初始化超时', 'error');
                    return;
                }
            }

            try {
                const oldLang = window.i18nService.getCurrentLanguage();
                log(`切换前语言: ${oldLang}`);
                
                await window.i18nService.setLanguage(language);
                
                const newLang = window.i18nService.getCurrentLanguage();
                log(`切换后语言: ${newLang}`);
                
                if (newLang === language) {
                    log(`✅ 语言切换成功: ${language}`);
                    showStatus('switch-status', `语言切换成功: ${language}`, 'success');
                    
                    // 应用翻译到页面
                    window.i18nService.applyTranslations();
                    updateCurrentLanguage();
                    
                    // 测试几个关键翻译
                    const title = window.i18nService.t('game.title');
                    const start = window.i18nService.t('menu.start');
                    log(`当前翻译 - 标题: "${title}", 开始: "${start}"`);
                    
                } else {
                    log(`❌ 语言切换失败，期望: ${language}, 实际: ${newLang}`, 'error');
                    showStatus('switch-status', `语言切换失败`, 'error');
                }
                
            } catch (error) {
                log(`❌ 语言切换异常: ${error.message}`, 'error');
                showStatus('switch-status', `切换异常: ${error.message}`, 'error');
            }
        }

        function testAllTranslations() {
            log('🧪 开始测试所有翻译...');
            
            if (!window.i18nService || !window.i18nService.initialized) {
                log('❌ i18nService 未初始化', 'error');
                return;
            }
            
            const testKeys = [
                'game.title', 'game.subtitle', 'menu.start', 'menu.settings', 
                'settings.language', 'settings.title', 'common.ok', 'common.cancel'
            ];
            
            const currentLang = window.i18nService.getCurrentLanguage();
            log(`当前语言: ${currentLang}`);
            
            testKeys.forEach(key => {
                const translation = window.i18nService.t(key);
                log(`${key}: "${translation}"`);
            });
            
            log('✅ 翻译测试完成');
        }

        function checkServiceStatus() {
            log('🔍 检查服务状态...');
            
            const storageStatus = window.storageService ? 
                (window.storageService.initialized ? '✅ 已初始化' : '⏳ 初始化中') : 
                '❌ 不存在';
            
            const i18nStatus = window.i18nService ? 
                (window.i18nService.initialized ? '✅ 已初始化' : '⏳ 初始化中') : 
                '❌ 不存在';
            
            log(`存储服务: ${storageStatus}`);
            log(`国际化服务: ${i18nStatus}`);
            
            if (window.i18nService && window.i18nService.initialized) {
                const currentLang = window.i18nService.getCurrentLanguage();
                const supportedLangs = window.i18nService.getSupportedLanguages();
                log(`当前语言: ${currentLang}`);
                log(`支持语言: ${supportedLangs.map(l => l.name).join(', ')}`);
                
                const zhCount = Object.keys(window.i18nService.translations['zh-CN'] || {}).length;
                const enCount = Object.keys(window.i18nService.translations['en-US'] || {}).length;
                log(`中文翻译数量: ${zhCount}`);
                log(`英文翻译数量: ${enCount}`);
            }
            
            showStatus('service-status', '服务状态检查完成，请查看日志', 'success');
        }

        async function reinitializeServices() {
            log('🔄 重新初始化服务...');
            
            try {
                if (window.storageService) {
                    await window.storageService.init();
                    log('✅ 存储服务重新初始化完成');
                }
                
                if (window.i18nService) {
                    await window.i18nService.init();
                    log('✅ 国际化服务重新初始化完成');
                    updateCurrentLanguage();
                }
                
                showStatus('service-status', '服务重新初始化完成', 'success');
            } catch (error) {
                log(`❌ 服务重新初始化失败: ${error.message}`, 'error');
                showStatus('service-status', `重新初始化失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 页面加载完成，开始初始化...');
            
            // 等待服务初始化
            setTimeout(() => {
                checkServiceStatus();
                updateCurrentLanguage();
                
                // 监听语言变更事件
                window.addEventListener('languageChanged', (event) => {
                    log(`🌍 收到语言变更事件: ${event.detail.language}`);
                    updateCurrentLanguage();
                });
                
                log('✅ 测试页面初始化完成');
                
                // 如果服务已经初始化，应用翻译
                if (window.i18nService && window.i18nService.initialized) {
                    window.i18nService.applyTranslations();
                }
                
            }, 1000);
        });
    </script>
</body>
</html>
