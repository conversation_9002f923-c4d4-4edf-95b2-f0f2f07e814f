<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瞬光捕手 - Split-Second Spark</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            text-align: center;
            max-width: 600px;
        }
        
        .game-title {
            font-size: 3rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .game-description {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .features {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .play-button {
            display: inline-block;
            padding: 15px 40px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            text-decoration: none;
            border-radius: 30px;
            font-size: 1.2rem;
            font-weight: bold;
            transition: transform 0.3s ease;
        }
        
        .play-button:hover {
            transform: translateY(-3px);
        }
        
        .info {
            margin-top: 40px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        @media (max-width: 768px) {
            .game-title {
                font-size: 2rem;
            }
            
            .features {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="game-title">瞬光捕手</h1>
        <p class="game-description">反应速度挑战游戏</p>
        
        <div class="features">
            <span class="feature">精准时机</span> <span class="feature">连击系统</span> <span class="feature">反应挑战</span>
        </div>
        
        <a href="瞬光捕手/index.html" class="play-button">🎮 开始游戏</a>
        
        <div class="info">
            <h3>🚀 部署信息</h3>
            <p>这是 瞬光捕手 的独立部署版本</p>
            <p>可以直接部署到任何静态服务器</p>
            <p>支持PWA、离线游戏和响应式设计</p>
        </div>
    </div>
    
    <script>
        // 简单的性能监控
        window.addEventListener('load', () => {
            const loadTime = performance.now();
            console.log(`🚀 瞬光捕手 独立版本加载完成，耗时: ${loadTime.toFixed(2)}ms`);
        });
    </script>
</body>
</html>