/* 瞬光捕手 - 现代化主样式文件 */

/* CSS 自定义属性 - 现代化色彩系统 */
:root {
    /* 主色调 - 深空蓝紫渐变 */
    --primary-bg: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 100%);
    --secondary-bg: linear-gradient(135deg, #16213e 0%, #1a1a2e 50%, #0f0f23 100%);

    /* 霓虹色彩系统 */
    --neon-blue: #00d4ff;
    --neon-purple: #b347d9;
    --neon-pink: #ff006e;
    --neon-green: #00f5a0;
    --neon-orange: #ff8500;
    --neon-yellow: #ffbe0b;

    /* 玻璃态效果 */
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    --glass-blur: blur(20px);

    /* 文字颜色 */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --text-tertiary: rgba(255, 255, 255, 0.6);
    --text-accent: var(--neon-blue);

    /* 动画缓动函数 */
    --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
    --ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);
    --ease-elastic: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', 'Arial', sans-serif;
    background: var(--primary-bg);
    color: var(--text-primary);
    overflow: hidden;
    user-select: none;
    position: relative;
}

/* 动态背景粒子系统 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(179, 71, 217, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 0, 110, 0.05) 0%, transparent 50%);
    animation: backgroundShift 20s ease-in-out infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes backgroundShift {
    0%, 100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
    33% {
        opacity: 0.8;
        transform: scale(1.1) rotate(120deg);
    }
    66% {
        opacity: 0.6;
        transform: scale(0.9) rotate(240deg);
    }
}

/* 游戏容器 - 现代化设计 */
#game-container {
    width: 100vw;
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

/* 通用屏幕样式 - 增强过渡效果 */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px) scale(0.95);
    transition: all 0.6s var(--ease-out-quart);
    z-index: 1;
    backdrop-filter: var(--glass-blur);
}

.screen.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
    z-index: 10;
}

/* 屏幕切换动画 */
.screen.entering {
    animation: screenEnter 0.8s var(--ease-out-quart) forwards;
}

.screen.leaving {
    animation: screenLeave 0.6s var(--ease-in-out-quart) forwards;
}

@keyframes screenEnter {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
        filter: blur(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0);
    }
}

@keyframes screenLeave {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0);
    }
    100% {
        opacity: 0;
        transform: translateY(-20px) scale(1.05);
        filter: blur(5px);
    }
}

.screen.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

/* 覆盖层样式 - 现代化玻璃态设计 */
.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: var(--glass-blur);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    transition: all 0.4s var(--ease-out-quart);
    opacity: 0;
    visibility: hidden;
}

.overlay:not(.hidden) {
    opacity: 1;
    visibility: visible;
}

.overlay.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.overlay-content {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    backdrop-filter: var(--glass-blur);
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: var(--glass-shadow);
    text-align: center;
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
    position: relative;
    transform: translateY(20px) scale(0.9);
    transition: all 0.4s var(--ease-out-quart);
}

.overlay:not(.hidden) .overlay-content {
    transform: translateY(0) scale(1);
}

/* 覆盖层内容发光边框效果 */
.overlay-content::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(45deg, var(--neon-blue), var(--neon-purple), var(--neon-pink), var(--neon-blue));
    border-radius: 20px;
    z-index: -1;
    opacity: 0.3;
    animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

/* 加载界面 - 现代化设计 */
#loading-screen {
    background: var(--primary-bg);
    position: relative;
    overflow: hidden;
}

/* 加载界面动态背景 */
#loading-screen::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background:
        radial-gradient(circle at 30% 70%, rgba(0, 212, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(179, 71, 217, 0.15) 0%, transparent 50%);
    animation: loadingBgRotate 15s linear infinite;
    z-index: 1;
}

@keyframes loadingBgRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content {
    text-align: center;
    position: relative;
    z-index: 2;
}

/* 现代化加载动画 */
.loading-spinner {
    width: 80px;
    height: 80px;
    margin: 2rem auto;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner::before,
.loading-spinner::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    border: 3px solid transparent;
}

.loading-spinner::before {
    width: 100%;
    height: 100%;
    border-top: 3px solid var(--neon-blue);
    border-right: 3px solid var(--neon-purple);
    animation: spinOuter 2s linear infinite;
}

.loading-spinner::after {
    width: 60%;
    height: 60%;
    border-bottom: 3px solid var(--neon-pink);
    border-left: 3px solid var(--neon-orange);
    animation: spinInner 1.5s linear infinite reverse;
}

@keyframes spinOuter {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes spinInner {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 标题样式 - 现代化霓虹效果 */
.game-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple), var(--neon-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: titleGradient 4s ease-in-out infinite, titleFloat 3s ease-in-out infinite alternate;
    letter-spacing: 3px;
    text-transform: uppercase;
    position: relative;
    filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.5));
}

.game-title::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    z-index: -1;
    filter: blur(10px);
    opacity: 0.7;
}

@keyframes titleGradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes titleFloat {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-5px); }
}

.game-subtitle {
    font-size: 1.4rem;
    color: var(--text-secondary);
    margin-bottom: 2.5rem;
    font-weight: 300;
    letter-spacing: 1px;
    animation: subtitlePulse 2s ease-in-out infinite alternate;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

@keyframes subtitlePulse {
    0% { opacity: 0.7; transform: scale(1); }
    100% { opacity: 1; transform: scale(1.02); }
}

/* 按钮样式 - 现代化玻璃态设计 */
.menu-btn {
    display: block;
    width: 280px;
    padding: 18px 35px;
    margin: 12px auto;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    backdrop-filter: var(--glass-blur);
    color: var(--text-primary);
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s var(--ease-out-quart);
    box-shadow: var(--glass-shadow);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* 按钮光效动画 */
.menu-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.menu-btn:hover::before {
    left: 100%;
}

.menu-btn:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
}

/* 触摸反馈样式 */
.menu-btn.touch-active,
.menu-btn:active {
    transform: translateY(-2px) scale(0.98);
    transition: all 0.1s ease;
}

.menu-btn.primary {
    background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple));
    border: 1px solid var(--neon-blue);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
    color: white;
}

.menu-btn.primary:hover {
    box-shadow: 0 15px 40px rgba(0, 212, 255, 0.5);
    transform: translateY(-5px) scale(1.05);
}

.small-btn {
    padding: 8px 16px;
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
    border: none;
    border-radius: 15px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 5px;
}

.small-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.small-btn.touch-active,
.small-btn:active {
    transform: translateY(0px) scale(0.9);
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
    transition: all 0.1s ease;
}

/* 主菜单样式 */
.menu-content {
    text-align: center;
    max-width: 400px;
}

.menu-buttons {
    margin: 2rem 0;
}

.player-info {
    margin-top: 2rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.current-player {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

/* 游戏界面样式 */
#game-screen {
    padding: 0;
}

.game-ui {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* 游戏头部 - 现代化玻璃态设计 */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.2rem 2.5rem;
    background: var(--glass-bg);
    border-bottom: 1px solid var(--glass-border);
    backdrop-filter: var(--glass-blur);
    flex-wrap: wrap;
    gap: 1.5rem;
    position: relative;
}

.game-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--neon-blue), transparent);
    animation: headerGlow 3s ease-in-out infinite;
}

@keyframes headerGlow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.8; }
}

.score-info, .level-info, .lives-info {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--text-primary);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.score-info:hover, .level-info:hover, .lives-info:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.05);
}

/* 游戏画布容器 - 增强视觉效果 */
#game-canvas-container {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background:
        radial-gradient(circle at 30% 70%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(179, 71, 217, 0.1) 0%, transparent 50%),
        var(--secondary-bg);
    overflow: hidden;
}

/* 动态背景粒子 */
#game-canvas-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.05) 0%, transparent 40%),
        radial-gradient(circle at 80% 20%, rgba(255, 0, 110, 0.05) 0%, transparent 40%);
    animation: canvasBgShift 15s ease-in-out infinite;
    pointer-events: none;
}

@keyframes canvasBgShift {
    0%, 100% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.1); }
}

#game-canvas {
    border: 2px solid var(--glass-border);
    border-radius: 15px;
    box-shadow:
        0 0 30px rgba(0, 0, 0, 0.5),
        inset 0 0 20px rgba(0, 212, 255, 0.1);
    background: #000;
    position: relative;
    transition: all 0.3s ease;
}

#game-canvas:hover {
    box-shadow:
        0 0 40px rgba(0, 212, 255, 0.3),
        inset 0 0 30px rgba(0, 212, 255, 0.15);
}

/* 游戏提示区域 */
.game-hints {
    padding: 1.2rem;
    text-align: center;
    background: var(--glass-bg);
    border-top: 1px solid var(--glass-border);
    backdrop-filter: var(--glass-blur);
    position: relative;
}

.game-hints::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--neon-purple), transparent);
    animation: hintsGlow 4s ease-in-out infinite;
}

@keyframes hintsGlow {
    0%, 100% { opacity: 0.2; }
    50% { opacity: 0.6; }
}

#hint-text {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-accent);
    text-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
    animation: hintPulse 2s ease-in-out infinite alternate;
}

@keyframes hintPulse {
    0% { opacity: 0.8; transform: scale(1); }
    100% { opacity: 1; transform: scale(1.02); }
}

/* 触摸友好元素通用样式 */
.touch-friendly {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    touch-action: manipulation;
}

/* 点击反馈动画 */
.click-feedback {
    animation: clickPulse 0.15s ease-out;
}

@keyframes clickPulse {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

/* 触摸控制区域 */
.touch-controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: none;
}

.touch-area {
    width: 200px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
}

.touch-area:active {
    background: rgba(255, 255, 255, 0.4);
    transform: scale(0.95);
}

/* 设置界面样式 */
.settings-content {
    max-width: 400px;
    width: 100%;
}

.setting-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 1.5rem 0;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.setting-group label {
    font-weight: bold;
    min-width: 80px;
}

.setting-group select,
.setting-group input[type="range"] {
    flex: 1;
    min-width: 150px;
}

.setting-group select {
    padding: 8px;
    border: none;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
}

.setting-group input[type="range"] {
    margin: 0 10px;
}

/* 玩家管理界面 */
.player-list, .new-player {
    margin: 1.5rem 0;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.player-list h3, .new-player h3 {
    margin-bottom: 1rem;
    color: #ffd700;
}

#existing-players {
    max-height: 200px;
    overflow-y: auto;
}

.player-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    margin: 0.5rem 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.player-item.active {
    background: rgba(255, 215, 0, 0.2);
    border: 1px solid #ffd700;
}

.new-player {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.new-player input {
    padding: 10px;
    border: none;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-size: 1rem;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 排行榜界面样式 */
.leaderboard-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.leaderboard-tabs {
    display: flex;
    justify-content: center;
    margin: 2rem 0;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tab-btn {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.tab-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.tab-btn.touch-active,
.tab-btn:active {
    transform: translateY(0px) scale(0.95);
    background: rgba(255, 255, 255, 0.3);
    transition: all 0.1s ease;
}

.tab-btn.active {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    border-color: transparent;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.leaderboard-list {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    overflow: hidden;
    margin: 2rem 0;
}

.leaderboard-header {
    display: grid;
    grid-template-columns: 60px 1fr 100px 120px;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    font-weight: bold;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.leaderboard-entries {
    max-height: 400px;
    overflow-y: auto;
}

.leaderboard-entry {
    display: grid;
    grid-template-columns: 60px 1fr 100px 120px;
    gap: 1rem;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: background 0.3s ease;
}

.leaderboard-entry:hover {
    background: rgba(255, 255, 255, 0.05);
}

.leaderboard-entry.current-player {
    background: rgba(76, 175, 80, 0.2);
    border-left: 4px solid #4CAF50;
}

.rank {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
}

.rank.top-3 {
    color: #FFD700;
}

.rank.rank-1::before {
    content: "🥇";
    margin-right: 0.25rem;
}

.rank.rank-2::before {
    content: "🥈";
    margin-right: 0.25rem;
}

.rank.rank-3::before {
    content: "🥉";
    margin-right: 0.25rem;
}

.player-name {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.player-name .name {
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.player-name .details {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

.score {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
    color: #4ecdc4;
}

.time {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

.player-rank-info {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 1rem;
    margin: 2rem 0;
    text-align: center;
}

.player-rank {
    font-size: 1.1rem;
}

.player-rank .rank-text {
    color: #4ecdc4;
    font-weight: bold;
}

.empty-leaderboard {
    text-align: center;
    padding: 3rem;
    color: rgba(255, 255, 255, 0.7);
}

.empty-leaderboard .icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.loading-leaderboard {
    text-align: center;
    padding: 2rem;
}

.loading-leaderboard .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-left: 4px solid #4ecdc4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

/* 关卡编辑器界面样式 */
.editor-layout {
    display: grid;
    grid-template-columns: 250px 1fr 300px;
    grid-template-rows: 1fr;
    height: 100vh;
    background: #1a1a2e;
}

.editor-toolbar {
    background: rgba(0, 0, 0, 0.3);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem;
    overflow-y: auto;
}

.toolbar-section {
    margin-bottom: 2rem;
}

.toolbar-section h3 {
    color: #4ecdc4;
    margin-bottom: 1rem;
    font-size: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 0.5rem;
}

.tool-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.tool-btn {
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.tool-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.tool-btn.touch-active,
.tool-btn:active {
    transform: translateY(0px) scale(0.95);
    background: rgba(255, 255, 255, 0.3);
    transition: all 0.1s ease;
}

.tool-btn.active {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    border-color: transparent;
    box-shadow: 0 2px 10px rgba(255, 107, 107, 0.3);
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.action-btn {
    padding: 0.75rem;
    background: rgba(76, 175, 80, 0.2);
    border: 1px solid #4CAF50;
    border-radius: 5px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.action-btn:hover {
    background: rgba(76, 175, 80, 0.4);
    transform: translateY(-1px);
}

.action-btn.touch-active,
.action-btn:active {
    transform: translateY(0px) scale(0.95);
    background: rgba(76, 175, 80, 0.5);
    transition: all 0.1s ease;
}

.view-controls {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.view-controls label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    font-size: 0.9rem;
}

.view-controls input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

.zoom-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.zoom-controls button {
    width: 30px;
    height: 30px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.zoom-controls button:hover {
    background: rgba(255, 255, 255, 0.2);
}

#zoom-level {
    color: white;
    font-size: 0.9rem;
    min-width: 50px;
    text-align: center;
}

.editor-main {
    position: relative;
    background: #16213e;
    overflow: hidden;
}

.editor-canvas-container {
    width: 100%;
    height: 100%;
    position: relative;
}

#level-editor-canvas {
    display: block;
    cursor: crosshair;
    background: transparent;
}

.editor-properties {
    background: rgba(0, 0, 0, 0.3);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem;
    overflow-y: auto;
}

.properties-section {
    margin-bottom: 2rem;
}

.properties-section h3 {
    color: #4ecdc4;
    margin-bottom: 1rem;
    font-size: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 0.5rem;
}

.setting-item {
    margin-bottom: 1rem;
}

.setting-item label {
    display: block;
    color: white;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.setting-item input,
.setting-item textarea,
.setting-item select {
    width: 100%;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    color: white;
    font-size: 0.9rem;
}

.setting-item textarea {
    height: 60px;
    resize: vertical;
}

.setting-item input::placeholder,
.setting-item textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

#property-panel {
    color: white;
}

.property-item {
    margin-bottom: 0.5rem;
}

.property-item label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 3px;
    transition: background 0.3s ease;
}

.property-item label:hover {
    background: rgba(255, 255, 255, 0.1);
}

.property-item input[type="radio"] {
    width: 16px;
    height: 16px;
}

.color-preview {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
    display: inline-block;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 0.9rem;
}

.stat-item:last-child {
    border-bottom: none;
}

.editor-nav {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 1000;
}

.nav-btn {
    padding: 0.75rem 1.5rem;
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translateY(-1px);
}

/* 对话框样式 */
.dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.dialog.hidden {
    display: none;
}

.dialog-content {
    background: #1a1a2e;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 2rem;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.dialog-content h3 {
    color: #4ecdc4;
    margin-bottom: 1.5rem;
    text-align: center;
}

.level-list {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 1.5rem;
}

.level-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.level-item:hover {
    background: rgba(255, 255, 255, 0.2);
}

.level-item.selected {
    background: rgba(76, 175, 80, 0.3);
    border-color: #4CAF50;
}

.level-info h4 {
    color: white;
    margin-bottom: 0.25rem;
}

.level-info p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
}

.level-meta {
    text-align: right;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

.dialog-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.dialog-btn {
    padding: 0.75rem 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dialog-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.dialog-btn.touch-active,
.dialog-btn:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.3);
    transition: all 0.1s ease;
}

.dialog-btn.primary {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    border-color: transparent;
}

/* 关卡编辑器移动端适配 */
@media (max-width: 1024px) {
    .editor-layout {
        grid-template-columns: 200px 1fr 250px;
    }

    .editor-toolbar {
        padding: 0.5rem;
    }

    .tool-buttons {
        grid-template-columns: 1fr;
    }

    .editor-properties {
        padding: 0.5rem;
    }
}

@media (max-width: 768px) {
    .editor-layout {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
    }

    .editor-toolbar {
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        padding: 1rem;
        max-height: 200px;
        overflow-y: auto;
    }

    .toolbar-section {
        margin-bottom: 1rem;
    }

    .tool-buttons {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.25rem;
    }

    .tool-btn {
        padding: 0.5rem 0.25rem;
        font-size: 0.7rem;
    }

    .action-buttons {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .action-btn {
        flex: 1;
        min-width: 80px;
        padding: 0.5rem 0.25rem;
        font-size: 0.8rem;
    }

    .editor-properties {
        border-left: none;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        max-height: 250px;
        overflow-y: auto;
    }

    .properties-section {
        margin-bottom: 1rem;
    }

    .dialog-content {
        margin: 1rem;
        padding: 1rem;
        max-width: calc(100% - 2rem);
    }

    .level-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .level-meta {
        text-align: left;
        width: 100%;
    }

    .dialog-buttons {
        flex-direction: column;
    }

    .dialog-btn {
        width: 100%;
    }

    .editor-nav {
        position: relative;
        top: auto;
        right: auto;
        padding: 1rem;
        text-align: center;
    }

    #level-editor-canvas {
        cursor: default;
    }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }

    .game-title {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .menu-buttons {
        gap: 0.75rem;
    }

    .menu-btn {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    .game-canvas {
        max-width: 100%;
        max-height: 60vh;
    }

    .game-ui {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0.5rem;
    }

    .ui-section {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    .ui-item {
        margin: 0;
    }

    .touch-controls {
        display: flex;
        justify-content: center;
        gap: 1rem;
        padding: 1rem;
    }

    .touch-btn {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        font-size: 1.2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .touch-btn:active {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0.95);
    }

    .level-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .level-card {
        padding: 1rem;
    }

    .player-list {
        grid-template-columns: 1fr;
    }

    .player-card {
        padding: 1rem;
    }

    .leaderboard-tabs {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .leaderboard-tab {
        flex: 1;
        min-width: 80px;
        padding: 0.5rem 0.25rem;
        font-size: 0.8rem;
    }

    .leaderboard-entries {
        gap: 0.5rem;
    }

    .leaderboard-entry {
        padding: 0.75rem;
        grid-template-columns: auto 1fr auto;
        gap: 0.75rem;
    }

    .entry-rank {
        font-size: 0.9rem;
    }

    .entry-info h4 {
        font-size: 0.9rem;
    }

    .entry-info p {
        font-size: 0.7rem;
    }

    .entry-score {
        font-size: 0.9rem;
    }
}

/* 自定义关卡界面样式 */
.custom-levels-layout {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
}

.custom-levels-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-left h2 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #4CAF50, #2196F3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-left p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
}

/* 控制面板 */
.levels-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-wrap: wrap;
    gap: 1rem;
}

.controls-section {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.filter-controls,
.sort-controls,
.search-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-controls label,
.sort-controls label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.filter-controls select,
.sort-controls select {
    padding: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 0.9rem;
}

.search-controls {
    position: relative;
}

.search-controls input {
    padding: 0.5rem 2.5rem 0.5rem 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 0.9rem;
    width: 200px;
}

.search-controls input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.search-controls .icon-btn {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    font-size: 1rem;
}

.stats-section {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* 关卡容器 */
.levels-container {
    flex: 1;
    padding: 1.5rem 2rem;
    overflow-y: auto;
}

.levels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
}

/* 关卡卡片 */
.level-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.level-card:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* 关卡卡片触摸反馈 */
.level-card.touch-active,
.level-card:active {
    transform: translateY(0px) scale(0.98);
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transition: all 0.1s ease;
}

.level-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.level-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: white;
    margin: 0;
    flex: 1;
    margin-right: 1rem;
}

.level-badges {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.difficulty-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.difficulty-easy {
    background: rgba(76, 175, 80, 0.8);
    color: white;
}

.difficulty-normal {
    background: rgba(33, 150, 243, 0.8);
    color: white;
}

.difficulty-hard {
    background: rgba(255, 152, 0, 0.8);
    color: white;
}

.difficulty-expert {
    background: rgba(244, 67, 54, 0.8);
    color: white;
}

.my-level-badge {
    background: rgba(156, 39, 176, 0.8);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.level-card-body {
    margin-bottom: 1rem;
}

.level-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.level-meta {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.meta-row {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
}

.meta-label {
    color: rgba(255, 255, 255, 0.7);
}

.meta-value {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.level-rating {
    margin-bottom: 1rem;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.rating-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.9rem;
}

.rating-icon {
    font-size: 1rem;
}

.rating-count {
    color: rgba(255, 255, 255, 0.8);
}

.rating-score {
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 8px;
    font-size: 0.85rem;
}

.rating-score.positive {
    background: rgba(76, 175, 80, 0.3);
    color: #4CAF50;
}

.rating-score.negative {
    background: rgba(244, 67, 54, 0.3);
    color: #F44336;
}

.level-card-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.level-action-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 70px;
}

.level-action-btn.primary {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
}

.level-action-btn.primary:hover {
    background: linear-gradient(45deg, #45a049, #3d8b40);
    transform: translateY(-1px);
}

.level-action-btn:not(.primary) {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.level-action-btn:not(.primary):hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

/* 关卡操作按钮触摸反馈 */
.level-action-btn.touch-active,
.level-action-btn:active {
    transform: scale(0.95);
    transition: all 0.1s ease;
}

.level-action-btn.primary.touch-active,
.level-action-btn.primary:active {
    background: linear-gradient(45deg, #3d8b40, #2e7d32);
}

.level-action-btn:not(.primary).touch-active,
.level-action-btn:not(.primary):active {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
}

/* 加载和空状态 */
.loading-state,
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
}

.empty-state p {
    font-size: 1rem;
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.6);
}

/* 关卡详情对话框 */
.level-detail-content {
    max-width: 800px;
    width: 90vw;
    max-height: 90vh;
    background: rgba(0, 0, 0, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    overflow: hidden;
    backdrop-filter: blur(15px);
}

.level-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.level-detail-header h3 {
    font-size: 1.5rem;
    color: white;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

.level-detail-body {
    padding: 2rem;
    max-height: 60vh;
    overflow-y: auto;
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
}

.level-info-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.level-meta {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.level-meta .meta-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.level-meta .meta-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.level-meta .meta-value {
    color: white;
    font-weight: 500;
}

.level-description h4 {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
}

.level-description p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
    font-size: 0.95rem;
}

.level-rating {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1rem;
}

.level-rating .rating-display {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 1rem;
}

.level-rating .rating-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.level-rating .rating-icon {
    font-size: 1.2rem;
}

.level-rating .rating-score {
    font-size: 1.1rem;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 8px;
}

.rating-actions {
    display: flex;
    gap: 1rem;
}

.rating-btn {
    padding: 0.5rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rating-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

.rating-btn.active {
    background: rgba(76, 175, 80, 0.3);
    border-color: #4CAF50;
    color: #4CAF50;
}

.rating-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.level-preview-section {
    display: flex;
    flex-direction: column;
}

.level-preview-section h4 {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.level-preview-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

#level-preview-canvas {
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    background: #1a1a2e;
}

.level-detail-actions {
    display: flex;
    gap: 1rem;
    padding: 1.5rem 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.level-detail-actions .dialog-btn {
    flex: 1;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.level-detail-actions .dialog-btn.primary {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
}

.level-detail-actions .dialog-btn.primary:hover {
    background: linear-gradient(45deg, #45a049, #3d8b40);
    transform: translateY(-1px);
}

.level-detail-actions .dialog-btn.danger {
    background: linear-gradient(45deg, #F44336, #d32f2f);
    color: white;
}

.level-detail-actions .dialog-btn.danger:hover {
    background: linear-gradient(45deg, #d32f2f, #b71c1c);
    transform: translateY(-1px);
}

.level-detail-actions .dialog-btn:not(.primary):not(.danger) {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.level-detail-actions .dialog-btn:not(.primary):not(.danger):hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}
