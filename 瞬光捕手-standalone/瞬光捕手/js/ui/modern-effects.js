/**
 * 瞬光捕手 - 现代化视觉效果控制器
 * 负责管理粒子系统、动画效果和现代化UI交互
 */

class ModernEffects {
    constructor() {
        this.particleSystem = null;
        this.particles = [];
        this.maxParticles = 50;
        this.animationId = null;
        this.isInitialized = false;
        
        console.log('🎨 现代化效果系统初始化');
    }

    /**
     * 初始化现代化效果系统
     */
    init() {
        if (this.isInitialized) return;
        
        this.particleSystem = document.getElementById('particle-system');
        if (!this.particleSystem) {
            console.warn('⚠️ 粒子系统容器未找到');
            return;
        }

        this.createParticles();
        this.startAnimation();
        this.bindEvents();
        this.isInitialized = true;
        
        console.log('✨ 现代化效果系统启动成功');
    }

    /**
     * 创建背景粒子
     */
    createParticles() {
        for (let i = 0; i < this.maxParticles; i++) {
            this.createParticle();
        }
    }

    /**
     * 创建单个粒子
     */
    createParticle() {
        const particle = document.createElement('div');
        particle.className = 'floating-particle';
        
        // 随机位置和延迟
        const x = Math.random() * window.innerWidth;
        const delay = Math.random() * 8;
        const duration = 8 + Math.random() * 4;
        
        particle.style.left = x + 'px';
        particle.style.animationDelay = delay + 's';
        particle.style.animationDuration = duration + 's';
        
        this.particleSystem.appendChild(particle);
        this.particles.push(particle);
        
        // 粒子动画结束后重新创建
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
                const index = this.particles.indexOf(particle);
                if (index > -1) {
                    this.particles.splice(index, 1);
                }
                // 创建新粒子保持数量
                if (this.particles.length < this.maxParticles) {
                    this.createParticle();
                }
            }
        }, (delay + duration) * 1000);
    }

    /**
     * 开始动画循环
     */
    startAnimation() {
        const animate = () => {
            // 这里可以添加其他需要持续更新的动画效果
            this.animationId = requestAnimationFrame(animate);
        };
        animate();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 按钮点击特效
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('menu-btn')) {
                this.createClickEffect(e.target, e.clientX, e.clientY);
            }
        });

        // 窗口大小改变时重新调整粒子
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    /**
     * 创建点击特效
     */
    createClickEffect(element, x, y) {
        const effect = document.createElement('div');
        effect.className = 'click-ripple-effect';
        effect.style.position = 'fixed';
        effect.style.left = x + 'px';
        effect.style.top = y + 'px';
        effect.style.width = '0px';
        effect.style.height = '0px';
        effect.style.borderRadius = '50%';
        effect.style.background = 'radial-gradient(circle, rgba(0, 212, 255, 0.6) 0%, transparent 70%)';
        effect.style.pointerEvents = 'none';
        effect.style.zIndex = '9999';
        effect.style.transform = 'translate(-50%, -50%)';
        
        document.body.appendChild(effect);
        
        // 动画效果
        effect.animate([
            { width: '0px', height: '0px', opacity: 1 },
            { width: '100px', height: '100px', opacity: 0 }
        ], {
            duration: 600,
            easing: 'ease-out'
        }).onfinish = () => {
            if (effect.parentNode) {
                effect.parentNode.removeChild(effect);
            }
        };
    }

    /**
     * 创建光点击中特效
     */
    createSparkHitEffect(x, y, color = '#00d4ff') {
        const effect = document.createElement('div');
        effect.className = 'spark-hit-effect';
        effect.style.position = 'absolute';
        effect.style.left = x + 'px';
        effect.style.top = y + 'px';
        effect.style.width = '20px';
        effect.style.height = '20px';
        effect.style.pointerEvents = 'none';
        effect.style.zIndex = '1000';
        
        // 创建多个粒子爆炸效果
        for (let i = 0; i < 8; i++) {
            const particle = document.createElement('div');
            particle.style.position = 'absolute';
            particle.style.width = '4px';
            particle.style.height = '4px';
            particle.style.background = color;
            particle.style.borderRadius = '50%';
            particle.style.left = '50%';
            particle.style.top = '50%';
            particle.style.transform = 'translate(-50%, -50%)';
            
            const angle = (i / 8) * Math.PI * 2;
            const distance = 50 + Math.random() * 30;
            const endX = Math.cos(angle) * distance;
            const endY = Math.sin(angle) * distance;
            
            particle.animate([
                { 
                    transform: 'translate(-50%, -50%) scale(1)',
                    opacity: 1
                },
                { 
                    transform: `translate(calc(-50% + ${endX}px), calc(-50% + ${endY}px)) scale(0)`,
                    opacity: 0
                }
            ], {
                duration: 800,
                easing: 'ease-out'
            });
            
            effect.appendChild(particle);
        }
        
        // 添加到游戏画布容器
        const canvasContainer = document.getElementById('game-canvas-container');
        if (canvasContainer) {
            canvasContainer.appendChild(effect);
            
            setTimeout(() => {
                if (effect.parentNode) {
                    effect.parentNode.removeChild(effect);
                }
            }, 1000);
        }
    }

    /**
     * 创建连击特效
     */
    createComboEffect(combo, x, y) {
        const effect = document.createElement('div');
        effect.className = 'combo-effect';
        effect.textContent = `${combo}x COMBO!`;
        effect.style.left = x + 'px';
        effect.style.top = y + 'px';
        
        const canvasContainer = document.getElementById('game-canvas-container');
        if (canvasContainer) {
            canvasContainer.appendChild(effect);
            
            setTimeout(() => {
                if (effect.parentNode) {
                    effect.parentNode.removeChild(effect);
                }
            }, 1000);
        }
    }

    /**
     * 处理窗口大小改变
     */
    handleResize() {
        // 清理现有粒子
        this.particles.forEach(particle => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        });
        this.particles = [];
        
        // 重新创建粒子
        setTimeout(() => {
            this.createParticles();
        }, 100);
    }

    /**
     * 添加脉冲效果到元素
     */
    addPulseEffect(element) {
        element.classList.add('pulse-effect');
    }

    /**
     * 移除脉冲效果
     */
    removePulseEffect(element) {
        element.classList.remove('pulse-effect');
    }

    /**
     * 销毁效果系统
     */
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        this.particles.forEach(particle => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        });
        
        this.particles = [];
        this.isInitialized = false;
        
        console.log('🎨 现代化效果系统已销毁');
    }
}

// 创建全局实例
window.modernEffects = new ModernEffects();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.modernEffects.init();
});

console.log('🎨 现代化效果模块加载完成');
