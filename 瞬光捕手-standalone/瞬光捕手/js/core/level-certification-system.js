/**
 * 关卡认证系统
 * 负责自定义关卡的质量评估和认证管理
 */
class LevelCertificationSystem {
    constructor() {
        this.initialized = false;
        
        // 认证标准配置
        this.certificationCriteria = {
            // 基础要求
            basic: {
                minDifficulty: 0.4,          // 最低难度要求
                minDuration: 15000,          // 最短持续时间(15秒)
                minSparks: 8,                // 最少光点数量
                maxSparks: 80,               // 最多光点数量
                minTargetScore: 500,         // 最低目标分数
                maxTargetScore: 20000        // 最高目标分数
            },
            
            // 质量评估权重
            qualityWeights: {
                difficulty: 0.25,           // 难度合理性
                balance: 0.20,              // 游戏平衡性
                creativity: 0.20,           // 创意性
                playability: 0.15,          // 可玩性
                technical: 0.10,            // 技术质量
                aesthetics: 0.10            // 美观性
            },
            
            // 认证等级
            certificationLevels: {
                bronze: { threshold: 0.6, name: '铜牌认证', benefits: ['参与排行榜'] },
                silver: { threshold: 0.75, name: '银牌认证', benefits: ['参与排行榜', '推荐展示'] },
                gold: { threshold: 0.9, name: '金牌认证', benefits: ['参与排行榜', '推荐展示', '精选关卡'] }
            }
        };
        
        // 认证记录
        this.certificationRecords = new Map();
        
        // 评估缓存
        this.evaluationCache = new Map();
        
        console.log('🏅 关卡认证系统已创建');
    }

    /**
     * 初始化认证系统
     */
    async initialize() {
        try {
            // 加载认证记录
            await this.loadCertificationRecords();
            
            this.initialized = true;
            console.log('🏅 关卡认证系统初始化完成');
            
        } catch (error) {
            console.error('❌ 关卡认证系统初始化失败:', error);
            throw error;
        }
    }

    /**
     * 评估关卡质量
     * @param {object} levelData - 关卡数据
     * @returns {object} 评估结果
     */
    async evaluateLevel(levelData) {
        try {
            // 检查缓存
            const cacheKey = this.generateCacheKey(levelData);
            if (this.evaluationCache.has(cacheKey)) {
                return this.evaluationCache.get(cacheKey);
            }

            const evaluation = {
                levelId: levelData.id,
                timestamp: Date.now(),
                scores: {},
                overallScore: 0,
                issues: [],
                recommendations: [],
                certificationEligible: false,
                suggestedLevel: null
            };

            // 1. 基础要求检查
            const basicCheck = this.checkBasicRequirements(levelData);
            if (!basicCheck.passed) {
                evaluation.issues.push(...basicCheck.issues);
                evaluation.recommendations.push(...basicCheck.recommendations);
                this.evaluationCache.set(cacheKey, evaluation);
                return evaluation;
            }

            // 2. 难度评估
            evaluation.scores.difficulty = this.evaluateDifficulty(levelData);
            
            // 3. 平衡性评估
            evaluation.scores.balance = this.evaluateBalance(levelData);
            
            // 4. 创意性评估
            evaluation.scores.creativity = this.evaluateCreativity(levelData);
            
            // 5. 可玩性评估
            evaluation.scores.playability = this.evaluatePlayability(levelData);
            
            // 6. 技术质量评估
            evaluation.scores.technical = this.evaluateTechnicalQuality(levelData);
            
            // 7. 美观性评估
            evaluation.scores.aesthetics = this.evaluateAesthetics(levelData);

            // 计算总分
            evaluation.overallScore = this.calculateOverallScore(evaluation.scores);
            
            // 确定认证等级
            evaluation.suggestedLevel = this.determineCertificationLevel(evaluation.overallScore);
            evaluation.certificationEligible = evaluation.suggestedLevel !== null;

            // 生成建议
            this.generateRecommendations(evaluation, levelData);

            // 缓存结果
            this.evaluationCache.set(cacheKey, evaluation);
            
            console.log(`📊 关卡评估完成: ${levelData.name}, 总分: ${evaluation.overallScore.toFixed(2)}`);
            return evaluation;

        } catch (error) {
            console.error('❌ 关卡评估失败:', error);
            throw error;
        }
    }

    /**
     * 申请关卡认证
     * @param {string} levelId - 关卡ID
     * @param {object} levelData - 关卡数据
     * @returns {object} 认证结果
     */
    async applyCertification(levelId, levelData) {
        try {
            // 检查是否已经认证
            if (this.certificationRecords.has(levelId)) {
                const existing = this.certificationRecords.get(levelId);
                if (existing.status === 'certified') {
                    return { success: false, reason: 'already_certified', certification: existing };
                }
            }

            // 评估关卡
            const evaluation = await this.evaluateLevel(levelData);
            
            if (!evaluation.certificationEligible) {
                return {
                    success: false,
                    reason: 'not_eligible',
                    evaluation: evaluation
                };
            }

            // 创建认证记录
            const certification = {
                levelId: levelId,
                levelName: levelData.name,
                authorId: levelData.authorId,
                authorName: levelData.author,
                appliedAt: Date.now(),
                evaluatedAt: evaluation.timestamp,
                status: 'pending', // pending, certified, rejected
                level: evaluation.suggestedLevel,
                score: evaluation.overallScore,
                evaluation: evaluation,
                reviewedBy: null,
                reviewedAt: null,
                validUntil: Date.now() + (365 * 24 * 60 * 60 * 1000) // 1年有效期
            };

            // 自动认证（如果分数足够高）
            if (evaluation.overallScore >= 0.8) {
                certification.status = 'certified';
                certification.reviewedBy = 'system';
                certification.reviewedAt = Date.now();
            }

            // 保存认证记录
            this.certificationRecords.set(levelId, certification);
            await this.saveCertificationRecords();

            console.log(`🏅 关卡认证申请: ${levelData.name}, 状态: ${certification.status}`);
            return { success: true, certification: certification };

        } catch (error) {
            console.error('❌ 关卡认证申请失败:', error);
            return { success: false, reason: 'application_error', error: error.message };
        }
    }

    /**
     * 检查基础要求
     */
    checkBasicRequirements(levelData) {
        const result = { passed: true, issues: [], recommendations: [] };
        const criteria = this.certificationCriteria.basic;
        const data = levelData.data || {};
        const objects = levelData.objects || [];

        // 检查关卡名称
        if (!levelData.name || levelData.name.trim().length < 3) {
            result.passed = false;
            result.issues.push('关卡名称过短（至少3个字符）');
        }

        // 检查关卡描述
        if (!levelData.description || levelData.description.trim().length < 10) {
            result.recommendations.push('建议添加详细的关卡描述');
        }

        // 检查持续时间
        const duration = data.duration || 30000;
        if (duration < criteria.minDuration) {
            result.passed = false;
            result.issues.push(`关卡时间过短（至少${criteria.minDuration/1000}秒）`);
        }

        // 检查光点数量
        const sparkCount = objects.filter(obj => obj.type === 'spark').length;
        if (sparkCount < criteria.minSparks) {
            result.passed = false;
            result.issues.push(`光点数量过少（至少${criteria.minSparks}个）`);
        }
        if (sparkCount > criteria.maxSparks) {
            result.passed = false;
            result.issues.push(`光点数量过多（最多${criteria.maxSparks}个）`);
        }

        // 检查目标分数
        const targetScore = data.targetScore || 1000;
        if (targetScore < criteria.minTargetScore) {
            result.passed = false;
            result.issues.push(`目标分数过低（至少${criteria.minTargetScore}分）`);
        }
        if (targetScore > criteria.maxTargetScore) {
            result.passed = false;
            result.issues.push(`目标分数过高（最多${criteria.maxTargetScore}分）`);
        }

        // 检查难度
        if (window.antiCheatSystem) {
            const difficulty = antiCheatSystem.calculateLevelDifficulty(levelData);
            if (difficulty < criteria.minDifficulty) {
                result.passed = false;
                result.issues.push(`关卡难度过低（至少${criteria.minDifficulty}）`);
            }
        }

        return result;
    }

    /**
     * 评估难度合理性
     */
    evaluateDifficulty(levelData) {
        if (!window.antiCheatSystem) return 0.5;
        
        const difficulty = antiCheatSystem.calculateLevelDifficulty(levelData);
        
        // 理想难度范围是0.4-0.8
        if (difficulty >= 0.4 && difficulty <= 0.8) {
            return 1.0; // 完美分数
        } else if (difficulty >= 0.3 && difficulty <= 0.9) {
            return 0.8; // 良好分数
        } else if (difficulty >= 0.2 && difficulty <= 1.0) {
            return 0.6; // 可接受分数
        } else {
            return 0.3; // 较差分数
        }
    }

    /**
     * 评估游戏平衡性
     */
    evaluateBalance(levelData) {
        let balanceScore = 0;
        const data = levelData.data || {};
        const objects = levelData.objects || [];
        
        // 时间与目标分数的平衡
        const duration = data.duration || 30000;
        const targetScore = data.targetScore || 1000;
        const scorePerSecond = targetScore / (duration / 1000);
        
        if (scorePerSecond >= 30 && scorePerSecond <= 150) {
            balanceScore += 0.4; // 合理的分数获取速度
        } else {
            balanceScore += 0.2;
        }
        
        // 光点分布平衡
        const sparks = objects.filter(obj => obj.type === 'spark');
        if (sparks.length > 0) {
            const distributionScore = this.calculateSparkDistribution(sparks);
            balanceScore += distributionScore * 0.3;
        }
        
        // 难度曲线
        balanceScore += 0.3; // 暂时给固定分数，后续可以根据光点时序分析
        
        return Math.min(balanceScore, 1.0);
    }

    /**
     * 评估创意性
     */
    evaluateCreativity(levelData) {
        let creativityScore = 0.5; // 基础分数
        const data = levelData.data || {};
        const objects = levelData.objects || [];
        
        // 特殊效果加分
        if (data.specialEffects && data.specialEffects.length > 0) {
            creativityScore += 0.2;
        }
        
        // 独特的光点排列
        const sparks = objects.filter(obj => obj.type === 'spark');
        if (sparks.length > 0) {
            const uniquePatterns = this.detectUniquePatterns(sparks);
            creativityScore += uniquePatterns * 0.2;
        }
        
        // 创新的游戏机制
        if (data.movingSparks || data.colorChangingSparks) {
            creativityScore += 0.1;
        }
        
        return Math.min(creativityScore, 1.0);
    }

    /**
     * 评估可玩性
     */
    evaluatePlayability(levelData) {
        let playabilityScore = 0.6; // 基础分数
        const data = levelData.data || {};
        
        // 合理的游戏时长
        const duration = data.duration || 30000;
        if (duration >= 20000 && duration <= 180000) { // 20秒到3分钟
            playabilityScore += 0.2;
        }
        
        // 适中的挑战性
        if (window.antiCheatSystem) {
            const difficulty = antiCheatSystem.calculateLevelDifficulty(levelData);
            if (difficulty >= 0.3 && difficulty <= 0.8) {
                playabilityScore += 0.2;
            }
        }
        
        return Math.min(playabilityScore, 1.0);
    }

    /**
     * 评估技术质量
     */
    evaluateTechnicalQuality(levelData) {
        let technicalScore = 0.7; // 基础分数
        
        // 数据完整性
        if (levelData.name && levelData.description && levelData.data) {
            technicalScore += 0.1;
        }
        
        // 配置合理性
        const data = levelData.data || {};
        if (data.duration && data.targetScore && data.sparkSpawnRate) {
            technicalScore += 0.1;
        }
        
        // 对象定义完整性
        const objects = levelData.objects || [];
        const validObjects = objects.filter(obj => 
            obj.type && obj.x !== undefined && obj.y !== undefined
        );
        if (validObjects.length === objects.length && objects.length > 0) {
            technicalScore += 0.1;
        }
        
        return Math.min(technicalScore, 1.0);
    }

    /**
     * 评估美观性
     */
    evaluateAesthetics(levelData) {
        let aestheticsScore = 0.6; // 基础分数
        const objects = levelData.objects || [];
        
        // 光点分布美观性
        const sparks = objects.filter(obj => obj.type === 'spark');
        if (sparks.length > 0) {
            const distributionScore = this.calculateSparkDistribution(sparks);
            aestheticsScore += distributionScore * 0.2;
        }
        
        // 颜色搭配（如果有的话）
        const hasColorVariety = sparks.some(spark => spark.color);
        if (hasColorVariety) {
            aestheticsScore += 0.2;
        }
        
        return Math.min(aestheticsScore, 1.0);
    }

    /**
     * 计算总分
     */
    calculateOverallScore(scores) {
        const weights = this.certificationCriteria.qualityWeights;
        let totalScore = 0;
        
        for (const [category, score] of Object.entries(scores)) {
            if (weights[category]) {
                totalScore += score * weights[category];
            }
        }
        
        return totalScore;
    }

    /**
     * 确定认证等级
     */
    determineCertificationLevel(score) {
        const levels = this.certificationCriteria.certificationLevels;
        
        if (score >= levels.gold.threshold) {
            return 'gold';
        } else if (score >= levels.silver.threshold) {
            return 'silver';
        } else if (score >= levels.bronze.threshold) {
            return 'bronze';
        } else {
            return null; // 不符合认证要求
        }
    }

    /**
     * 生成改进建议
     */
    generateRecommendations(evaluation, levelData) {
        const scores = evaluation.scores;
        
        if (scores.difficulty < 0.6) {
            evaluation.recommendations.push('建议调整关卡难度，增加挑战性');
        }
        
        if (scores.balance < 0.6) {
            evaluation.recommendations.push('建议优化游戏平衡性，调整时间和分数比例');
        }
        
        if (scores.creativity < 0.6) {
            evaluation.recommendations.push('建议增加创意元素，如特殊效果或独特排列');
        }
        
        if (scores.playability < 0.6) {
            evaluation.recommendations.push('建议改善可玩性，确保游戏体验流畅');
        }
        
        if (scores.technical < 0.8) {
            evaluation.recommendations.push('建议完善技术细节，确保数据完整性');
        }
        
        if (scores.aesthetics < 0.6) {
            evaluation.recommendations.push('建议改善视觉效果，优化光点分布和颜色搭配');
        }
    }

    // 辅助方法
    generateCacheKey(levelData) {
        return levelData.id || JSON.stringify(levelData);
    }

    calculateSparkDistribution(sparks) {
        // 简化的分布计算，实际实现会更复杂
        return Math.random() * 0.5 + 0.5; // 临时实现
    }

    detectUniquePatterns(sparks) {
        // 简化的模式检测，实际实现会更复杂
        return Math.random() * 0.5; // 临时实现
    }

    async loadCertificationRecords() {
        try {
            if (!storageService) return;
            
            const records = await storageService.get('level.certification.records');
            if (records) {
                this.certificationRecords = new Map(Object.entries(records));
            }
        } catch (error) {
            console.error('加载认证记录失败:', error);
        }
    }

    async saveCertificationRecords() {
        try {
            if (!storageService) return;
            
            const records = Object.fromEntries(this.certificationRecords);
            await storageService.put('level.certification.records', records);
        } catch (error) {
            console.error('保存认证记录失败:', error);
        }
    }
}

// 创建全局关卡认证系统实例
window.levelCertificationSystem = new LevelCertificationSystem();
