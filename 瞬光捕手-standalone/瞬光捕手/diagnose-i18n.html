<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>i18n 问题诊断</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { background: #f8f9fa; padding: 10px; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
        button { background: #007bff; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; margin: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>🔍 i18n 问题诊断工具</h1>
    
    <div class="step">
        <h3>步骤 1: 检查脚本加载</h3>
        <button onclick="checkScripts()">检查脚本</button>
        <div id="scripts-result" class="result"></div>
    </div>

    <div class="step">
        <h3>步骤 2: 检查服务实例</h3>
        <button onclick="checkServices()">检查服务</button>
        <div id="services-result" class="result"></div>
    </div>

    <div class="step">
        <h3>步骤 3: 检查翻译数据</h3>
        <button onclick="checkTranslations()">检查翻译数据</button>
        <div id="translations-result" class="result"></div>
    </div>

    <div class="step">
        <h3>步骤 4: 测试语言切换</h3>
        <button onclick="testLanguageSwitch()">测试切换</button>
        <div id="switch-result" class="result"></div>
    </div>

    <div class="step">
        <h3>步骤 5: 实时测试</h3>
        <div>当前显示: <span id="live-test" data-i18n="game.title">瞬光捕手</span></div>
        <button onclick="switchAndUpdate('en-US')">切换到英文</button>
        <button onclick="switchAndUpdate('zh-CN')">切换到中文</button>
    </div>

    <!-- 加载脚本 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>

    <script>
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const className = type === 'error' ? 'error' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : '';
            element.innerHTML += `<span class="${className}">${message}</span>\n`;
        }

        function clear(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        function checkScripts() {
            clear('scripts-result');
            log('scripts-result', '=== 脚本加载检查 ===');
            
            // 检查脚本标签
            const scripts = document.querySelectorAll('script[src]');
            log('scripts-result', `找到 ${scripts.length} 个外部脚本:`);
            scripts.forEach(script => {
                log('scripts-result', `  - ${script.src}`);
            });
            
            // 检查全局对象
            log('scripts-result', '\n=== 全局对象检查 ===');
            log('scripts-result', `window.StorageService: ${typeof window.StorageService}`, 
                typeof window.StorageService === 'function' ? 'success' : 'error');
            log('scripts-result', `window.I18nService: ${typeof window.I18nService}`, 
                typeof window.I18nService === 'function' ? 'success' : 'error');
            log('scripts-result', `window.storageService: ${typeof window.storageService}`, 
                typeof window.storageService === 'object' ? 'success' : 'error');
            log('scripts-result', `window.i18nService: ${typeof window.i18nService}`, 
                typeof window.i18nService === 'object' ? 'success' : 'error');
        }

        function checkServices() {
            clear('services-result');
            log('services-result', '=== 服务状态检查 ===');
            
            if (window.storageService) {
                log('services-result', `storageService.initialized: ${window.storageService.initialized}`, 
                    window.storageService.initialized ? 'success' : 'warning');
                log('services-result', `storageService.adapter: ${window.storageService.adapter ? window.storageService.adapter.constructor.name : 'null'}`);
            } else {
                log('services-result', 'storageService 不存在', 'error');
            }
            
            if (window.i18nService) {
                log('services-result', `i18nService.initialized: ${window.i18nService.initialized}`, 
                    window.i18nService.initialized ? 'success' : 'warning');
                log('services-result', `i18nService.currentLanguage: ${window.i18nService.currentLanguage}`);
                log('services-result', `i18nService.translations: ${typeof window.i18nService.translations}`);
            } else {
                log('services-result', 'i18nService 不存在', 'error');
            }
        }

        function checkTranslations() {
            clear('translations-result');
            log('translations-result', '=== 翻译数据检查 ===');
            
            if (!window.i18nService) {
                log('translations-result', 'i18nService 不存在', 'error');
                return;
            }
            
            const translations = window.i18nService.translations;
            log('translations-result', `翻译对象类型: ${typeof translations}`);
            
            if (translations) {
                const languages = Object.keys(translations);
                log('translations-result', `支持的语言: ${languages.join(', ')}`);
                
                languages.forEach(lang => {
                    const count = Object.keys(translations[lang] || {}).length;
                    log('translations-result', `${lang}: ${count} 个翻译键`, count > 0 ? 'success' : 'error');
                });
                
                // 测试几个关键翻译
                log('translations-result', '\n=== 关键翻译测试 ===');
                const testKeys = ['game.title', 'menu.start', 'settings.title'];
                testKeys.forEach(key => {
                    const zhTranslation = translations['zh-CN']?.[key];
                    const enTranslation = translations['en-US']?.[key];
                    log('translations-result', `${key}:`);
                    log('translations-result', `  zh-CN: "${zhTranslation}"`, zhTranslation ? 'success' : 'error');
                    log('translations-result', `  en-US: "${enTranslation}"`, enTranslation ? 'success' : 'error');
                });
            }
        }

        async function testLanguageSwitch() {
            clear('switch-result');
            log('switch-result', '=== 语言切换测试 ===');
            
            if (!window.i18nService) {
                log('switch-result', 'i18nService 不存在', 'error');
                return;
            }
            
            const originalLang = window.i18nService.getCurrentLanguage();
            log('switch-result', `原始语言: ${originalLang}`);
            
            // 测试切换到英文
            log('switch-result', '\n--- 切换到英文 ---');
            try {
                await window.i18nService.setLanguage('en-US');
                const newLang = window.i18nService.getCurrentLanguage();
                log('switch-result', `切换后语言: ${newLang}`, newLang === 'en-US' ? 'success' : 'error');
                
                const titleEn = window.i18nService.t('game.title');
                log('switch-result', `game.title: "${titleEn}"`, titleEn === 'Split-Second Spark' ? 'success' : 'error');
                
            } catch (error) {
                log('switch-result', `切换失败: ${error.message}`, 'error');
            }
            
            // 测试切换回中文
            log('switch-result', '\n--- 切换回中文 ---');
            try {
                await window.i18nService.setLanguage('zh-CN');
                const newLang = window.i18nService.getCurrentLanguage();
                log('switch-result', `切换后语言: ${newLang}`, newLang === 'zh-CN' ? 'success' : 'error');
                
                const titleZh = window.i18nService.t('game.title');
                log('switch-result', `game.title: "${titleZh}"`, titleZh === '瞬光捕手' ? 'success' : 'error');
                
            } catch (error) {
                log('switch-result', `切换失败: ${error.message}`, 'error');
            }
        }

        async function switchAndUpdate(language) {
            if (!window.i18nService) {
                alert('i18nService 不存在');
                return;
            }
            
            try {
                await window.i18nService.setLanguage(language);
                window.i18nService.applyTranslations();
                
                const currentLang = window.i18nService.getCurrentLanguage();
                const currentText = document.getElementById('live-test').textContent;
                console.log(`语言切换到: ${currentLang}, 显示文本: "${currentText}"`);
                
            } catch (error) {
                console.error('切换失败:', error);
                alert(`切换失败: ${error.message}`);
            }
        }

        // 页面加载完成后自动运行诊断
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                console.log('开始自动诊断...');
                checkScripts();
                setTimeout(() => checkServices(), 500);
                setTimeout(() => checkTranslations(), 1000);
            }, 1000);
        });
    </script>
</body>
</html>
