/**
 * 防作弊系统
 * 负责分数验证、异常检测、关卡难度评估等防作弊功能
 */
class AntiCheatSystem {
    constructor() {
        this.initialized = false;
        
        // 配置参数
        this.config = {
            // 分数验证配置
            scoreValidation: {
                maxScorePerSecond: 1000,        // 每秒最大分数
                maxScorePerHit: 500,            // 单次击中最大分数
                minGameDuration: 5000,          // 最小游戏时长(毫秒)
                maxAccuracy: 100,               // 最大准确率
                suspiciousAccuracy: 98          // 可疑准确率阈值
            },
            
            // 关卡难度评估配置
            levelDifficulty: {
                minSparks: 5,                   // 最少光点数量
                minDuration: 10000,             // 最短持续时间
                maxTargetScore: 50000,          // 最大目标分数
                sparkDensityThreshold: 0.1,     // 光点密度阈值
                difficultyWeights: {            // 难度权重
                    sparkCount: 0.3,
                    duration: 0.2,
                    targetScore: 0.2,
                    sparkSpeed: 0.15,
                    sparkSize: 0.15
                }
            },
            
            // 异常检测配置
            anomalyDetection: {
                maxConsecutivePerfects: 50,     // 最大连续完美击中
                suspiciousComboLength: 100,     // 可疑连击长度
                rapidSubmissionThreshold: 3,    // 快速提交阈值(次/分钟)
                identicalScoreThreshold: 5      // 相同分数提交阈值
            }
        };
        
        // 玩家行为记录
        this.playerBehavior = new Map();
        
        // 关卡难度缓存
        this.levelDifficultyCache = new Map();
        
        console.log('🛡️ 防作弊系统已创建');
    }

    /**
     * 初始化防作弊系统
     */
    async initialize() {
        try {
            // 加载历史行为数据
            await this.loadPlayerBehaviorData();
            
            // 初始化关卡难度缓存
            await this.initializeLevelDifficultyCache();
            
            this.initialized = true;
            console.log('🛡️ 防作弊系统初始化完成');
            
        } catch (error) {
            console.error('❌ 防作弊系统初始化失败:', error);
            throw error;
        }
    }

    /**
     * 验证分数提交
     * @param {object} scoreData - 分数数据
     * @param {object} gameData - 游戏数据
     * @param {object} levelData - 关卡数据
     * @returns {object} 验证结果
     */
    validateScoreSubmission(scoreData, gameData, levelData) {
        const validationResult = {
            isValid: true,
            warnings: [],
            errors: [],
            riskLevel: 'low', // low, medium, high, critical
            details: {}
        };

        try {
            // 1. 基础数据验证
            this.validateBasicData(scoreData, gameData, validationResult);
            
            // 2. 分数合理性检查
            this.validateScoreReasonableness(scoreData, gameData, validationResult);
            
            // 3. 时间关联验证
            this.validateTimeConsistency(scoreData, gameData, validationResult);
            
            // 4. 关卡难度验证
            if (levelData) {
                this.validateLevelDifficulty(scoreData, levelData, validationResult);
            }
            
            // 5. 玩家行为分析
            this.analyzePlayerBehavior(scoreData, gameData, validationResult);
            
            // 6. 异常模式检测
            this.detectAnomalousPatterns(scoreData, gameData, validationResult);
            
            // 根据错误和警告确定最终验证结果
            if (validationResult.errors.length > 0) {
                validationResult.isValid = false;
                validationResult.riskLevel = 'critical';
            } else if (validationResult.warnings.length > 2) {
                validationResult.riskLevel = 'high';
            } else if (validationResult.warnings.length > 0) {
                validationResult.riskLevel = 'medium';
            }
            
            console.log(`🛡️ 分数验证完成 - 风险等级: ${validationResult.riskLevel}`);
            return validationResult;
            
        } catch (error) {
            console.error('❌ 分数验证失败:', error);
            validationResult.isValid = false;
            validationResult.errors.push('验证过程发生错误');
            validationResult.riskLevel = 'critical';
            return validationResult;
        }
    }

    /**
     * 基础数据验证
     */
    validateBasicData(scoreData, gameData, result) {
        // 检查必要字段
        const requiredFields = ['score', 'duration', 'totalHits'];
        for (const field of requiredFields) {
            if (scoreData[field] === undefined || scoreData[field] === null) {
                result.errors.push(`缺少必要字段: ${field}`);
            }
        }
        
        // 检查数据类型和范围
        if (typeof scoreData.score !== 'number' || scoreData.score < 0) {
            result.errors.push('分数数据无效');
        }
        
        if (typeof scoreData.duration !== 'number' || scoreData.duration < 0) {
            result.errors.push('游戏时长数据无效');
        }
        
        if (scoreData.accuracy !== undefined && (scoreData.accuracy < 0 || scoreData.accuracy > 100)) {
            result.errors.push('准确率数据无效');
        }
    }

    /**
     * 分数合理性检查
     */
    validateScoreReasonableness(scoreData, gameData, result) {
        const { score, duration, totalHits, perfectHits, accuracy } = scoreData;
        const config = this.config.scoreValidation;
        
        // 检查每秒分数
        const scorePerSecond = score / (duration / 1000);
        if (scorePerSecond > config.maxScorePerSecond) {
            result.errors.push(`每秒分数过高: ${scorePerSecond.toFixed(2)}`);
        }
        
        // 检查单次击中平均分数
        if (totalHits > 0) {
            const avgScorePerHit = score / totalHits;
            if (avgScorePerHit > config.maxScorePerHit) {
                result.warnings.push(`单次击中平均分数较高: ${avgScorePerHit.toFixed(2)}`);
            }
        }
        
        // 检查游戏时长
        if (duration < config.minGameDuration) {
            result.errors.push(`游戏时长过短: ${duration}ms`);
        }
        
        // 检查准确率
        if (accuracy !== undefined) {
            if (accuracy > config.suspiciousAccuracy) {
                result.warnings.push(`准确率异常高: ${accuracy}%`);
            }
            
            // 检查完美击中比例
            if (perfectHits !== undefined && totalHits > 0) {
                const perfectRatio = (perfectHits / totalHits) * 100;
                if (perfectRatio > 90) {
                    result.warnings.push(`完美击中比例过高: ${perfectRatio.toFixed(1)}%`);
                }
            }
        }
    }

    /**
     * 时间关联验证
     */
    validateTimeConsistency(scoreData, gameData, result) {
        const { duration, totalHits, combo } = scoreData;
        
        // 检查击中频率
        if (totalHits > 0) {
            const hitsPerSecond = totalHits / (duration / 1000);
            if (hitsPerSecond > 20) { // 每秒超过20次击中可能异常
                result.warnings.push(`击中频率过高: ${hitsPerSecond.toFixed(2)}/秒`);
            }
        }
        
        // 检查连击与时间的关系
        if (combo > 0 && duration > 0) {
            const minTimeForCombo = combo * 100; // 假设每次连击至少需要100ms
            if (duration < minTimeForCombo) {
                result.warnings.push(`连击时间不合理: ${combo}连击在${duration}ms内完成`);
            }
        }
    }

    /**
     * 关卡难度验证
     */
    validateLevelDifficulty(scoreData, levelData, result) {
        // 计算关卡难度
        const difficulty = this.calculateLevelDifficulty(levelData);
        
        // 根据难度判断分数合理性
        if (difficulty < 0.3) { // 过于简单的关卡
            result.warnings.push('关卡难度过低，可能影响分数公平性');
            result.details.levelDifficulty = difficulty;
        }
        
        // 检查目标分数与实际分数的关系
        if (levelData.data && levelData.data.targetScore) {
            const scoreRatio = scoreData.score / levelData.data.targetScore;
            if (scoreRatio > 5) { // 分数超过目标分数5倍
                result.warnings.push(`分数远超目标分数: ${scoreRatio.toFixed(2)}倍`);
            }
        }
    }

    /**
     * 计算关卡难度
     * @param {object} levelData - 关卡数据
     * @returns {number} 难度值 (0-1)
     */
    calculateLevelDifficulty(levelData) {
        if (!levelData || !levelData.data) return 0;

        // 从缓存中获取
        const cacheKey = levelData.id || JSON.stringify(levelData.data);
        if (this.levelDifficultyCache.has(cacheKey)) {
            return this.levelDifficultyCache.get(cacheKey);
        }

        const data = levelData.data;
        const objects = levelData.objects || [];
        const weights = this.config.levelDifficulty.difficultyWeights;
        let difficulty = 0;

        // 1. 光点数量因子
        const sparkCount = objects.filter(obj => obj.type === 'spark').length;
        const sparkFactor = Math.min(sparkCount / 50, 1); // 50个光点为满分
        difficulty += sparkFactor * weights.sparkCount;

        // 2. 持续时间因子
        const duration = data.duration || 30000;
        const durationFactor = Math.min(duration / 120000, 1); // 2分钟为满分
        difficulty += durationFactor * weights.duration;

        // 3. 目标分数因子
        const targetScore = data.targetScore || 1000;
        const scoreFactor = Math.min(targetScore / 10000, 1); // 10000分为满分
        difficulty += scoreFactor * weights.targetScore;

        // 4. 光点速度因子
        const sparkSpeed = data.sparkSpeed || 1;
        const speedFactor = Math.min(sparkSpeed / 2, 1); // 2倍速度为满分
        difficulty += speedFactor * weights.sparkSpeed;

        // 5. 光点大小因子（越小越难）
        const sparkSize = data.sparkSize || 20;
        const sizeFactor = Math.max(0, 1 - sparkSize / 40); // 40像素为最简单
        difficulty += sizeFactor * weights.sparkSize;

        // 6. 额外难度因子
        difficulty += this.calculateAdvancedDifficultyFactors(levelData, objects);

        // 确保难度值在0-1范围内
        difficulty = Math.max(0, Math.min(1, difficulty));

        // 缓存结果
        this.levelDifficultyCache.set(cacheKey, difficulty);

        return difficulty;
    }

    /**
     * 计算高级难度因子
     * @param {object} levelData - 关卡数据
     * @param {Array} objects - 关卡对象数组
     * @returns {number} 额外难度值
     */
    calculateAdvancedDifficultyFactors(levelData, objects) {
        let advancedDifficulty = 0;
        const data = levelData.data;

        // 光点密度因子
        const sparkDensity = this.calculateSparkDensity(objects);
        if (sparkDensity > 0.5) {
            advancedDifficulty += 0.1; // 高密度增加难度
        }

        // 光点分布均匀性
        const distributionScore = this.calculateSparkDistribution(objects);
        advancedDifficulty += distributionScore * 0.1;

        // 时间压力因子
        const timePressure = this.calculateTimePressure(data);
        advancedDifficulty += timePressure * 0.15;

        // 精确度要求
        const precisionRequirement = this.calculatePrecisionRequirement(data);
        advancedDifficulty += precisionRequirement * 0.1;

        // 特殊效果复杂度
        const effectComplexity = this.calculateEffectComplexity(data);
        advancedDifficulty += effectComplexity * 0.05;

        return Math.min(advancedDifficulty, 0.4); // 最多增加0.4的难度
    }

    /**
     * 计算光点密度
     * @param {Array} objects - 关卡对象数组
     * @returns {number} 密度值 (0-1)
     */
    calculateSparkDensity(objects) {
        const sparks = objects.filter(obj => obj.type === 'spark');
        if (sparks.length === 0) return 0;

        // 假设游戏区域为800x600
        const gameArea = 800 * 600;
        const sparkArea = sparks.length * Math.PI * Math.pow(20, 2); // 假设光点半径为20

        return Math.min(sparkArea / gameArea, 1);
    }

    /**
     * 计算光点分布均匀性
     * @param {Array} objects - 关卡对象数组
     * @returns {number} 分布分数 (0-1)
     */
    calculateSparkDistribution(objects) {
        const sparks = objects.filter(obj => obj.type === 'spark' && obj.x !== undefined && obj.y !== undefined);
        if (sparks.length < 2) return 0;

        // 计算光点间的平均距离
        let totalDistance = 0;
        let pairCount = 0;

        for (let i = 0; i < sparks.length; i++) {
            for (let j = i + 1; j < sparks.length; j++) {
                const dx = sparks[i].x - sparks[j].x;
                const dy = sparks[i].y - sparks[j].y;
                totalDistance += Math.sqrt(dx * dx + dy * dy);
                pairCount++;
            }
        }

        const avgDistance = totalDistance / pairCount;

        // 理想距离约为100像素，距离越接近理想值分布越好
        const idealDistance = 100;
        const distributionScore = 1 - Math.abs(avgDistance - idealDistance) / idealDistance;

        return Math.max(0, Math.min(1, distributionScore));
    }

    /**
     * 计算时间压力
     * @param {object} data - 关卡数据
     * @returns {number} 时间压力值 (0-1)
     */
    calculateTimePressure(data) {
        const duration = data.duration || 30000;
        const targetScore = data.targetScore || 1000;

        // 计算每秒需要获得的分数
        const scorePerSecond = targetScore / (duration / 1000);

        // 正常情况下每秒100分，超过则有时间压力
        const normalScorePerSecond = 100;
        const pressureFactor = Math.max(0, (scorePerSecond - normalScorePerSecond) / normalScorePerSecond);

        return Math.min(pressureFactor, 1);
    }

    /**
     * 计算精确度要求
     * @param {object} data - 关卡数据
     * @returns {number} 精确度要求值 (0-1)
     */
    calculatePrecisionRequirement(data) {
        let precisionScore = 0;

        // 完美击中窗口越小，精确度要求越高
        const perfectWindow = data.perfectWindow || 100;
        if (perfectWindow < 50) {
            precisionScore += 0.5;
        } else if (perfectWindow < 100) {
            precisionScore += 0.3;
        }

        // 光点大小越小，精确度要求越高
        const sparkSize = data.sparkSize || 20;
        if (sparkSize < 15) {
            precisionScore += 0.5;
        } else if (sparkSize < 20) {
            precisionScore += 0.3;
        }

        return Math.min(precisionScore, 1);
    }

    /**
     * 计算特殊效果复杂度
     * @param {object} data - 关卡数据
     * @returns {number} 效果复杂度值 (0-1)
     */
    calculateEffectComplexity(data) {
        let complexity = 0;

        if (data.specialEffects && Array.isArray(data.specialEffects)) {
            // 每种特殊效果增加复杂度
            complexity += data.specialEffects.length * 0.2;
        }

        // 检查是否有移动光点
        if (data.movingSparks) {
            complexity += 0.3;
        }

        // 检查是否有变色光点
        if (data.colorChangingSparks) {
            complexity += 0.2;
        }

        // 检查是否有大小变化
        if (data.sizingChangingSparks) {
            complexity += 0.2;
        }

        return Math.min(complexity, 1);
    }

    /**
     * 获取关卡难度等级描述
     * @param {number} difficulty - 难度值 (0-1)
     * @returns {object} 难度等级信息
     */
    getDifficultyLevel(difficulty) {
        if (difficulty < 0.2) {
            return { level: 'very_easy', name: '非常简单', color: '#4ade80' };
        } else if (difficulty < 0.4) {
            return { level: 'easy', name: '简单', color: '#84cc16' };
        } else if (difficulty < 0.6) {
            return { level: 'normal', name: '普通', color: '#eab308' };
        } else if (difficulty < 0.8) {
            return { level: 'hard', name: '困难', color: '#f97316' };
        } else {
            return { level: 'very_hard', name: '非常困难', color: '#ef4444' };
        }
    }

    /**
     * 分析玩家行为
     */
    analyzePlayerBehavior(scoreData, gameData, result) {
        const currentPlayer = playerManager.getCurrentPlayer();
        if (!currentPlayer || currentPlayer.isGuest) return;
        
        const playerId = currentPlayer.id;
        const now = Date.now();
        
        // 获取或创建玩家行为记录
        if (!this.playerBehavior.has(playerId)) {
            this.playerBehavior.set(playerId, {
                submissions: [],
                patterns: {
                    avgAccuracy: 0,
                    avgScorePerSecond: 0,
                    commonPlayTimes: []
                }
            });
        }
        
        const behavior = this.playerBehavior.get(playerId);
        
        // 记录本次提交
        behavior.submissions.push({
            timestamp: now,
            score: scoreData.score,
            duration: scoreData.duration,
            accuracy: scoreData.accuracy || 0
        });
        
        // 保持最近100次记录
        if (behavior.submissions.length > 100) {
            behavior.submissions = behavior.submissions.slice(-100);
        }
        
        // 检查快速提交
        const recentSubmissions = behavior.submissions.filter(s => now - s.timestamp < 60000);
        if (recentSubmissions.length > this.config.anomalyDetection.rapidSubmissionThreshold) {
            result.warnings.push('提交频率过高');
        }
        
        // 检查相同分数
        const identicalScores = behavior.submissions.filter(s => s.score === scoreData.score);
        if (identicalScores.length > this.config.anomalyDetection.identicalScoreThreshold) {
            result.warnings.push('相同分数提交次数过多');
        }
    }

    /**
     * 检测异常模式
     */
    detectAnomalousPatterns(scoreData, gameData, result) {
        const { perfectHits, combo, totalHits, score, duration } = scoreData;
        const config = this.config.anomalyDetection;

        // 检查连续完美击中
        if (perfectHits > config.maxConsecutivePerfects) {
            result.warnings.push(`连续完美击中次数异常: ${perfectHits}`);
        }

        // 检查连击长度
        if (combo > config.suspiciousComboLength) {
            result.warnings.push(`连击长度异常: ${combo}`);
        }

        // 检查击中模式
        if (totalHits > 0 && perfectHits > 0) {
            const perfectRatio = perfectHits / totalHits;
            if (perfectRatio === 1 && totalHits > 10) {
                result.warnings.push('所有击中都是完美击中，可能存在异常');
            }

            // 检查完美击中比例是否过高
            if (perfectRatio > 0.95 && totalHits > 20) {
                result.warnings.push(`完美击中比例过高: ${(perfectRatio * 100).toFixed(1)}%`);
            }
        }

        // 检查分数增长模式
        this.detectScoreGrowthAnomalies(scoreData, result);

        // 检查时间模式异常
        this.detectTimePatternAnomalies(scoreData, gameData, result);

        // 检查统计数据一致性
        this.validateStatisticsConsistency(scoreData, result);
    }

    /**
     * 检测分数增长异常
     */
    detectScoreGrowthAnomalies(scoreData, result) {
        const { score, totalHits, duration } = scoreData;

        if (totalHits > 0 && duration > 0) {
            // 检查分数增长是否过于线性（可能是脚本）
            const avgScorePerHit = score / totalHits;
            const timePerHit = duration / totalHits;

            // 如果每次击中的分数和时间都非常一致，可能是异常
            if (avgScorePerHit > 100 && timePerHit < 50) {
                result.warnings.push('分数增长模式异常，可能存在自动化行为');
            }

            // 检查分数爆发性增长
            const scorePerSecond = score / (duration / 1000);
            if (scorePerSecond > 2000) {
                result.warnings.push(`分数增长过快: ${scorePerSecond.toFixed(2)}/秒`);
            }
        }
    }

    /**
     * 检测时间模式异常
     */
    detectTimePatternAnomalies(scoreData, gameData, result) {
        const { duration, totalHits, combo } = scoreData;

        // 检查游戏时长与击中次数的关系
        if (totalHits > 0) {
            const hitsPerSecond = totalHits / (duration / 1000);

            // 人类反应极限检查
            if (hitsPerSecond > 15) {
                result.errors.push(`击中频率超出人类极限: ${hitsPerSecond.toFixed(2)}/秒`);
            } else if (hitsPerSecond > 10) {
                result.warnings.push(`击中频率异常高: ${hitsPerSecond.toFixed(2)}/秒`);
            }
        }

        // 检查连击与时间的合理性
        if (combo > 0 && duration > 0) {
            const minTimeForCombo = combo * 50; // 每次连击至少50ms
            if (duration < minTimeForCombo) {
                result.errors.push(`连击时间不合理: ${combo}连击在${duration}ms内完成`);
            }
        }

        // 检查游戏时长是否过短
        if (duration < 3000 && scoreData.score > 1000) {
            result.warnings.push(`游戏时长过短但分数较高: ${duration}ms`);
        }
    }

    /**
     * 验证统计数据一致性
     */
    validateStatisticsConsistency(scoreData, result) {
        const { perfectHits, totalHits, accuracy, score } = scoreData;

        // 检查击中数据一致性
        if (perfectHits > totalHits) {
            result.errors.push('完美击中数不能超过总击中数');
        }

        // 检查准确率一致性
        if (accuracy !== undefined && totalHits > 0) {
            // 假设还有goodHits和missedHits
            const calculatedAccuracy = ((perfectHits + (totalHits - perfectHits)) / totalHits) * 100;
            if (Math.abs(accuracy - calculatedAccuracy) > 5) {
                result.warnings.push('准确率数据不一致');
            }
        }

        // 检查分数与击中数的合理性
        if (totalHits > 0) {
            const avgScorePerHit = score / totalHits;
            if (avgScorePerHit > 1000) {
                result.warnings.push(`单次击中平均分数过高: ${avgScorePerHit.toFixed(2)}`);
            }
        }

        // 检查数据范围
        if (accuracy !== undefined && (accuracy < 0 || accuracy > 100)) {
            result.errors.push('准确率数据超出有效范围');
        }

        if (perfectHits < 0 || totalHits < 0 || score < 0) {
            result.errors.push('统计数据不能为负数');
        }
    }

    /**
     * 高级行为分析
     */
    advancedBehaviorAnalysis(scoreData, gameData, result) {
        const currentPlayer = playerManager.getCurrentPlayer();
        if (!currentPlayer || currentPlayer.isGuest) return;

        const playerId = currentPlayer.id;
        const behavior = this.playerBehavior.get(playerId);

        if (!behavior || behavior.submissions.length < 5) return;

        // 分析分数提升模式
        this.analyzeScoreProgressionPattern(behavior, scoreData, result);

        // 分析游戏时间模式
        this.analyzePlayTimePattern(behavior, scoreData, result);

        // 分析准确率变化
        this.analyzeAccuracyPattern(behavior, scoreData, result);
    }

    /**
     * 分析分数提升模式
     */
    analyzeScoreProgressionPattern(behavior, scoreData, result) {
        const recentScores = behavior.submissions.slice(-10).map(s => s.score);
        const currentScore = scoreData.score;

        // 检查分数是否有异常跳跃
        const avgRecentScore = recentScores.reduce((a, b) => a + b, 0) / recentScores.length;
        const scoreJump = currentScore / avgRecentScore;

        if (scoreJump > 3) {
            result.warnings.push(`分数异常跳跃: 比平均分数高${scoreJump.toFixed(1)}倍`);
        }

        // 检查分数是否过于稳定（可能是脚本）
        const scoreVariance = this.calculateVariance(recentScores);
        if (scoreVariance < 100 && recentScores.length > 5) {
            result.warnings.push('分数变化过于稳定，可能存在异常');
        }
    }

    /**
     * 分析游戏时间模式
     */
    analyzePlayTimePattern(behavior, scoreData, result) {
        const recentDurations = behavior.submissions.slice(-10).map(s => s.duration);
        const currentDuration = scoreData.duration;

        // 检查游戏时长是否过于一致
        const durationVariance = this.calculateVariance(recentDurations);
        if (durationVariance < 1000 && recentDurations.length > 5) {
            result.warnings.push('游戏时长过于一致，可能存在异常');
        }
    }

    /**
     * 分析准确率模式
     */
    analyzeAccuracyPattern(behavior, scoreData, result) {
        const recentAccuracies = behavior.submissions.slice(-10)
            .map(s => s.accuracy)
            .filter(a => a !== undefined && a > 0);

        if (recentAccuracies.length < 3) return;

        const currentAccuracy = scoreData.accuracy;
        if (currentAccuracy === undefined) return;

        // 检查准确率是否有异常提升
        const avgRecentAccuracy = recentAccuracies.reduce((a, b) => a + b, 0) / recentAccuracies.length;
        const accuracyImprovement = currentAccuracy - avgRecentAccuracy;

        if (accuracyImprovement > 20) {
            result.warnings.push(`准确率异常提升: +${accuracyImprovement.toFixed(1)}%`);
        }

        // 检查准确率是否过于稳定
        const accuracyVariance = this.calculateVariance(recentAccuracies);
        if (accuracyVariance < 1 && currentAccuracy > 90) {
            result.warnings.push('准确率过于稳定且过高，可能存在异常');
        }
    }

    /**
     * 计算方差
     */
    calculateVariance(values) {
        if (values.length < 2) return 0;

        const mean = values.reduce((a, b) => a + b, 0) / values.length;
        const squaredDiffs = values.map(value => Math.pow(value - mean, 2));

        return squaredDiffs.reduce((a, b) => a + b, 0) / values.length;
    }

    /**
     * 加载玩家行为数据
     */
    async loadPlayerBehaviorData() {
        try {
            if (!storageService) return;
            
            const behaviorData = await storageService.get('player.behavior.data');
            if (behaviorData) {
                this.playerBehavior = new Map(Object.entries(behaviorData));
            }
        } catch (error) {
            console.error('加载玩家行为数据失败:', error);
        }
    }

    /**
     * 保存玩家行为数据
     */
    async savePlayerBehaviorData() {
        try {
            if (!storageService) return;
            
            const behaviorData = Object.fromEntries(this.playerBehavior);
            await storageService.put('player.behavior.data', behaviorData);
        } catch (error) {
            console.error('保存玩家行为数据失败:', error);
        }
    }

    /**
     * 初始化关卡难度缓存
     */
    async initializeLevelDifficultyCache() {
        try {
            if (!storageService) return;
            
            const cacheData = await storageService.get('level.difficulty.cache');
            if (cacheData) {
                this.levelDifficultyCache = new Map(Object.entries(cacheData));
            }
        } catch (error) {
            console.error('初始化关卡难度缓存失败:', error);
        }
    }

    /**
     * 保存关卡难度缓存
     */
    async saveLevelDifficultyCache() {
        try {
            if (!storageService) return;
            
            const cacheData = Object.fromEntries(this.levelDifficultyCache);
            await storageService.put('level.difficulty.cache', cacheData);
        } catch (error) {
            console.error('保存关卡难度缓存失败:', error);
        }
    }

    /**
     * 清理过期数据
     */
    async cleanupExpiredData() {
        const now = Date.now();
        const oneWeekAgo = now - (7 * 24 * 60 * 60 * 1000);
        
        // 清理过期的玩家行为记录
        for (const [playerId, behavior] of this.playerBehavior.entries()) {
            behavior.submissions = behavior.submissions.filter(s => s.timestamp > oneWeekAgo);
            if (behavior.submissions.length === 0) {
                this.playerBehavior.delete(playerId);
            }
        }
        
        // 保存清理后的数据
        await this.savePlayerBehaviorData();
    }
}

// 创建全局防作弊系统实例
window.antiCheatSystem = new AntiCheatSystem();
