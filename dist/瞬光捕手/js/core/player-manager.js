/**
 * 瞬光捕手 - 玩家管理系统
 * 负责玩家账号的创建、切换和数据管理
 */

class PlayerManager {
    constructor() {
        this.currentPlayer = null;
        this.players = new Map();
        this.initialized = false;
        this.guestPlayer = {
            id: 'guest',
            name: '游客',
            isGuest: true,
            createdAt: Date.now(),
            lastPlayedAt: Date.now()
        };
    }

    /**
     * 初始化玩家管理系统
     */
    async init() {
        try {
            // 加载所有玩家数据
            await this.loadAllPlayers();
            
            // 获取上次使用的玩家
            const lastPlayerId = await storageService.get('system.lastPlayer', 'guest');
            
            // 设置当前玩家
            if (lastPlayerId === 'guest' || !this.players.has(lastPlayerId)) {
                this.currentPlayer = this.guestPlayer;
            } else {
                this.currentPlayer = this.players.get(lastPlayerId);
            }
            
            this.initialized = true;
            console.log(`玩家管理系统初始化完成，当前玩家: ${this.currentPlayer.name}`);
            
            // 更新UI显示
            this.updatePlayerUI();
            
        } catch (error) {
            console.error('玩家管理系统初始化失败:', error);
            this.currentPlayer = this.guestPlayer;
            this.initialized = true;
        }
    }

    /**
     * 加载所有玩家数据
     */
    async loadAllPlayers() {
        try {
            const playerKeys = await storageService.list('player.');
            
            for (const key of playerKeys) {
                if (key.startsWith('player.') && key.endsWith('.profile')) {
                    const playerData = await storageService.get(key);
                    if (playerData && playerData.id) {
                        this.players.set(playerData.id, playerData);
                    }
                }
            }
            
            console.log(`加载了 ${this.players.size} 个玩家账号`);
        } catch (error) {
            console.error('加载玩家数据失败:', error);
        }
    }

    /**
     * 创建新玩家
     * @param {string} name - 玩家名称
     * @returns {Promise<object|null>} 创建的玩家对象或null（如果失败）
     */
    async createPlayer(name) {
        if (!name || name.trim().length === 0) {
            throw new Error(i18nService.t('error.invalidInput'));
        }

        const trimmedName = name.trim();
        
        // 检查名称是否已存在
        if (this.isPlayerNameExists(trimmedName)) {
            throw new Error(i18nService.t('error.playerNameExists'));
        }

        // 生成唯一ID
        const playerId = this.generatePlayerId();
        
        // 创建玩家对象
        const player = {
            id: playerId,
            name: trimmedName,
            isGuest: false,
            createdAt: Date.now(),
            lastPlayedAt: Date.now(),
            stats: {
                totalScore: 0,
                bestScore: 0,
                totalGames: 0,
                totalPlayTime: 0,
                levelsCompleted: 0,
                perfectHits: 0,
                customLevelsCreated: 0
            },
            settings: {
                soundVolume: 50,
                musicVolume: 30,
                language: i18nService.getCurrentLanguage()
            },
            gameData: {
                currentLevel: 1,
                unlockedLevels: [1],
                achievements: []
            }
        };

        try {
            // 保存玩家数据
            await this.savePlayerData(player);
            
            // 添加到内存中
            this.players.set(playerId, player);
            
            console.log(`创建新玩家: ${trimmedName} (ID: ${playerId})`);
            return player;
            
        } catch (error) {
            console.error('创建玩家失败:', error);
            throw new Error(i18nService.t('error.saveGameFailed'));
        }
    }

    /**
     * 切换到指定玩家
     * @param {string} playerId - 玩家ID
     * @returns {Promise<boolean>} 是否成功切换
     */
    async switchToPlayer(playerId) {
        try {
            let targetPlayer;
            
            if (playerId === 'guest') {
                targetPlayer = this.guestPlayer;
            } else {
                targetPlayer = this.players.get(playerId);
                if (!targetPlayer) {
                    throw new Error(i18nService.t('error.playerNotFound'));
                }
            }

            // 保存当前玩家的最后游玩时间
            if (this.currentPlayer && !this.currentPlayer.isGuest) {
                this.currentPlayer.lastPlayedAt = Date.now();
                await this.savePlayerData(this.currentPlayer);
            }

            // 切换玩家
            this.currentPlayer = targetPlayer;
            
            // 更新最后游玩时间
            if (!targetPlayer.isGuest) {
                targetPlayer.lastPlayedAt = Date.now();
                await this.savePlayerData(targetPlayer);
            }

            // 保存当前玩家ID
            await storageService.put('system.lastPlayer', playerId);
            
            // 更新UI
            this.updatePlayerUI();
            
            console.log(`切换到玩家: ${targetPlayer.name}`);
            return true;
            
        } catch (error) {
            console.error('切换玩家失败:', error);
            return false;
        }
    }

    /**
     * 删除玩家
     * @param {string} playerId - 玩家ID
     * @returns {Promise<boolean>} 是否成功删除
     */
    async deletePlayer(playerId) {
        if (playerId === 'guest') {
            return false; // 不能删除游客账号
        }

        try {
            const player = this.players.get(playerId);
            if (!player) {
                return false;
            }

            // 删除玩家相关的所有数据
            const playerKeys = await storageService.list(`player.${playerId}.`);
            for (const key of playerKeys) {
                await storageService.delete(key);
            }

            // 从内存中移除
            this.players.delete(playerId);

            // 如果删除的是当前玩家，切换到游客
            if (this.currentPlayer && this.currentPlayer.id === playerId) {
                await this.switchToPlayer('guest');
            }

            console.log(`删除玩家: ${player.name} (ID: ${playerId})`);
            return true;
            
        } catch (error) {
            console.error('删除玩家失败:', error);
            return false;
        }
    }

    /**
     * 获取当前玩家
     * @returns {object} 当前玩家对象
     */
    getCurrentPlayer() {
        return this.currentPlayer || this.guestPlayer;
    }

    /**
     * 获取所有玩家列表
     * @returns {Array} 玩家列表
     */
    getAllPlayers() {
        return Array.from(this.players.values()).sort((a, b) => b.lastPlayedAt - a.lastPlayedAt);
    }

    /**
     * 更新玩家统计数据
     * @param {object} stats - 统计数据更新
     */
    async updatePlayerStats(stats) {
        const player = this.getCurrentPlayer();
        if (player.isGuest) {
            return; // 游客数据不保存
        }

        // 更新统计数据
        Object.assign(player.stats, stats);
        player.lastPlayedAt = Date.now();

        // 保存数据
        await this.savePlayerData(player);
    }

    /**
     * 更新玩家游戏数据
     * @param {object} gameData - 游戏数据更新
     */
    async updatePlayerGameData(gameData) {
        const player = this.getCurrentPlayer();
        if (player.isGuest) {
            return; // 游客数据不保存
        }

        // 更新游戏数据
        Object.assign(player.gameData, gameData);
        player.lastPlayedAt = Date.now();

        // 保存数据
        await this.savePlayerData(player);
    }

    /**
     * 更新玩家设置
     * @param {object} settings - 设置数据更新
     */
    async updatePlayerSettings(settings) {
        const player = this.getCurrentPlayer();
        if (player.isGuest) {
            return; // 游客设置不保存
        }

        // 更新设置
        Object.assign(player.settings, settings);

        // 保存数据
        await this.savePlayerData(player);
    }

    /**
     * 保存玩家数据
     * @param {object} player - 玩家对象
     */
    async savePlayerData(player) {
        if (player.isGuest) {
            return; // 游客数据不保存
        }

        const key = `player.${player.id}.profile`;
        await storageService.put(key, player);
    }

    /**
     * 检查玩家名称是否已存在
     * @param {string} name - 玩家名称
     * @returns {boolean} 是否存在
     */
    isPlayerNameExists(name) {
        const lowerName = name.toLowerCase();
        
        // 检查是否与游客名称冲突
        if (lowerName === '游客' || lowerName === 'guest') {
            return true;
        }

        // 检查现有玩家
        for (const player of this.players.values()) {
            if (player.name.toLowerCase() === lowerName) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 生成唯一的玩家ID
     * @returns {string} 玩家ID
     */
    generatePlayerId() {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substr(2, 5);
        return `player_${timestamp}_${random}`;
    }

    /**
     * 更新玩家UI显示
     */
    updatePlayerUI() {
        const currentPlayerNameElement = document.getElementById('current-player-name');
        if (currentPlayerNameElement) {
            const player = this.getCurrentPlayer();
            currentPlayerNameElement.textContent = player.name;
        }
    }

    /**
     * 显示玩家管理界面
     */
    showPlayerManagement() {
        const overlay = document.getElementById('player-management');
        const existingPlayersContainer = document.getElementById('existing-players');
        
        if (!overlay || !existingPlayersContainer) {
            return;
        }

        // 清空现有列表
        existingPlayersContainer.innerHTML = '';

        // 添加游客选项
        const guestItem = this.createPlayerItem(this.guestPlayer);
        existingPlayersContainer.appendChild(guestItem);

        // 添加其他玩家
        const players = this.getAllPlayers();
        players.forEach(player => {
            const playerItem = this.createPlayerItem(player);
            existingPlayersContainer.appendChild(playerItem);
        });

        // 显示界面
        overlay.classList.remove('hidden');
    }

    /**
     * 创建玩家列表项
     * @param {object} player - 玩家对象
     * @returns {HTMLElement} 玩家列表项元素
     */
    createPlayerItem(player) {
        const item = document.createElement('div');
        item.className = 'player-item';
        
        if (this.currentPlayer && this.currentPlayer.id === player.id) {
            item.classList.add('active');
        }

        const nameSpan = document.createElement('span');
        nameSpan.textContent = player.name;
        
        const buttonsDiv = document.createElement('div');
        
        // 选择按钮
        if (this.currentPlayer.id !== player.id) {
            const selectBtn = document.createElement('button');
            selectBtn.className = 'small-btn';
            selectBtn.textContent = i18nService.t('player.select');
            selectBtn.onclick = () => {
                this.switchToPlayer(player.id);
                this.hidePlayerManagement();
            };
            buttonsDiv.appendChild(selectBtn);
        }

        // 删除按钮（不能删除游客和当前玩家）
        if (!player.isGuest && this.currentPlayer.id !== player.id) {
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'small-btn';
            deleteBtn.style.background = 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)';
            deleteBtn.textContent = i18nService.t('player.delete');
            deleteBtn.onclick = () => {
                if (confirm(`确定要删除玩家 "${player.name}" 吗？此操作不可撤销。`)) {
                    this.deletePlayer(player.id);
                    this.showPlayerManagement(); // 刷新列表
                }
            };
            buttonsDiv.appendChild(deleteBtn);
        }

        item.appendChild(nameSpan);
        item.appendChild(buttonsDiv);
        
        return item;
    }

    /**
     * 隐藏玩家管理界面
     */
    hidePlayerManagement() {
        const overlay = document.getElementById('player-management');
        if (overlay) {
            overlay.classList.add('hidden');
        }
    }

    /**
     * 处理创建新玩家
     */
    async handleCreateNewPlayer() {
        const nameInput = document.getElementById('new-player-name');
        if (!nameInput) {
            return;
        }

        const name = nameInput.value.trim();
        if (!name) {
            alert(i18nService.t('error.invalidInput'));
            return;
        }

        try {
            const newPlayer = await this.createPlayer(name);
            if (newPlayer) {
                nameInput.value = '';
                this.showPlayerManagement(); // 刷新列表
                alert(`玩家 "${newPlayer.name}" 创建成功！`);
            }
        } catch (error) {
            alert(error.message);
        }
    }
}

// 创建全局玩家管理器实例
window.playerManager = new PlayerManager();
