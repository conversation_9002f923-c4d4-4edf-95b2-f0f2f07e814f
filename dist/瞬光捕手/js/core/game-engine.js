/**
 * 瞬光捕手 - 核心游戏引擎
 * 实现游戏的核心逻辑：捕捉决定性瞬间的光点
 */

class GameEngine {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.gameState = 'menu'; // menu, playing, paused, gameOver
        this.animationId = null;
        
        // 游戏数据
        this.score = 0;
        this.level = 1;
        this.lives = 3;
        this.combo = 0;
        this.maxCombo = 0;
        
        // 游戏对象
        this.sparks = [];
        this.particles = [];
        this.powerUps = [];
        
        // 时间管理
        this.lastTime = 0;
        this.deltaTime = 0;
        this.gameTime = 0;
        
        // 关卡配置
        this.levelConfig = {
            sparkSpawnRate: 2000, // 光点生成间隔（毫秒）
            sparkSpeed: 1.0,      // 光点移动速度倍数
            sparkCount: 1,        // 同时存在的光点数量
            perfectWindow: 100,   // 完美时机窗口（毫秒）
            goodWindow: 200,      // 良好时机窗口（毫秒）
        };

        // 自定义关卡相关
        this.currentCustomLevel = null;
        this.levelDuration = null;
        this.levelStartTime = null;
        
        // 输入状态
        this.inputState = {
            mouseX: 0,
            mouseY: 0,
            isPressed: false,
            touchActive: false
        };
        
        this.initialized = false;
    }

    /**
     * 初始化游戏引擎
     */
    async init() {
        try {
            this.canvas = document.getElementById('game-canvas');
            this.ctx = this.canvas.getContext('2d');
            
            // 设置画布大小
            this.resizeCanvas();
            
            // 绑定事件
            this.bindEvents();
            
            // 初始化游戏对象
            this.reset();
            
            this.initialized = true;
            console.log('游戏引擎初始化完成');
            
        } catch (error) {
            console.error('游戏引擎初始化失败:', error);
        }
    }

    /**
     * 调整画布大小
     */
    resizeCanvas() {
        const container = document.getElementById('game-canvas-container');
        if (!container || !this.canvas) return;
        
        const containerRect = container.getBoundingClientRect();
        const maxWidth = Math.min(containerRect.width - 40, 800);
        const maxHeight = Math.min(containerRect.height - 40, 600);
        
        // 保持16:9的宽高比
        let canvasWidth = maxWidth;
        let canvasHeight = maxWidth * 9 / 16;
        
        if (canvasHeight > maxHeight) {
            canvasHeight = maxHeight;
            canvasWidth = maxHeight * 16 / 9;
        }
        
        this.canvas.width = canvasWidth;
        this.canvas.height = canvasHeight;
        
        // 设置CSS样式
        this.canvas.style.width = canvasWidth + 'px';
        this.canvas.style.height = canvasHeight + 'px';
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 窗口大小变化
        window.addEventListener('resize', () => this.resizeCanvas());
        
        // 鼠标事件
        this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        
        // 触摸事件
        this.canvas.addEventListener('touchstart', (e) => this.handleTouchStart(e));
        this.canvas.addEventListener('touchmove', (e) => this.handleTouchMove(e));
        this.canvas.addEventListener('touchend', (e) => this.handleTouchEnd(e));
        
        // 防止右键菜单
        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
    }

    /**
     * 加载自定义关卡
     * @param {object} levelData - 关卡数据
     */
    async loadCustomLevel(levelData) {
        try {
            console.log('加载自定义关卡:', levelData.name || '未命名关卡');

            // 保存当前关卡数据
            this.currentCustomLevel = levelData;

            // 重置游戏状态
            this.reset();

            // 应用自定义关卡配置
            if (levelData.data) {
                this.applyCustomLevelConfig(levelData.data);
            }

            // 如果关卡有预设的光点，创建它们
            if (levelData.objects && levelData.objects.length > 0) {
                this.createCustomSparks(levelData.objects);
            }

            console.log('自定义关卡加载完成');
            return true;

        } catch (error) {
            console.error('加载自定义关卡失败:', error);
            return false;
        }
    }

    /**
     * 应用自定义关卡配置
     * @param {object} levelConfig - 关卡配置
     */
    applyCustomLevelConfig(levelConfig) {
        // 应用关卡配置，如果没有则使用默认值
        this.levelConfig = {
            sparkSpawnRate: levelConfig.sparkSpawnRate || 2000,
            sparkSpeed: levelConfig.sparkSpeed || 1.0,
            sparkCount: levelConfig.sparkCount || 1,
            perfectWindow: levelConfig.perfectWindow || 100,
            goodWindow: levelConfig.goodWindow || 200,
            duration: levelConfig.duration || 30000, // 关卡持续时间
            targetScore: levelConfig.targetScore || 1000 // 目标分数
        };

        // 设置关卡持续时间
        if (levelConfig.duration) {
            this.levelDuration = levelConfig.duration;
            this.levelStartTime = Date.now();
        }
    }

    /**
     * 创建自定义光点
     * @param {Array} objects - 关卡对象列表
     */
    createCustomSparks(objects) {
        // 清空现有光点
        this.sparks = [];

        // 创建关卡中定义的光点
        objects.forEach(obj => {
            if (obj.type === 'spark') {
                const spark = {
                    id: obj.id,
                    x: obj.x,
                    y: obj.y,
                    targetX: obj.properties.targetX || obj.x,
                    targetY: obj.properties.targetY || obj.y,
                    size: obj.properties.size || 20,
                    color: obj.properties.color || this.getRandomSparkColor(),
                    speed: obj.properties.speed || this.levelConfig.sparkSpeed,
                    life: 1.0,
                    maxLife: obj.properties.maxLife || 3000,
                    perfectTime: obj.properties.perfectTime || 1000,
                    perfectDuration: this.levelConfig.perfectWindow,
                    goodDuration: this.levelConfig.goodWindow,
                    phase: 'approaching',
                    phaseStartTime: this.gameTime,
                    glowIntensity: 0,
                    pulsePhase: 0,
                    isCustom: true // 标记为自定义光点
                };

                this.sparks.push(spark);
            }
        });
    }

    /**
     * 开始游戏
     */
    startGame() {
        this.gameState = 'playing';
        this.reset();
        this.lastSparkTime = 0;
        this.gameLoop();
        this.updateHint('game.hint.start');
        console.log('游戏开始');
    }

    /**
     * 暂停游戏
     */
    pauseGame() {
        if (this.gameState === 'playing') {
            this.gameState = 'paused';
            if (this.animationId) {
                cancelAnimationFrame(this.animationId);
                this.animationId = null;
            }
        }
    }

    /**
     * 恢复游戏
     */
    resumeGame() {
        if (this.gameState === 'paused') {
            this.gameState = 'playing';
            this.gameLoop();
        }
    }

    /**
     * 重置游戏状态
     */
    reset() {
        this.score = 0;
        this.level = 1;
        this.lives = 3;
        this.combo = 0;
        this.maxCombo = 0;
        this.gameTime = 0;
        this.lastSparkTime = 0;
        
        this.sparks = [];
        this.particles = [];
        this.powerUps = [];
        
        this.updateLevelConfig();
        this.updateUI();
    }

    /**
     * 游戏主循环
     */
    gameLoop(currentTime = 0) {
        if (this.gameState !== 'playing') return;
        
        this.deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        this.gameTime += this.deltaTime;
        
        // 更新游戏逻辑
        this.update();
        
        // 渲染画面
        this.render();
        
        // 继续循环
        this.animationId = requestAnimationFrame((time) => this.gameLoop(time));
    }

    /**
     * 更新游戏逻辑
     */
    update() {
        // 生成新的光点
        this.spawnSparks();

        // 更新光点
        this.updateSparks();

        // 更新粒子效果
        this.updateParticles();

        // 更新流星效果
        this.updateMeteors();

        // 更新道具
        this.updatePowerUps();

        // 检查关卡进度
        this.checkLevelProgress();

        // 检查游戏结束条件
        this.checkGameOver();
    }

    /**
     * 生成光点
     */
    spawnSparks() {
        if (this.gameTime - this.lastSparkTime >= this.levelConfig.sparkSpawnRate) {
            if (this.sparks.length < this.levelConfig.sparkCount) {
                this.createSpark();
                this.lastSparkTime = this.gameTime;
            }
        }
    }

    /**
     * 创建新光点
     */
    createSpark() {
        const spark = {
            id: Date.now() + Math.random(),
            x: Math.random() * (this.canvas.width - 100) + 50,
            y: Math.random() * (this.canvas.height - 100) + 50,
            targetX: Math.random() * (this.canvas.width - 100) + 50,
            targetY: Math.random() * (this.canvas.height - 100) + 50,
            size: 20 + Math.random() * 20,
            color: this.getRandomSparkColor(),
            speed: this.levelConfig.sparkSpeed * (0.5 + Math.random() * 0.5),
            life: 1.0,
            maxLife: 3000 + Math.random() * 2000, // 3-5秒生命周期
            perfectTime: 1000 + Math.random() * 1000, // 1-2秒后进入完美时机
            perfectDuration: this.levelConfig.perfectWindow,
            goodDuration: this.levelConfig.goodWindow,
            phase: 'approaching', // approaching, perfect, good, fading
            phaseStartTime: this.gameTime,
            glowIntensity: 0,
            pulsePhase: 0
        };
        
        this.sparks.push(spark);
    }

    /**
     * 获取随机光点颜色
     */
    getRandomSparkColor() {
        const colors = [
            '#FFD700', // 金色
            '#FF6B6B', // 红色
            '#4ECDC4', // 青色
            '#45B7D1', // 蓝色
            '#96CEB4', // 绿色
            '#FFEAA7', // 黄色
            '#DDA0DD', // 紫色
            '#FFA07A'  // 橙色
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    /**
     * 更新光点状态
     */
    updateSparks() {
        for (let i = this.sparks.length - 1; i >= 0; i--) {
            const spark = this.sparks[i];
            const elapsed = this.gameTime - spark.phaseStartTime;
            
            // 更新位置（缓慢移动到目标位置）
            const dx = spark.targetX - spark.x;
            const dy = spark.targetY - spark.y;
            spark.x += dx * spark.speed * this.deltaTime * 0.001;
            spark.y += dy * spark.speed * this.deltaTime * 0.001;
            
            // 更新脉冲效果
            spark.pulsePhase += this.deltaTime * 0.005;
            
            // 更新阶段
            if (spark.phase === 'approaching' && elapsed >= spark.perfectTime) {
                spark.phase = 'perfect';
                spark.phaseStartTime = this.gameTime;
                spark.glowIntensity = 1.0;
            } else if (spark.phase === 'perfect' && elapsed >= spark.perfectDuration) {
                spark.phase = 'good';
                spark.phaseStartTime = this.gameTime;
                spark.glowIntensity = 0.6;
            } else if (spark.phase === 'good' && elapsed >= spark.goodDuration) {
                spark.phase = 'fading';
                spark.phaseStartTime = this.gameTime;
                spark.glowIntensity = 0.3;
            }
            
            // 更新生命值
            spark.life -= this.deltaTime / spark.maxLife;
            
            // 移除死亡的光点
            if (spark.life <= 0) {
                this.sparks.splice(i, 1);
                // 如果光点自然消失，减少连击
                if (this.combo > 0) {
                    this.combo = Math.max(0, this.combo - 1);
                }
            }
        }
    }

    /**
     * 更新粒子效果
     */
    updateParticles() {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            
            particle.x += particle.vx * this.deltaTime * 0.001;
            particle.y += particle.vy * this.deltaTime * 0.001;
            particle.life -= this.deltaTime / particle.maxLife;
            particle.size *= 0.999;
            
            if (particle.life <= 0 || particle.size < 1) {
                this.particles.splice(i, 1);
            }
        }
    }

    /**
     * 更新道具
     */
    updatePowerUps() {
        // 暂时不实现道具系统
    }

    /**
     * 检查关卡进度
     */
    checkLevelProgress() {
        const scoreThreshold = this.level * 1000;
        if (this.score >= scoreThreshold) {
            this.level++;
            this.updateLevelConfig();
            this.updateUI();
            this.showLevelUpEffect();
        }
    }

    /**
     * 更新关卡配置
     */
    updateLevelConfig() {
        const baseConfig = {
            sparkSpawnRate: 2000,
            sparkSpeed: 1.0,
            sparkCount: 1,
            perfectWindow: 100,
            goodWindow: 200,
        };
        
        // 随着关卡增加难度
        this.levelConfig = {
            sparkSpawnRate: Math.max(800, baseConfig.sparkSpawnRate - (this.level - 1) * 100),
            sparkSpeed: baseConfig.sparkSpeed + (this.level - 1) * 0.1,
            sparkCount: Math.min(5, Math.floor((this.level - 1) / 3) + 1),
            perfectWindow: Math.max(50, baseConfig.perfectWindow - (this.level - 1) * 5),
            goodWindow: Math.max(100, baseConfig.goodWindow - (this.level - 1) * 10),
        };
    }

    /**
     * 检查游戏结束
     */
    checkGameOver() {
        if (this.lives <= 0) {
            this.gameState = 'gameOver';
            this.endGame();
        }
    }

    /**
     * 结束游戏
     */
    async endGame() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }

        // 计算游戏统计数据
        const gameStats = this.calculateGameStats();

        // 更新玩家统计
        const player = playerManager.getCurrentPlayer();
        if (!player.isGuest) {
            await playerManager.updatePlayerStats({
                totalScore: player.stats.totalScore + this.score,
                bestScore: Math.max(player.stats.bestScore, this.score),
                totalGames: player.stats.totalGames + 1,
                levelsCompleted: Math.max(player.stats.levelsCompleted, this.level - 1),
                perfectHits: player.stats.perfectHits + this.perfectHits
            });
        }

        // 提交分数到排行榜
        await this.submitScoreToLeaderboard(gameStats);

        // 显示游戏结束界面
        this.showGameOverScreen();
    }

    /**
     * 计算游戏统计数据
     */
    calculateGameStats() {
        const totalHits = this.perfectHits + this.goodHits + this.missedHits;
        const accuracy = totalHits > 0 ? ((this.perfectHits + this.goodHits) / totalHits * 100) : 0;
        const duration = Date.now() - this.gameStartTime;

        return {
            score: this.score,
            level: this.level,
            perfectHits: this.perfectHits,
            combo: this.maxCombo,
            duration: duration,
            accuracy: Math.round(accuracy * 100) / 100,
            totalHits: totalHits
        };
    }

    /**
     * 提交分数到排行榜
     */
    async submitScoreToLeaderboard(gameStats) {
        if (!leaderboardManager || !leaderboardManager.initialized) {
            console.log('排行榜管理器未初始化，跳过分数提交');
            return;
        }

        try {
            // 准备游戏数据用于防作弊验证
            const gameData = {
                startTime: this.gameStartTime,
                endTime: Date.now(),
                clickEvents: this.clickEvents || [],
                sparkEvents: this.sparkEvents || [],
                gameMode: this.gameMode || 'normal',
                levelConfig: this.currentLevelConfig || null
            };

            // 获取当前关卡数据（如果是自定义关卡）
            const levelData = this.currentLevel ? {
                id: this.currentLevel.id,
                name: this.currentLevel.name,
                data: this.currentLevel.data,
                objects: this.currentLevel.objects,
                author: this.currentLevel.author,
                authorId: this.currentLevel.authorId,
                isCustom: this.currentLevel.isCustom || false,
                certified: this.currentLevel.certified || false
            } : null;

            console.log('🎯 开始提交分数到排行榜...');

            // 提交到全球排行榜
            const globalResult = await leaderboardManager.submitScore(
                leaderboardManager.leaderboardTypes.GLOBAL_HIGH_SCORE,
                gameStats,
                levelData,
                gameData
            );

            if (globalResult.success) {
                console.log(`✅ 全球排行榜提交成功，排名: ${globalResult.rank}`);
            } else {
                console.warn(`⚠️ 全球排行榜提交失败: ${globalResult.reason}`);
            }

            // 提交到每日排行榜
            const dailyResult = await leaderboardManager.submitScore(
                leaderboardManager.leaderboardTypes.DAILY_HIGH_SCORE,
                gameStats,
                levelData,
                gameData
            );

            // 提交到每周排行榜
            const weeklyResult = await leaderboardManager.submitScore(
                leaderboardManager.leaderboardTypes.WEEKLY_HIGH_SCORE,
                gameStats,
                levelData,
                gameData
            );

            // 如果有完美击中，提交到完美击中排行榜
            if (gameStats.perfectHits > 0) {
                const perfectResult = await leaderboardManager.submitScore(
                    leaderboardManager.leaderboardTypes.PERFECT_HITS,
                    { ...gameStats, score: gameStats.perfectHits },
                    levelData,
                    gameData
                );

                if (perfectResult.success) {
                    console.log(`🎯 完美击中排行榜提交成功: ${gameStats.perfectHits}次`);
                }
            }

            // 如果有连击记录，提交到连击排行榜
            if (gameStats.combo > 1) {
                const comboResult = await leaderboardManager.submitScore(
                    leaderboardManager.leaderboardTypes.COMBO_RECORD,
                    { ...gameStats, score: gameStats.combo },
                    levelData,
                    gameData
                );

                if (comboResult.success) {
                    console.log(`🔥 连击排行榜提交成功: ${gameStats.combo}连击`);
                }
            }

            // 显示提交结果通知
            this.showSubmissionResults([globalResult, dailyResult, weeklyResult]);

        } catch (error) {
            console.error('❌ 提交分数到排行榜失败:', error);

            // 显示错误通知
            if (window.uiManager) {
                uiManager.showToast('分数提交失败，请稍后重试', 'error');
            }
        }
    }

    /**
     * 处理点击/触摸
     */
    handleClick(x, y) {
        if (this.gameState !== 'playing') return;

        let hit = false;
        let bestHit = null;
        let bestDistance = Infinity;

        // 检查是否击中光点
        for (let i = this.sparks.length - 1; i >= 0; i--) {
            const spark = this.sparks[i];
            const distance = Math.sqrt((x - spark.x) ** 2 + (y - spark.y) ** 2);

            // 计算实际的渲染大小（包括脉冲效果）
            const pulse = Math.sin(spark.pulsePhase) * 0.2 + 1;
            const actualSize = spark.size * pulse * spark.life;

            // 增加容错机制：为移动的光点提供额外的碰撞范围
            let hitRadius = actualSize;
            if (spark.speed > 0) {
                // 根据移动速度增加碰撞范围
                const speedFactor = Math.min(spark.speed * 0.5, 10); // 最多增加10像素
                hitRadius += speedFactor;
            }

            if (distance <= hitRadius && distance < bestDistance) {
                bestHit = { spark, index: i, distance };
                bestDistance = distance;
                hit = true;
            }
        }

        if (bestHit) {
            console.log(`🎯 击中光点! 距离: ${bestHit.distance.toFixed(2)}, 阶段: ${bestHit.spark.phase}`);
            this.hitSpark(bestHit.spark, bestHit.index);
        } else {
            console.log(`❌ 未击中任何光点! 点击位置: (${x.toFixed(2)}, ${y.toFixed(2)})`);
            // 输出当前所有光点的位置和大小用于调试
            this.sparks.forEach((spark, i) => {
                const distance = Math.sqrt((x - spark.x) ** 2 + (y - spark.y) ** 2);
                const pulse = Math.sin(spark.pulsePhase) * 0.2 + 1;
                const actualSize = spark.size * pulse * spark.life;
                console.log(`  光点${i}: 位置(${spark.x.toFixed(2)}, ${spark.y.toFixed(2)}), 距离: ${distance.toFixed(2)}, 大小: ${actualSize.toFixed(2)}, 阶段: ${spark.phase}`);
            });
            this.missClick();
        }
    }

    /**
     * 击中光点
     */
    hitSpark(spark, index) {
        let points = 0;
        let feedback = '';
        
        // 根据时机计算得分
        if (spark.phase === 'perfect') {
            points = 100;
            feedback = 'game.hint.perfect';
            this.combo++;
            this.perfectHits = (this.perfectHits || 0) + 1;
        } else if (spark.phase === 'good') {
            points = 50;
            feedback = 'game.hint.good';
            this.combo++;
        } else {
            points = 20;
            feedback = 'game.hint.good';
            this.combo = Math.max(0, this.combo - 1);
        }
        
        // 连击奖励
        if (this.combo > 1) {
            points *= Math.min(this.combo * 0.1 + 1, 3); // 最多3倍奖励
        }
        
        this.score += Math.floor(points);
        this.maxCombo = Math.max(this.maxCombo, this.combo);
        
        // 创建粒子效果
        this.createHitEffect(spark.x, spark.y, spark.color);

        // 创建现代化视觉效果
        if (window.modernEffects) {
            // 光点击中特效
            const canvasRect = this.canvas.getBoundingClientRect();
            const effectX = canvasRect.left + spark.x;
            const effectY = canvasRect.top + spark.y;
            window.modernEffects.createSparkHitEffect(spark.x, spark.y, spark.color);

            // 连击特效
            if (this.combo > 2) {
                window.modernEffects.createComboEffect(this.combo, spark.x, spark.y);
            }
        }

        // 移除光点
        this.sparks.splice(index, 1);

        // 更新UI
        this.updateUI();
        this.updateHint(feedback);
    }

    /**
     * 错过点击
     */
    missClick() {
        this.combo = 0;
        this.lives--;
        this.updateUI();
        this.updateHint('game.hint.miss');
    }

    /**
     * 创建击中效果
     */
    createHitEffect(x, y, color) {
        for (let i = 0; i < 10; i++) {
            const particle = {
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 200,
                vy: (Math.random() - 0.5) * 200,
                size: Math.random() * 8 + 2,
                color: color,
                life: 1.0,
                maxLife: 1000 + Math.random() * 500
            };
            this.particles.push(particle);
        }
    }

    /**
     * 显示升级效果
     */
    showLevelUpEffect() {
        this.updateHint('恭喜升级！');
        // 可以添加更多视觉效果
    }

    /**
     * 渲染游戏画面
     */
    render() {
        if (!this.ctx) return;
        
        // 清空画布
        this.ctx.fillStyle = '#000';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 渲染背景效果
        this.renderBackground();

        // 渲染流星效果
        this.renderMeteors();

        // 渲染光点
        this.renderSparks();

        // 渲染粒子效果
        this.renderParticles();

        // 渲染UI元素
        this.renderGameUI();
    }

    /**
     * 渲染背景 - 现代化动态背景
     */
    renderBackground() {
        // 动态星空背景
        this.ctx.save();

        // 创建动态渐变背景
        const time = this.gameTime * 0.001;
        const gradient = this.ctx.createRadialGradient(
            this.canvas.width * 0.5, this.canvas.height * 0.5, 0,
            this.canvas.width * 0.5, this.canvas.height * 0.5, Math.max(this.canvas.width, this.canvas.height) * 0.7
        );

        gradient.addColorStop(0, `rgba(0, 212, 255, ${0.05 + Math.sin(time) * 0.02})`);
        gradient.addColorStop(0.5, `rgba(179, 71, 217, ${0.03 + Math.cos(time * 0.7) * 0.02})`);
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0.1)');

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 动态星点
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        for (let i = 0; i < 80; i++) {
            const x = (i * 137.5 + time * 10) % this.canvas.width;
            const y = (i * 73.3 + time * 5) % this.canvas.height;
            const twinkle = Math.sin(time * 2 + i) * 0.5 + 0.5;

            this.ctx.globalAlpha = twinkle * 0.6;
            this.ctx.fillRect(x, y, 1 + twinkle, 1 + twinkle);
        }

        // 添加流星效果
        if (Math.random() < 0.002) {
            this.createMeteor();
        }

        this.ctx.restore();
    }

    /**
     * 创建流星效果
     */
    createMeteor() {
        const meteor = {
            x: Math.random() * this.canvas.width,
            y: -10,
            vx: (Math.random() - 0.5) * 2,
            vy: Math.random() * 3 + 2,
            life: 1,
            decay: 0.02
        };

        if (!this.meteors) this.meteors = [];
        this.meteors.push(meteor);
    }

    /**
     * 更新流星效果
     */
    updateMeteors() {
        if (!this.meteors) return;

        this.meteors.forEach((meteor, index) => {
            meteor.x += meteor.vx;
            meteor.y += meteor.vy;
            meteor.life -= meteor.decay;

            // 移除消失的流星
            if (meteor.life <= 0 || meteor.y > this.canvas.height + 10) {
                this.meteors.splice(index, 1);
            }
        });
    }

    /**
     * 渲染流星效果
     */
    renderMeteors() {
        if (!this.meteors) return;

        this.meteors.forEach(meteor => {
            this.ctx.save();

            // 创建流星尾迹渐变
            const gradient = this.ctx.createLinearGradient(
                meteor.x - meteor.vx * 10, meteor.y - meteor.vy * 10,
                meteor.x, meteor.y
            );
            gradient.addColorStop(0, 'rgba(255, 255, 255, 0)');
            gradient.addColorStop(1, `rgba(255, 255, 255, ${meteor.life * 0.8})`);

            this.ctx.strokeStyle = gradient;
            this.ctx.lineWidth = 2;
            this.ctx.lineCap = 'round';

            this.ctx.beginPath();
            this.ctx.moveTo(meteor.x - meteor.vx * 15, meteor.y - meteor.vy * 15);
            this.ctx.lineTo(meteor.x, meteor.y);
            this.ctx.stroke();

            // 流星头部
            this.ctx.fillStyle = `rgba(255, 255, 255, ${meteor.life})`;
            this.ctx.beginPath();
            this.ctx.arc(meteor.x, meteor.y, 2, 0, Math.PI * 2);
            this.ctx.fill();

            this.ctx.restore();
        });
    }

    /**
     * 渲染光点 - 现代化增强版本
     */
    renderSparks() {
        this.sparks.forEach(spark => {
            this.ctx.save();

            // 脉冲效果
            const pulse = Math.sin(spark.pulsePhase) * 0.3 + 1;
            const size = spark.size * pulse * spark.life;

            // 增强发光效果
            if (spark.glowIntensity > 0) {
                // 多层发光效果
                for (let i = 0; i < 3; i++) {
                    this.ctx.shadowColor = spark.color;
                    this.ctx.shadowBlur = spark.glowIntensity * (30 - i * 8);
                    this.ctx.globalAlpha = spark.life * (0.3 - i * 0.1);

                    this.ctx.fillStyle = spark.color;
                    this.ctx.beginPath();
                    this.ctx.arc(spark.x, spark.y, size * (1.2 - i * 0.2), 0, Math.PI * 2);
                    this.ctx.fill();
                }
            }

            // 重置阴影
            this.ctx.shadowBlur = 0;

            // 绘制主光点 - 渐变效果
            const gradient = this.ctx.createRadialGradient(
                spark.x, spark.y, 0,
                spark.x, spark.y, size
            );
            gradient.addColorStop(0, '#FFFFFF');
            gradient.addColorStop(0.3, spark.color);
            gradient.addColorStop(1, 'transparent');

            this.ctx.fillStyle = gradient;
            this.ctx.globalAlpha = spark.life;
            this.ctx.beginPath();
            this.ctx.arc(spark.x, spark.y, size, 0, Math.PI * 2);
            this.ctx.fill();

            // 绘制内核 - 更亮的中心
            this.ctx.fillStyle = '#FFFFFF';
            this.ctx.globalAlpha = spark.life * 0.9;
            this.ctx.beginPath();
            this.ctx.arc(spark.x, spark.y, size * 0.2, 0, Math.PI * 2);
            this.ctx.fill();

            // 完美阶段的特殊效果
            if (spark.phase === 'perfect') {
                this.ctx.strokeStyle = spark.color;
                this.ctx.lineWidth = 2;
                this.ctx.globalAlpha = spark.life * 0.6;
                this.ctx.beginPath();
                this.ctx.arc(spark.x, spark.y, size * 1.5, 0, Math.PI * 2);
                this.ctx.stroke();
            }

            this.ctx.restore();
        });
    }

    /**
     * 渲染粒子效果
     */
    renderParticles() {
        this.particles.forEach(particle => {
            this.ctx.save();
            this.ctx.fillStyle = particle.color;
            this.ctx.globalAlpha = particle.life;
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();
        });
    }

    /**
     * 渲染游戏UI
     */
    renderGameUI() {
        // 连击显示
        if (this.combo > 1) {
            this.ctx.save();
            this.ctx.fillStyle = '#FFD700';
            this.ctx.font = 'bold 24px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(`连击 x${this.combo}`, this.canvas.width / 2, 50);
            this.ctx.restore();
        }
    }

    /**
     * 更新UI显示
     */
    updateUI() {
        const scoreElement = document.getElementById('current-score');
        const levelElement = document.getElementById('current-level');
        const livesElement = document.getElementById('current-lives');
        
        if (scoreElement) scoreElement.textContent = this.score;
        if (levelElement) levelElement.textContent = this.level;
        if (livesElement) livesElement.textContent = this.lives;
    }

    /**
     * 更新提示信息
     */
    updateHint(messageKey) {
        const hintElement = document.getElementById('hint-text');
        if (hintElement) {
            hintElement.textContent = i18nService.t(messageKey);
        }
    }

    /**
     * 显示分数提交结果
     */
    showSubmissionResults(results) {
        const successfulSubmissions = results.filter(r => r && r.success);
        const failedSubmissions = results.filter(r => r && !r.success);

        if (successfulSubmissions.length > 0) {
            const bestRank = Math.min(...successfulSubmissions.map(r => r.rank).filter(r => r > 0));

            if (window.uiManager) {
                if (bestRank <= 10) {
                    uiManager.showToast(`🏆 恭喜！您进入了前${bestRank}名！`, 'success');
                } else if (bestRank <= 100) {
                    uiManager.showToast(`🎉 分数已提交，排名第${bestRank}位！`, 'success');
                } else {
                    uiManager.showToast('✅ 分数已成功提交到排行榜！', 'success');
                }
            }
        }

        if (failedSubmissions.length > 0) {
            const reasons = failedSubmissions.map(r => r.reason);

            if (reasons.includes('validation_failed') || reasons.includes('high_risk')) {
                if (window.uiManager) {
                    uiManager.showToast('⚠️ 分数验证失败，请确保游戏过程正常', 'warning');
                }
            } else if (reasons.includes('custom_level_restricted')) {
                if (window.uiManager) {
                    uiManager.showToast('ℹ️ 自定义关卡分数已记录，但不参与官方排行榜', 'info');
                }
            }
        }
    }

    /**
     * 显示游戏结束界面
     */
    showGameOverScreen() {
        const finalScoreElement = document.getElementById('final-score-value');
        if (finalScoreElement) {
            finalScoreElement.textContent = this.score;
        }
        
        const gameOverScreen = document.getElementById('game-over');
        if (gameOverScreen) {
            gameOverScreen.classList.remove('hidden');
        }
    }

    // 输入处理方法
    handleMouseDown(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        this.handleClick(x, y);
    }

    handleMouseMove(e) {
        const rect = this.canvas.getBoundingClientRect();
        this.inputState.mouseX = e.clientX - rect.left;
        this.inputState.mouseY = e.clientY - rect.top;
    }

    handleMouseUp(e) {
        this.inputState.isPressed = false;
    }

    handleTouchStart(e) {
        e.preventDefault();
        const rect = this.canvas.getBoundingClientRect();
        const touch = e.touches[0];
        const x = touch.clientX - rect.left;
        const y = touch.clientY - rect.top;
        this.handleClick(x, y);
    }

    handleTouchMove(e) {
        e.preventDefault();
    }

    handleTouchEnd(e) {
        e.preventDefault();
        this.inputState.touchActive = false;
    }
}

// 创建全局游戏引擎实例
window.gameEngine = new GameEngine();
