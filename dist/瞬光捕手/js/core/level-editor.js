/**
 * 瞬光捕手 - 关卡编辑器
 * 提供可视化的关卡创建和编辑功能
 */

class LevelEditor {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.isEditing = false;
        this.currentLevel = null;
        this.selectedTool = 'spark';
        this.isPlaying = false;
        this.previewEngine = null;
        
        // 编辑器状态
        this.editorState = {
            zoom: 1.0,
            offsetX: 0,
            offsetY: 0,
            gridSize: 20,
            showGrid: true,
            snapToGrid: true
        };
        
        // 工具类型
        this.tools = {
            SPARK: 'spark',
            OBSTACLE: 'obstacle',
            POWERUP: 'powerup',
            TRIGGER: 'trigger',
            ERASER: 'eraser',
            SELECT: 'select'
        };
        
        // 光点类型
        this.sparkTypes = {
            NORMAL: { color: '#4ecdc4', speed: 1.0, points: 100, size: 8 },
            FAST: { color: '#ff6b6b', speed: 2.0, points: 200, size: 6 },
            SLOW: { color: '#95e1d3', speed: 0.5, points: 50, size: 12 },
            BONUS: { color: '#ffd93d', speed: 1.5, points: 500, size: 10 },
            PERFECT: { color: '#ff9ff3', speed: 1.2, points: 1000, size: 8 }
        };
        
        // 障碍物类型
        this.obstacleTypes = {
            WALL: { color: '#666666', solid: true, width: 20, height: 100 },
            MOVING_WALL: { color: '#888888', solid: true, width: 20, height: 100, moves: true },
            SPIKE: { color: '#ff4757', deadly: true, width: 30, height: 30 }
        };
        
        // 道具类型
        this.powerupTypes = {
            SLOW_TIME: { color: '#74b9ff', effect: 'slowTime', duration: 5000 },
            DOUBLE_SCORE: { color: '#fdcb6e', effect: 'doubleScore', duration: 10000 },
            SHIELD: { color: '#00b894', effect: 'shield', duration: 8000 },
            MULTI_HIT: { color: '#e17055', effect: 'multiHit', duration: 6000 }
        };
        
        // 当前选中的对象
        this.selectedObjects = [];
        this.dragStart = null;
        this.isDragging = false;
        
        // 历史记录（撤销/重做）
        this.history = [];
        this.historyIndex = -1;
        this.maxHistorySize = 50;
    }

    /**
     * 初始化关卡编辑器
     */
    async init() {
        try {
            this.canvas = document.getElementById('level-editor-canvas');
            if (!this.canvas) {
                console.error('找不到关卡编辑器画布');
                return false;
            }
            
            this.ctx = this.canvas.getContext('2d');
            this.setupCanvas();
            this.bindEvents();
            this.setupToolbar();
            
            console.log('关卡编辑器初始化完成');
            return true;
        } catch (error) {
            console.error('关卡编辑器初始化失败:', error);
            return false;
        }
    }

    /**
     * 设置画布
     */
    setupCanvas() {
        const container = this.canvas.parentElement;
        this.canvas.width = container.clientWidth;
        this.canvas.height = container.clientHeight;
        
        // 设置画布样式
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.lineCap = 'round';
        this.ctx.lineJoin = 'round';
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 鼠标事件
        this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        this.canvas.addEventListener('wheel', (e) => this.handleWheel(e));
        
        // 触摸事件
        this.canvas.addEventListener('touchstart', (e) => this.handleTouchStart(e));
        this.canvas.addEventListener('touchmove', (e) => this.handleTouchMove(e));
        this.canvas.addEventListener('touchend', (e) => this.handleTouchEnd(e));
        
        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        
        // 窗口大小变化
        window.addEventListener('resize', () => this.handleResize());
    }

    /**
     * 设置工具栏
     */
    setupToolbar() {
        // 工具按钮 - 使用触摸友好的事件处理
        const toolButtons = document.querySelectorAll('.tool-btn');
        toolButtons.forEach(button => {
            this.addTouchFriendlyEvents(button, () => {
                this.selectTool(button.dataset.tool);
                this.updateToolbarUI();
            });
        });

        // 属性面板
        this.setupPropertyPanel();

        // 关卡设置
        this.setupLevelSettings();
    }

    /**
     * 为按钮添加触摸友好的事件处理
     */
    addTouchFriendlyEvents(element, callback) {
        if (window.touchHelper) {
            // 使用全局TouchHelper工具
            window.touchHelper.addTouchFriendlyEvents(element, callback);
        } else {
            // 降级到基本的点击事件
            console.warn('⚠️ TouchHelper未加载，使用基本点击事件');
            element.addEventListener('click', callback);
        }
    }

    /**
     * 设置属性面板
     */
    setupPropertyPanel() {
        const propertyPanel = document.getElementById('property-panel');
        if (!propertyPanel) return;
        
        // 根据选中的工具显示不同的属性
        this.updatePropertyPanel();
    }

    /**
     * 设置关卡设置面板
     */
    setupLevelSettings() {
        const settingsPanel = document.getElementById('level-settings');
        if (!settingsPanel) return;
        
        // 关卡名称
        const nameInput = settingsPanel.querySelector('#level-name');
        if (nameInput) {
            nameInput.addEventListener('input', (e) => {
                if (this.currentLevel) {
                    this.currentLevel.name = e.target.value;
                    this.markAsModified();
                }
            });
        }
        
        // 关卡描述
        const descInput = settingsPanel.querySelector('#level-description');
        if (descInput) {
            descInput.addEventListener('input', (e) => {
                if (this.currentLevel) {
                    this.currentLevel.description = e.target.value;
                    this.markAsModified();
                }
            });
        }
        
        // 难度设置
        const difficultySelect = settingsPanel.querySelector('#level-difficulty');
        if (difficultySelect) {
            difficultySelect.addEventListener('change', (e) => {
                if (this.currentLevel) {
                    this.currentLevel.difficulty = e.target.value;
                    this.markAsModified();
                }
            });
        }
    }

    /**
     * 创建新关卡
     */
    createNewLevel() {
        this.currentLevel = {
            id: this.generateLevelId(),
            name: i18nService.t('editor.newLevel'),
            description: '',
            difficulty: 'normal',
            author: playerManager.getCurrentPlayer().name,
            createdAt: Date.now(),
            modifiedAt: Date.now(),
            version: '1.0',
            objects: [],
            settings: {
                timeLimit: 60,
                targetScore: 1000,
                backgroundColor: '#1a1a2e',
                music: 'default'
            }
        };
        
        this.clearHistory();
        this.saveToHistory();
        this.updateLevelSettingsUI();
        this.render();
        
        console.log('创建新关卡:', this.currentLevel.name);
    }

    /**
     * 加载关卡
     */
    async loadLevel(levelId) {
        try {
            const level = await levelManager.getLevel(levelId);
            if (!level) {
                throw new Error('关卡不存在');
            }
            
            this.currentLevel = { ...level };
            this.clearHistory();
            this.saveToHistory();
            this.updateLevelSettingsUI();
            this.render();
            
            console.log('加载关卡:', this.currentLevel.name);
            return true;
        } catch (error) {
            console.error('加载关卡失败:', error);
            return false;
        }
    }

    /**
     * 保存关卡
     */
    async saveLevel() {
        if (!this.currentLevel) {
            console.error('没有可保存的关卡');
            return false;
        }
        
        try {
            this.currentLevel.modifiedAt = Date.now();

            // 如果关卡已存在则更新，否则创建新关卡
            if (this.currentLevel.id && levelManager.customLevels.has(this.currentLevel.id)) {
                await levelManager.updateCustomLevel(this.currentLevel.id, this.currentLevel);
            } else {
                const levelId = await levelManager.createCustomLevel(this.currentLevel);
                if (levelId) {
                    this.currentLevel.id = levelId;
                }
            }
            
            console.log('关卡保存成功:', this.currentLevel.name);
            return true;
        } catch (error) {
            console.error('保存关卡失败:', error);
            return false;
        }
    }

    /**
     * 测试关卡
     */
    async testLevel() {
        if (!this.currentLevel || this.currentLevel.objects.length === 0) {
            alert(i18nService.t('editor.noObjectsToTest'));
            return;
        }
        
        try {
            // 保存当前关卡到临时存储
            const tempLevel = { ...this.currentLevel, id: 'temp_test_level' };
            await levelManager.createCustomLevel(tempLevel);
            
            // 切换到游戏界面并加载测试关卡
            this.isPlaying = true;
            screenManager.showScreen('game-screen');
            await gameEngine.loadCustomLevel(tempLevel);
            gameEngine.startGame();
            
            console.log('开始测试关卡:', this.currentLevel.name);
        } catch (error) {
            console.error('测试关卡失败:', error);
            alert(i18nService.t('editor.testFailed'));
        }
    }

    /**
     * 发布关卡
     */
    async publishLevel() {
        if (!this.currentLevel) {
            console.error('没有可发布的关卡');
            return false;
        }
        
        // 验证关卡
        const validation = this.validateLevel();
        if (!validation.valid) {
            alert(i18nService.t('editor.validationFailed') + ': ' + validation.errors.join(', '));
            return false;
        }
        
        try {
            // 先保存关卡（如果还没保存的话）
            if (!this.currentLevel.id) {
                const levelId = await levelManager.createCustomLevel(this.currentLevel);
                if (levelId) {
                    this.currentLevel.id = levelId;
                }
            }

            // 发布关卡
            await levelManager.publishLevel(this.currentLevel.id);
            
            console.log('关卡发布成功:', this.currentLevel.name);
            alert(i18nService.t('editor.publishSuccess'));
            return true;
        } catch (error) {
            console.error('发布关卡失败:', error);
            alert(i18nService.t('editor.publishFailed'));
            return false;
        }
    }

    /**
     * 验证关卡
     */
    validateLevel() {
        const errors = [];
        
        if (!this.currentLevel) {
            errors.push(i18nService.t('editor.noLevel'));
            return { valid: false, errors };
        }
        
        if (!this.currentLevel.name || this.currentLevel.name.trim() === '') {
            errors.push(i18nService.t('editor.nameRequired'));
        }
        
        if (this.currentLevel.objects.length === 0) {
            errors.push(i18nService.t('editor.noObjects'));
        }
        
        // 检查是否有光点
        const sparks = this.currentLevel.objects.filter(obj => obj.type === 'spark');
        if (sparks.length === 0) {
            errors.push(i18nService.t('editor.noSparks'));
        }
        
        // 检查关卡边界
        const outOfBounds = this.currentLevel.objects.filter(obj => 
            obj.x < 0 || obj.y < 0 || obj.x > this.canvas.width || obj.y > this.canvas.height
        );
        if (outOfBounds.length > 0) {
            errors.push(i18nService.t('editor.objectsOutOfBounds'));
        }
        
        return { valid: errors.length === 0, errors };
    }

    /**
     * 生成关卡ID
     */
    generateLevelId() {
        return 'level_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 选择工具
     */
    selectTool(tool) {
        this.selectedTool = tool;
        this.selectedObjects = [];
        this.updatePropertyPanel();
        console.log('选择工具:', tool);
    }

    /**
     * 更新工具栏UI
     */
    updateToolbarUI() {
        const toolButtons = document.querySelectorAll('.tool-btn');
        toolButtons.forEach(button => {
            button.classList.toggle('active', button.dataset.tool === this.selectedTool);
        });
    }

    /**
     * 更新属性面板
     */
    updatePropertyPanel() {
        const propertyPanel = document.getElementById('property-panel');
        if (!propertyPanel) return;
        
        let html = '';
        
        switch (this.selectedTool) {
            case this.tools.SPARK:
                html = this.generateSparkProperties();
                break;
            case this.tools.OBSTACLE:
                html = this.generateObstacleProperties();
                break;
            case this.tools.POWERUP:
                html = this.generatePowerupProperties();
                break;
            default:
                html = '<p data-i18n="editor.selectTool">请选择工具</p>';
        }
        
        propertyPanel.innerHTML = html;
        i18nService.applyTranslations();
    }

    /**
     * 生成光点属性面板
     */
    generateSparkProperties() {
        let html = '<h3 data-i18n="editor.sparkProperties">光点属性</h3>';
        
        Object.keys(this.sparkTypes).forEach(type => {
            const spark = this.sparkTypes[type];
            html += `
                <div class="property-item">
                    <input type="radio" name="spark-type" value="${type}" id="spark-${type}">
                    <label for="spark-${type}">
                        <span class="color-preview" style="background-color: ${spark.color}"></span>
                        ${i18nService.t('editor.spark.' + type.toLowerCase())}
                        (${spark.points}分)
                    </label>
                </div>
            `;
        });
        
        return html;
    }

    /**
     * 生成障碍物属性面板
     */
    generateObstacleProperties() {
        let html = '<h3 data-i18n="editor.obstacleProperties">障碍物属性</h3>';
        
        Object.keys(this.obstacleTypes).forEach(type => {
            const obstacle = this.obstacleTypes[type];
            html += `
                <div class="property-item">
                    <input type="radio" name="obstacle-type" value="${type}" id="obstacle-${type}">
                    <label for="obstacle-${type}">
                        <span class="color-preview" style="background-color: ${obstacle.color}"></span>
                        ${i18nService.t('editor.obstacle.' + type.toLowerCase())}
                    </label>
                </div>
            `;
        });
        
        return html;
    }

    /**
     * 生成道具属性面板
     */
    generatePowerupProperties() {
        let html = '<h3 data-i18n="editor.powerupProperties">道具属性</h3>';
        
        Object.keys(this.powerupTypes).forEach(type => {
            const powerup = this.powerupTypes[type];
            html += `
                <div class="property-item">
                    <input type="radio" name="powerup-type" value="${type}" id="powerup-${type}">
                    <label for="powerup-${type}">
                        <span class="color-preview" style="background-color: ${powerup.color}"></span>
                        ${i18nService.t('editor.powerup.' + type.toLowerCase())}
                    </label>
                </div>
            `;
        });
        
        return html;
    }

    /**
     * 更新关卡设置UI
     */
    updateLevelSettingsUI() {
        if (!this.currentLevel) return;
        
        const nameInput = document.getElementById('level-name');
        if (nameInput) nameInput.value = this.currentLevel.name;
        
        const descInput = document.getElementById('level-description');
        if (descInput) descInput.value = this.currentLevel.description;
        
        const difficultySelect = document.getElementById('level-difficulty');
        if (difficultySelect) difficultySelect.value = this.currentLevel.difficulty;
    }

    /**
     * 标记为已修改
     */
    markAsModified() {
        if (this.currentLevel) {
            this.currentLevel.modifiedAt = Date.now();
        }
    }

    /**
     * 保存到历史记录
     */
    saveToHistory() {
        if (!this.currentLevel) return;
        
        // 移除当前位置之后的历史记录
        this.history = this.history.slice(0, this.historyIndex + 1);
        
        // 添加新的历史记录
        this.history.push(JSON.parse(JSON.stringify(this.currentLevel)));
        this.historyIndex++;
        
        // 限制历史记录大小
        if (this.history.length > this.maxHistorySize) {
            this.history.shift();
            this.historyIndex--;
        }
    }

    /**
     * 清空历史记录
     */
    clearHistory() {
        this.history = [];
        this.historyIndex = -1;
    }

    /**
     * 撤销
     */
    undo() {
        if (this.historyIndex > 0) {
            this.historyIndex--;
            this.currentLevel = JSON.parse(JSON.stringify(this.history[this.historyIndex]));
            this.updateLevelSettingsUI();
            this.render();
            console.log('撤销操作');
        }
    }

    /**
     * 重做
     */
    redo() {
        if (this.historyIndex < this.history.length - 1) {
            this.historyIndex++;
            this.currentLevel = JSON.parse(JSON.stringify(this.history[this.historyIndex]));
            this.updateLevelSettingsUI();
            this.render();
            console.log('重做操作');
        }
    }

    /**
     * 渲染编辑器
     */
    render() {
        if (!this.ctx || !this.canvas) return;

        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制背景
        this.renderBackground();

        // 绘制网格
        if (this.editorState.showGrid) {
            this.renderGrid();
        }

        // 绘制关卡对象
        if (this.currentLevel && this.currentLevel.objects) {
            this.renderLevelObjects();
        }

        // 绘制选中对象的高亮
        this.renderSelection();

        // 绘制工具预览
        this.renderToolPreview();
    }

    /**
     * 渲染背景
     */
    renderBackground() {
        const bgColor = this.currentLevel?.settings?.backgroundColor || '#1a1a2e';
        this.ctx.fillStyle = bgColor;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    /**
     * 渲染网格
     */
    renderGrid() {
        const gridSize = this.editorState.gridSize * this.editorState.zoom;

        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();

        // 垂直线
        for (let x = 0; x < this.canvas.width; x += gridSize) {
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
        }

        // 水平线
        for (let y = 0; y < this.canvas.height; y += gridSize) {
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
        }

        this.ctx.stroke();
    }

    /**
     * 渲染关卡对象
     */
    renderLevelObjects() {
        this.currentLevel.objects.forEach(obj => {
            this.renderObject(obj);
        });
    }

    /**
     * 渲染单个对象
     */
    renderObject(obj) {
        this.ctx.save();

        switch (obj.type) {
            case 'spark':
                this.renderSpark(obj);
                break;
            case 'obstacle':
                this.renderObstacle(obj);
                break;
            case 'powerup':
                this.renderPowerup(obj);
                break;
            case 'trigger':
                this.renderTrigger(obj);
                break;
        }

        this.ctx.restore();
    }

    /**
     * 渲染光点
     */
    renderSpark(spark) {
        const sparkType = this.sparkTypes[spark.subType] || this.sparkTypes.NORMAL;

        this.ctx.fillStyle = sparkType.color;
        this.ctx.beginPath();
        this.ctx.arc(spark.x, spark.y, sparkType.size, 0, Math.PI * 2);
        this.ctx.fill();

        // 绘制光晕效果
        const gradient = this.ctx.createRadialGradient(
            spark.x, spark.y, 0,
            spark.x, spark.y, sparkType.size * 2
        );
        gradient.addColorStop(0, this.addAlphaToColor(sparkType.color, 0.5));
        gradient.addColorStop(1, this.addAlphaToColor(sparkType.color, 0));

        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(spark.x, spark.y, sparkType.size * 2, 0, Math.PI * 2);
        this.ctx.fill();
    }

    /**
     * 渲染障碍物
     */
    renderObstacle(obstacle) {
        const obstacleType = this.obstacleTypes[obstacle.subType] || this.obstacleTypes.WALL;

        this.ctx.fillStyle = obstacleType.color;
        this.ctx.fillRect(
            obstacle.x - obstacleType.width / 2,
            obstacle.y - obstacleType.height / 2,
            obstacleType.width,
            obstacleType.height
        );

        // 如果是移动障碍物，绘制移动路径
        if (obstacleType.moves && obstacle.path) {
            this.ctx.strokeStyle = this.addAlphaToColor(obstacleType.color, 0.5);
            this.ctx.lineWidth = 2;
            this.ctx.setLineDash([5, 5]);
            this.ctx.beginPath();
            this.ctx.moveTo(obstacle.path[0].x, obstacle.path[0].y);
            for (let i = 1; i < obstacle.path.length; i++) {
                this.ctx.lineTo(obstacle.path[i].x, obstacle.path[i].y);
            }
            this.ctx.stroke();
            this.ctx.setLineDash([]);
        }
    }

    /**
     * 渲染道具
     */
    renderPowerup(powerup) {
        const powerupType = this.powerupTypes[powerup.subType] || this.powerupTypes.SLOW_TIME;

        this.ctx.fillStyle = powerupType.color;
        this.ctx.fillRect(powerup.x - 15, powerup.y - 15, 30, 30);

        // 绘制道具图标
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '16px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';

        let icon = '?';
        switch (powerup.subType) {
            case 'SLOW_TIME': icon = '⏰'; break;
            case 'DOUBLE_SCORE': icon = '×2'; break;
            case 'SHIELD': icon = '🛡'; break;
            case 'MULTI_HIT': icon = '⚡'; break;
        }

        this.ctx.fillText(icon, powerup.x, powerup.y);
    }

    /**
     * 渲染触发器
     */
    renderTrigger(trigger) {
        this.ctx.strokeStyle = '#ff9ff3';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([3, 3]);
        this.ctx.strokeRect(trigger.x - 25, trigger.y - 25, 50, 50);
        this.ctx.setLineDash([]);

        this.ctx.fillStyle = '#ff9ff3';
        this.ctx.font = '12px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('T', trigger.x, trigger.y);
    }

    /**
     * 渲染选中对象的高亮
     */
    renderSelection() {
        this.selectedObjects.forEach(obj => {
            this.ctx.strokeStyle = '#ffff00';
            this.ctx.lineWidth = 2;
            this.ctx.setLineDash([5, 5]);

            const bounds = this.getObjectBounds(obj);
            this.ctx.strokeRect(
                bounds.x - 5,
                bounds.y - 5,
                bounds.width + 10,
                bounds.height + 10
            );

            this.ctx.setLineDash([]);
        });
    }

    /**
     * 渲染工具预览
     */
    renderToolPreview() {
        // 在鼠标位置显示当前工具的预览
        if (this.mousePos && this.selectedTool !== this.tools.SELECT && this.selectedTool !== this.tools.ERASER) {
            this.ctx.globalAlpha = 0.5;

            const previewObj = {
                x: this.mousePos.x,
                y: this.mousePos.y,
                type: this.selectedTool,
                subType: this.getSelectedSubType()
            };

            this.renderObject(previewObj);
            this.ctx.globalAlpha = 1.0;
        }
    }

    /**
     * 获取对象边界
     */
    getObjectBounds(obj) {
        let width = 20, height = 20;

        switch (obj.type) {
            case 'spark':
                const sparkType = this.sparkTypes[obj.subType] || this.sparkTypes.NORMAL;
                width = height = sparkType.size * 2;
                break;
            case 'obstacle':
                const obstacleType = this.obstacleTypes[obj.subType] || this.obstacleTypes.WALL;
                width = obstacleType.width;
                height = obstacleType.height;
                break;
            case 'powerup':
                width = height = 30;
                break;
            case 'trigger':
                width = height = 50;
                break;
        }

        return {
            x: obj.x - width / 2,
            y: obj.y - height / 2,
            width: width,
            height: height
        };
    }

    /**
     * 获取当前选中的子类型
     */
    getSelectedSubType() {
        const propertyPanel = document.getElementById('property-panel');
        if (!propertyPanel) return null;

        const checkedRadio = propertyPanel.querySelector('input[type="radio"]:checked');
        return checkedRadio ? checkedRadio.value : null;
    }

    /**
     * 鼠标按下事件
     */
    handleMouseDown(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        this.mousePos = { x, y };
        this.dragStart = { x, y };
        this.isDragging = false;

        switch (this.selectedTool) {
            case this.tools.SELECT:
                this.handleSelectMouseDown(x, y);
                break;
            case this.tools.ERASER:
                this.handleEraserMouseDown(x, y);
                break;
            default:
                this.handleCreateMouseDown(x, y);
                break;
        }
    }

    /**
     * 鼠标移动事件
     */
    handleMouseMove(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        this.mousePos = { x, y };

        if (this.dragStart) {
            const dx = x - this.dragStart.x;
            const dy = y - this.dragStart.y;

            if (Math.abs(dx) > 5 || Math.abs(dy) > 5) {
                this.isDragging = true;
            }

            if (this.isDragging && this.selectedTool === this.tools.SELECT) {
                this.handleDragMove(dx, dy);
            }
        }

        this.render();
    }

    /**
     * 鼠标抬起事件
     */
    handleMouseUp(e) {
        if (this.isDragging && this.selectedTool === this.tools.SELECT) {
            this.saveToHistory();
        }

        this.dragStart = null;
        this.isDragging = false;
    }

    /**
     * 处理选择工具的鼠标按下
     */
    handleSelectMouseDown(x, y) {
        const clickedObject = this.getObjectAt(x, y);

        if (clickedObject) {
            if (!this.selectedObjects.includes(clickedObject)) {
                this.selectedObjects = [clickedObject];
            }
        } else {
            this.selectedObjects = [];
        }

        this.render();
    }

    /**
     * 处理橡皮擦工具的鼠标按下
     */
    handleEraserMouseDown(x, y) {
        const objectToDelete = this.getObjectAt(x, y);

        if (objectToDelete && this.currentLevel) {
            const index = this.currentLevel.objects.indexOf(objectToDelete);
            if (index > -1) {
                this.currentLevel.objects.splice(index, 1);
                this.selectedObjects = this.selectedObjects.filter(obj => obj !== objectToDelete);
                this.saveToHistory();
                this.markAsModified();
                this.render();
            }
        }
    }

    /**
     * 处理创建工具的鼠标按下
     */
    handleCreateMouseDown(x, y) {
        if (!this.currentLevel) return;

        const subType = this.getSelectedSubType();
        if (!subType) {
            alert(i18nService.t('editor.selectSubType'));
            return;
        }

        // 对齐到网格
        if (this.editorState.snapToGrid) {
            x = Math.round(x / this.editorState.gridSize) * this.editorState.gridSize;
            y = Math.round(y / this.editorState.gridSize) * this.editorState.gridSize;
        }

        const newObject = {
            id: this.generateObjectId(),
            type: this.selectedTool,
            subType: subType,
            x: x,
            y: y,
            createdAt: Date.now()
        };

        // 添加特定类型的属性
        this.addTypeSpecificProperties(newObject);

        this.currentLevel.objects.push(newObject);
        this.saveToHistory();
        this.markAsModified();
        this.render();
    }

    /**
     * 添加特定类型的属性
     */
    addTypeSpecificProperties(obj) {
        switch (obj.type) {
            case 'obstacle':
                if (obj.subType === 'MOVING_WALL') {
                    obj.path = [
                        { x: obj.x, y: obj.y },
                        { x: obj.x + 100, y: obj.y }
                    ];
                    obj.speed = 50;
                }
                break;
            case 'trigger':
                obj.action = 'spawn_sparks';
                obj.parameters = { count: 3, type: 'NORMAL' };
                break;
        }
    }

    /**
     * 处理拖拽移动
     */
    handleDragMove(dx, dy) {
        this.selectedObjects.forEach(obj => {
            obj.x += dx;
            obj.y += dy;

            // 对齐到网格
            if (this.editorState.snapToGrid) {
                obj.x = Math.round(obj.x / this.editorState.gridSize) * this.editorState.gridSize;
                obj.y = Math.round(obj.y / this.editorState.gridSize) * this.editorState.gridSize;
            }
        });

        this.markAsModified();
        this.dragStart = { x: this.mousePos.x, y: this.mousePos.y };
    }

    /**
     * 获取指定位置的对象
     */
    getObjectAt(x, y) {
        if (!this.currentLevel || !this.currentLevel.objects) return null;

        // 从后往前查找（后绘制的在上层）
        for (let i = this.currentLevel.objects.length - 1; i >= 0; i--) {
            const obj = this.currentLevel.objects[i];
            const bounds = this.getObjectBounds(obj);

            if (x >= bounds.x && x <= bounds.x + bounds.width &&
                y >= bounds.y && y <= bounds.y + bounds.height) {
                return obj;
            }
        }

        return null;
    }

    /**
     * 生成对象ID
     */
    generateObjectId() {
        return 'obj_' + Date.now() + '_' + Math.random().toString(36).substr(2, 6);
    }

    /**
     * 键盘事件处理
     */
    handleKeyDown(e) {
        if (!this.isEditing) return;

        switch (e.key) {
            case 'Delete':
            case 'Backspace':
                this.deleteSelectedObjects();
                break;
            case 'z':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    if (e.shiftKey) {
                        this.redo();
                    } else {
                        this.undo();
                    }
                }
                break;
            case 'a':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    this.selectAllObjects();
                }
                break;
            case 'Escape':
                this.selectedObjects = [];
                this.render();
                break;
        }
    }

    /**
     * 删除选中的对象
     */
    deleteSelectedObjects() {
        if (this.selectedObjects.length === 0 || !this.currentLevel) return;

        this.selectedObjects.forEach(obj => {
            const index = this.currentLevel.objects.indexOf(obj);
            if (index > -1) {
                this.currentLevel.objects.splice(index, 1);
            }
        });

        this.selectedObjects = [];
        this.saveToHistory();
        this.markAsModified();
        this.render();
    }

    /**
     * 选择所有对象
     */
    selectAllObjects() {
        if (this.currentLevel && this.currentLevel.objects) {
            this.selectedObjects = [...this.currentLevel.objects];
            this.render();
        }
    }

    /**
     * 触摸事件处理
     */
    handleTouchStart(e) {
        e.preventDefault();
        if (e.touches.length === 1) {
            const touch = e.touches[0];
            const rect = this.canvas.getBoundingClientRect();
            const x = touch.clientX - rect.left;
            const y = touch.clientY - rect.top;

            this.handleMouseDown({ clientX: touch.clientX, clientY: touch.clientY });
        }
    }

    handleTouchMove(e) {
        e.preventDefault();
        if (e.touches.length === 1) {
            const touch = e.touches[0];
            this.handleMouseMove({ clientX: touch.clientX, clientY: touch.clientY });
        }
    }

    handleTouchEnd(e) {
        e.preventDefault();
        this.handleMouseUp(e);
    }

    /**
     * 滚轮事件处理（缩放）
     */
    handleWheel(e) {
        e.preventDefault();

        const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
        const newZoom = Math.max(0.1, Math.min(5.0, this.editorState.zoom * zoomFactor));

        if (newZoom !== this.editorState.zoom) {
            this.editorState.zoom = newZoom;
            this.render();
        }
    }

    /**
     * 窗口大小变化处理
     */
    handleResize() {
        if (this.canvas && this.canvas.parentElement) {
            this.canvas.width = this.canvas.parentElement.clientWidth;
            this.canvas.height = this.canvas.parentElement.clientHeight;
            this.render();
        }
    }

    /**
     * 开始编辑
     */
    startEditing() {
        this.isEditing = true;
        this.render();
    }

    /**
     * 为颜色添加透明度
     * @param {string} color - 原始颜色（支持 hex 格式）
     * @param {number} alpha - 透明度 (0-1)
     * @returns {string} 带透明度的颜色字符串
     */
    addAlphaToColor(color, alpha) {
        try {
            // 处理十六进制颜色
            if (color.startsWith('#')) {
                const hex = color.slice(1);
                let r, g, b;

                if (hex.length === 3) {
                    r = parseInt(hex[0] + hex[0], 16);
                    g = parseInt(hex[1] + hex[1], 16);
                    b = parseInt(hex[2] + hex[2], 16);
                } else if (hex.length === 6) {
                    r = parseInt(hex.slice(0, 2), 16);
                    g = parseInt(hex.slice(2, 4), 16);
                    b = parseInt(hex.slice(4, 6), 16);
                } else {
                    throw new Error('无效的十六进制颜色格式');
                }

                return `rgba(${r}, ${g}, ${b}, ${alpha})`;
            }

            // 如果已经是带透明度的颜色，直接返回
            if (color.includes('rgba')) {
                return color;
            }

            // 默认情况下，返回原颜色
            console.warn('⚠️ 无法识别的颜色格式:', color);
            return color;

        } catch (error) {
            console.error('❌ 颜色透明度处理失败:', error, '原颜色:', color);
            return `rgba(255, 255, 255, ${alpha})`;
        }
    }

    /**
     * 停止编辑
     */
    stopEditing() {
        this.isEditing = false;
        this.selectedObjects = [];
    }
}

// 创建全局关卡编辑器实例
window.levelEditor = new LevelEditor();
