/**
 * 瞬光捕手 - 自定义KV存储服务
 * 提供统一的键值存储接口，支持多种后端适配器
 */

class StorageService {
    constructor() {
        this.adapter = null;
        this.initialized = false;
        this.init();
    }

    /**
     * 初始化存储服务
     * 自动选择最佳的存储适配器
     */
    async init() {
        try {
            // 优先尝试使用 IndexedDB
            if (this.isIndexedDBSupported()) {
                this.adapter = new IndexedDBAdapter();
                await this.adapter.init();
                console.log('存储服务：使用 IndexedDB 适配器');
            } else if (this.isLocalStorageSupported()) {
                // 回退到 localStorage
                this.adapter = new LocalStorageAdapter();
                await this.adapter.init();
                console.log('存储服务：使用 localStorage 适配器');
            } else {
                // 最后回退到内存存储
                this.adapter = new MemoryAdapter();
                await this.adapter.init();
                console.log('存储服务：使用内存适配器（数据不会持久化）');
            }
            this.initialized = true;
        } catch (error) {
            console.error('存储服务初始化失败:', error);
            // 使用内存适配器作为最后的回退
            this.adapter = new MemoryAdapter();
            await this.adapter.init();
            this.initialized = true;
        }
    }

    /**
     * 检查 IndexedDB 是否支持
     */
    isIndexedDBSupported() {
        return typeof window !== 'undefined' && 
               'indexedDB' in window && 
               indexedDB !== null;
    }

    /**
     * 检查 localStorage 是否支持
     */
    isLocalStorageSupported() {
        try {
            if (typeof window === 'undefined' || !window.localStorage) {
                return false;
            }
            const testKey = '__storage_test__';
            window.localStorage.setItem(testKey, 'test');
            window.localStorage.removeItem(testKey);
            return true;
        } catch (e) {
            return false;
        }
    }

    /**
     * 确保存储服务已初始化
     */
    async ensureInitialized() {
        if (!this.initialized) {
            await this.init();
        }
    }

    /**
     * 保存数据
     * @param {string} key - 键名
     * @param {any} value - 值
     * @returns {Promise<boolean>} 是否成功
     */
    async put(key, value) {
        await this.ensureInitialized();
        try {
            return await this.adapter.put(key, value);
        } catch (error) {
            console.error(`存储数据失败 [${key}]:`, error);
            return false;
        }
    }

    /**
     * 读取数据
     * @param {string} key - 键名
     * @param {any} defaultValue - 默认值
     * @returns {Promise<any>} 数据值
     */
    async get(key, defaultValue = null) {
        await this.ensureInitialized();
        try {
            const result = await this.adapter.get(key);
            return result !== null ? result : defaultValue;
        } catch (error) {
            console.error(`读取数据失败 [${key}]:`, error);
            return defaultValue;
        }
    }

    /**
     * 删除数据
     * @param {string} key - 键名
     * @returns {Promise<boolean>} 是否成功
     */
    async delete(key) {
        await this.ensureInitialized();
        try {
            return await this.adapter.delete(key);
        } catch (error) {
            console.error(`删除数据失败 [${key}]:`, error);
            return false;
        }
    }

    /**
     * 列出指定前缀的所有键
     * @param {string} prefix - 前缀
     * @returns {Promise<string[]>} 键名列表
     */
    async list(prefix = '') {
        await this.ensureInitialized();
        try {
            return await this.adapter.list(prefix);
        } catch (error) {
            console.error(`列出键失败 [${prefix}]:`, error);
            return [];
        }
    }

    /**
     * 清空所有数据
     * @returns {Promise<boolean>} 是否成功
     */
    async clear() {
        await this.ensureInitialized();
        try {
            return await this.adapter.clear();
        } catch (error) {
            console.error('清空数据失败:', error);
            return false;
        }
    }

    /**
     * 获取存储使用情况
     * @returns {Promise<object>} 存储信息
     */
    async getStorageInfo() {
        await this.ensureInitialized();
        try {
            return await this.adapter.getStorageInfo();
        } catch (error) {
            console.error('获取存储信息失败:', error);
            return { used: 0, available: 0, type: 'unknown' };
        }
    }
}

/**
 * IndexedDB 适配器
 */
class IndexedDBAdapter {
    constructor() {
        this.dbName = 'SplitSecondSparkDB';
        this.dbVersion = 1;
        this.storeName = 'gameData';
        this.db = null;
    }

    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                this.db = request.result;
                resolve();
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                if (!db.objectStoreNames.contains(this.storeName)) {
                    db.createObjectStore(this.storeName, { keyPath: 'key' });
                }
            };
        });
    }

    async put(key, value) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('IndexedDB not initialized'));
                return;
            }
            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            const request = store.put({ 
                key: key, 
                value: value, 
                timestamp: Date.now() 
            });
            
            request.onsuccess = () => resolve(true);
            request.onerror = () => reject(request.error);
        });
    }

    async get(key) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('IndexedDB not initialized'));
                return;
            }
            const transaction = this.db.transaction([this.storeName], 'readonly');
            const store = transaction.objectStore(this.storeName);
            const request = store.get(key);
            
            request.onsuccess = () => {
                const result = request.result;
                resolve(result ? result.value : null);
            };
            request.onerror = () => reject(request.error);
        });
    }

    async delete(key) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('IndexedDB not initialized'));
                return;
            }
            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            const request = store.delete(key);
            
            request.onsuccess = () => resolve(true);
            request.onerror = () => reject(request.error);
        });
    }

    async list(prefix) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('IndexedDB not initialized'));
                return;
            }
            const transaction = this.db.transaction([this.storeName], 'readonly');
            const store = transaction.objectStore(this.storeName);
            const request = store.getAllKeys();
            
            request.onsuccess = () => {
                const keys = request.result.filter(key => key.startsWith(prefix));
                resolve(keys);
            };
            request.onerror = () => reject(request.error);
        });
    }

    async clear() {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('IndexedDB not initialized'));
                return;
            }
            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            const request = store.clear();
            
            request.onsuccess = () => resolve(true);
            request.onerror = () => reject(request.error);
        });
    }

    async getStorageInfo() {
        if ('storage' in navigator && 'estimate' in navigator.storage) {
            const estimate = await navigator.storage.estimate();
            return {
                used: estimate.usage || 0,
                available: estimate.quota || 0,
                type: 'IndexedDB'
            };
        }
        return { used: 0, available: 0, type: 'IndexedDB' };
    }
}

/**
 * localStorage 适配器
 */
class LocalStorageAdapter {
    constructor() {
        this.prefix = 'sss_'; // Split-Second Spark prefix
    }

    async init() {
        // localStorage 不需要异步初始化
        return Promise.resolve();
    }

    async put(key, value) {
        try {
            const data = {
                value: value,
                timestamp: Date.now()
            };
            localStorage.setItem(this.prefix + key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('localStorage 存储失败:', error);
            return false;
        }
    }

    async get(key) {
        try {
            const item = localStorage.getItem(this.prefix + key);
            if (item === null) return null;
            
            const data = JSON.parse(item);
            return data.value;
        } catch (error) {
            console.error('localStorage 读取失败:', error);
            return null;
        }
    }

    async delete(key) {
        try {
            localStorage.removeItem(this.prefix + key);
            return true;
        } catch (error) {
            console.error('localStorage 删除失败:', error);
            return false;
        }
    }

    async list(prefix) {
        try {
            const keys = [];
            const fullPrefix = this.prefix + prefix;
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(fullPrefix)) {
                    keys.push(key.substring(this.prefix.length));
                }
            }
            return keys;
        } catch (error) {
            console.error('localStorage 列表失败:', error);
            return [];
        }
    }

    async clear() {
        try {
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(this.prefix)) {
                    keysToRemove.push(key);
                }
            }
            
            keysToRemove.forEach(key => localStorage.removeItem(key));
            return true;
        } catch (error) {
            console.error('localStorage 清空失败:', error);
            return false;
        }
    }

    async getStorageInfo() {
        try {
            let used = 0;
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(this.prefix)) {
                    used += key.length + (localStorage.getItem(key) || '').length;
                }
            }
            
            // localStorage 通常限制为 5-10MB
            const available = 5 * 1024 * 1024; // 假设 5MB
            return { used, available, type: 'localStorage' };
        } catch (error) {
            return { used: 0, available: 0, type: 'localStorage' };
        }
    }
}

/**
 * 内存适配器（临时存储，页面刷新后丢失）
 */
class MemoryAdapter {
    constructor() {
        this.data = new Map();
    }

    async init() {
        return Promise.resolve();
    }

    async put(key, value) {
        this.data.set(key, {
            value: value,
            timestamp: Date.now()
        });
        return true;
    }

    async get(key) {
        const item = this.data.get(key);
        return item ? item.value : null;
    }

    async delete(key) {
        return this.data.delete(key);
    }

    async list(prefix) {
        const keys = [];
        for (const key of this.data.keys()) {
            if (key.startsWith(prefix)) {
                keys.push(key);
            }
        }
        return keys;
    }

    async clear() {
        this.data.clear();
        return true;
    }

    async getStorageInfo() {
        const used = JSON.stringify([...this.data.entries()]).length;
        return { used, available: Infinity, type: 'Memory' };
    }
}

// 创建全局存储服务实例
window.storageService = new StorageService();
