/**
 * 触摸支持工具类
 * 为按钮和交互元素提供统一的触摸友好事件处理
 */
class TouchHelper {
    constructor() {
        this.touchStarted = new WeakMap();
        this.touchMoved = new WeakMap();
        this.touchTimers = new WeakMap();
        
        // 配置参数
        this.config = {
            touchMoveThreshold: 10,     // 触摸移动阈值（像素）
            touchFeedbackDelay: 100,    // 触摸反馈延迟（毫秒）
            longPressThreshold: 500,    // 长按阈值（毫秒）
            doubleTapThreshold: 300     // 双击阈值（毫秒）
        };
        
        console.log('✅ 触摸支持工具初始化完成');
    }

    /**
     * 为元素添加触摸友好的事件处理
     * @param {HTMLElement} element - 目标元素
     * @param {Function} callback - 点击回调函数
     * @param {Object} options - 配置选项
     */
    addTouchFriendlyEvents(element, callback, options = {}) {
        if (!element || typeof callback !== 'function') {
            console.warn('⚠️ TouchHelper: 无效的元素或回调函数');
            return;
        }

        const config = { ...this.config, ...options };
        
        // 初始化元素状态
        this.touchStarted.set(element, false);
        this.touchMoved.set(element, false);

        // 鼠标点击事件（桌面端）
        element.addEventListener('click', (e) => {
            if (!this.touchStarted.get(element)) {
                this.handleCallback(element, callback, e);
            }
        });

        // 触摸开始
        element.addEventListener('touchstart', (e) => {
            this.handleTouchStart(element, e, config);
        }, { passive: false });

        // 触摸移动
        element.addEventListener('touchmove', (e) => {
            this.handleTouchMove(element, e, config);
        }, { passive: false });

        // 触摸结束
        element.addEventListener('touchend', (e) => {
            this.handleTouchEnd(element, e, callback, config);
        }, { passive: false });

        // 触摸取消
        element.addEventListener('touchcancel', (e) => {
            this.handleTouchCancel(element, e);
        }, { passive: false });

        // 键盘支持（无障碍访问）
        element.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.handleCallback(element, callback, e);
            }
        });

        // 确保按钮可以获得焦点
        if (!element.hasAttribute('tabindex')) {
            element.setAttribute('tabindex', '0');
        }

        // 添加触摸友好的样式类
        element.classList.add('touch-friendly');
    }

    /**
     * 处理触摸开始
     */
    handleTouchStart(element, event, config) {
        this.touchStarted.set(element, true);
        this.touchMoved.set(element, false);

        // 添加触摸反馈
        element.classList.add('touch-active');

        // 记录初始触摸位置
        const touch = event.touches[0];
        element._touchStartPos = {
            x: touch.clientX,
            y: touch.clientY
        };

        // 防止默认行为和事件冒泡
        event.preventDefault();
        event.stopPropagation();
    }

    /**
     * 处理触摸移动
     */
    handleTouchMove(element, event, config) {
        if (!element._touchStartPos) return;

        const touch = event.touches[0];
        const deltaX = Math.abs(touch.clientX - element._touchStartPos.x);
        const deltaY = Math.abs(touch.clientY - element._touchStartPos.y);

        // 如果移动距离超过阈值，标记为移动
        if (deltaX > config.touchMoveThreshold || deltaY > config.touchMoveThreshold) {
            this.touchMoved.set(element, true);
        }

        // 检查是否移出元素边界
        const rect = element.getBoundingClientRect();
        const x = touch.clientX - rect.left;
        const y = touch.clientY - rect.top;

        if (x < -10 || x > rect.width + 10 || y < -10 || y > rect.height + 10) {
            element.classList.remove('touch-active');
        } else if (!element.classList.contains('touch-active')) {
            element.classList.add('touch-active');
        }

        event.preventDefault();
    }

    /**
     * 处理触摸结束
     */
    handleTouchEnd(element, event, callback, config) {
        element.classList.remove('touch-active');

        const wasMoved = this.touchMoved.get(element);
        
        if (!wasMoved) {
            // 只有在没有移动的情况下才触发回调
            this.handleCallback(element, callback, event);
        }

        // 重置状态
        this.resetTouchState(element, config.touchFeedbackDelay);

        event.preventDefault();
        event.stopPropagation();
    }

    /**
     * 处理触摸取消
     */
    handleTouchCancel(element, event) {
        element.classList.remove('touch-active');
        this.resetTouchState(element, 0);
        event.preventDefault();
    }

    /**
     * 重置触摸状态
     */
    resetTouchState(element, delay) {
        // 清除之前的定时器
        const existingTimer = this.touchTimers.get(element);
        if (existingTimer) {
            clearTimeout(existingTimer);
        }

        // 设置新的定时器
        const timer = setTimeout(() => {
            this.touchStarted.set(element, false);
            this.touchMoved.set(element, false);
            delete element._touchStartPos;
            this.touchTimers.delete(element);
        }, delay);

        this.touchTimers.set(element, timer);
    }

    /**
     * 处理回调函数
     */
    handleCallback(element, callback, event) {
        try {
            // 添加点击反馈动画
            this.addClickFeedback(element);
            
            // 执行回调
            callback(event);
        } catch (error) {
            console.error('❌ TouchHelper: 回调执行失败:', error);
        }
    }

    /**
     * 添加点击反馈动画
     */
    addClickFeedback(element) {
        element.classList.add('click-feedback');
        
        setTimeout(() => {
            element.classList.remove('click-feedback');
        }, 150);
    }

    /**
     * 批量为多个元素添加触摸支持
     * @param {string|NodeList} selector - CSS选择器或元素列表
     * @param {Function} callback - 回调函数
     * @param {Object} options - 配置选项
     */
    addTouchSupportToElements(selector, callback, options = {}) {
        let elements;
        
        if (typeof selector === 'string') {
            elements = document.querySelectorAll(selector);
        } else if (selector instanceof NodeList || Array.isArray(selector)) {
            elements = selector;
        } else {
            console.warn('⚠️ TouchHelper: 无效的选择器');
            return;
        }

        elements.forEach(element => {
            this.addTouchFriendlyEvents(element, callback, options);
        });

        console.log(`✅ 为 ${elements.length} 个元素添加了触摸支持`);
    }

    /**
     * 移除元素的触摸支持
     * @param {HTMLElement} element - 目标元素
     */
    removeTouchSupport(element) {
        if (!element) return;

        // 清理状态
        this.touchStarted.delete(element);
        this.touchMoved.delete(element);
        
        const timer = this.touchTimers.get(element);
        if (timer) {
            clearTimeout(timer);
            this.touchTimers.delete(element);
        }

        // 移除样式类
        element.classList.remove('touch-friendly', 'touch-active', 'click-feedback');
        
        // 清理属性
        delete element._touchStartPos;
    }

    /**
     * 检测设备是否支持触摸
     * @returns {boolean}
     */
    static isTouchDevice() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }

    /**
     * 获取触摸设备类型
     * @returns {string}
     */
    static getTouchDeviceType() {
        const userAgent = navigator.userAgent.toLowerCase();
        
        if (/ipad/.test(userAgent)) return 'tablet';
        if (/iphone|android.*mobile/.test(userAgent)) return 'mobile';
        if (/android/.test(userAgent)) return 'tablet';
        if (TouchHelper.isTouchDevice()) return 'touch';
        
        return 'desktop';
    }
}

// 创建全局实例
window.touchHelper = new TouchHelper();

console.log('📱 触摸支持工具已加载');
