/**
 * Split-Second Spark - 增强存储服务
 * 支持本地存储 + 云存储的混合存储方案
 */

class EnhancedStorageService {
    constructor(options = {}) {
        this.isInitialized = false;
        this.storageType = 'memory';
        this.memoryStorage = new Map();
        this.dbName = options.dbName || 'SplitSecondSparkDB';
        this.dbVersion = options.dbVersion || 1;
        this.db = null;
        
        // 云存储配置
        this.cloudConfig = options.cloudConfig || null;
        this.enableCloudSync = options.enableCloudSync || false;
        this.cloudAdapter = null;
        this.hybridMode = false;
        this.syncInterval = options.syncInterval || 5 * 60 * 1000; // 5分钟
        this.syncTimer = null;
        
        // 存储适配器优先级
        this.adapterPriority = options.adapterPriority || [
            'hybrid',      // 混合存储（本地+云端）
            'indexeddb',   // IndexedDB
            'localstorage', // localStorage
            'memory'       // 内存存储
        ];
        
        console.log('🗄️ 增强存储服务初始化中...', {
            cloudSync: this.enableCloudSync,
            adapters: this.adapterPriority
        });
    }

    /**
     * 初始化存储服务
     */
    async init() {
        try {
            console.log('🗄️ 初始化增强存储服务...');
            
            // 如果启用云存储，尝试初始化云适配器
            if (this.enableCloudSync && this.cloudConfig) {
                await this.initCloudStorage();
            }
            
            // 根据优先级选择存储方案
            for (const adapterType of this.adapterPriority) {
                try {
                    if (adapterType === 'hybrid' && this.cloudAdapter) {
                        await this.initHybridStorage();
                        this.storageType = 'hybrid';
                        console.log('✅ 使用混合存储模式（本地 + 云端）');
                        break;
                    } else if (adapterType === 'indexeddb' && this.isIndexedDBSupported()) {
                        await this.initIndexedDB();
                        this.storageType = 'indexeddb';
                        console.log('✅ 使用 IndexedDB 存储');
                        break;
                    } else if (adapterType === 'localstorage' && this.isLocalStorageSupported()) {
                        this.initLocalStorage();
                        this.storageType = 'localstorage';
                        console.log('✅ 使用 localStorage 存储');
                        break;
                    } else if (adapterType === 'memory') {
                        this.storageType = 'memory';
                        console.log('⚠️ 使用内存存储（数据不会持久化）');
                        break;
                    }
                } catch (error) {
                    console.warn(`⚠️ ${adapterType} 适配器初始化失败:`, error.message);
                    continue;
                }
            }
            
            this.isInitialized = true;
            console.log(`✅ 增强存储服务初始化完成，使用: ${this.storageType}`);
            
        } catch (error) {
            console.error('❌ 增强存储服务初始化失败:', error);
            this.storageType = 'memory';
            this.isInitialized = true;
        }
    }

    /**
     * 初始化云存储
     */
    async initCloudStorage() {
        try {
            if (this.cloudConfig.type === 'firebase') {
                // 动态导入 Firebase 适配器
                const { FirebaseStorageAdapter } = await import('./cloud-storage-adapter.js');
                this.cloudAdapter = new FirebaseStorageAdapter(this.cloudConfig.config);
                await this.cloudAdapter.init();
                console.log('✅ Firebase 云存储初始化成功');
            } else {
                throw new Error(`不支持的云存储类型: ${this.cloudConfig.type}`);
            }
        } catch (error) {
            console.warn('⚠️ 云存储初始化失败:', error.message);
            this.cloudAdapter = null;
        }
    }

    /**
     * 初始化混合存储模式
     */
    async initHybridStorage() {
        if (!this.cloudAdapter) {
            throw new Error('云存储适配器未初始化');
        }

        // 创建本地存储适配器
        let localAdapter;
        if (this.isIndexedDBSupported()) {
            await this.initIndexedDB();
            localAdapter = {
                put: this.putIndexedDB.bind(this),
                get: this.getIndexedDB.bind(this),
                delete: this.deleteIndexedDB.bind(this),
                list: this.listIndexedDB.bind(this),
                getStats: () => ({ type: 'IndexedDB', isAvailable: true })
            };
        } else if (this.isLocalStorageSupported()) {
            localAdapter = {
                put: this.putLocalStorage.bind(this),
                get: this.getLocalStorage.bind(this),
                delete: this.deleteLocalStorage.bind(this),
                list: this.listLocalStorage.bind(this),
                getStats: () => ({ type: 'localStorage', isAvailable: true })
            };
        } else {
            localAdapter = {
                put: this.putMemory.bind(this),
                get: this.getMemory.bind(this),
                delete: this.deleteMemory.bind(this),
                list: this.listMemory.bind(this),
                getStats: () => ({ type: 'Memory', isAvailable: true })
            };
        }

        // 创建混合适配器
        const { HybridStorageAdapter } = await import('./cloud-storage-adapter.js');
        this.hybridAdapter = new HybridStorageAdapter(localAdapter, this.cloudAdapter);
        await this.hybridAdapter.init();
        
        this.hybridMode = true;
        console.log('✅ 混合存储模式初始化成功');
    }

    /**
     * 保存数据
     */
    async put(key, value) {
        if (!this.isInitialized) {
            await this.init();
        }

        try {
            if (this.hybridMode && this.hybridAdapter) {
                return await this.hybridAdapter.put(key, value);
            }

            const serializedValue = JSON.stringify(value);
            
            switch (this.storageType) {
                case 'indexeddb':
                    return await this.putIndexedDB(key, serializedValue);
                case 'localstorage':
                    return this.putLocalStorage(key, serializedValue);
                case 'memory':
                default:
                    return this.putMemory(key, serializedValue);
            }
        } catch (error) {
            console.error('❌ 数据保存失败:', error);
            throw error;
        }
    }

    /**
     * 读取数据
     */
    async get(key, defaultValue = null) {
        if (!this.isInitialized) {
            await this.init();
        }

        try {
            if (this.hybridMode && this.hybridAdapter) {
                const result = await this.hybridAdapter.get(key);
                return result !== null ? result : defaultValue;
            }

            let result;
            switch (this.storageType) {
                case 'indexeddb':
                    result = await this.getIndexedDB(key);
                    break;
                case 'localstorage':
                    result = this.getLocalStorage(key);
                    break;
                case 'memory':
                default:
                    result = this.getMemory(key);
                    break;
            }
            
            if (result === null) {
                return defaultValue;
            }
            
            return JSON.parse(result);
        } catch (error) {
            console.error('❌ 数据读取失败:', error);
            return defaultValue;
        }
    }

    /**
     * 删除数据
     */
    async delete(key) {
        if (!this.isInitialized) {
            await this.init();
        }

        try {
            if (this.hybridMode && this.hybridAdapter) {
                return await this.hybridAdapter.delete(key);
            }

            switch (this.storageType) {
                case 'indexeddb':
                    return await this.deleteIndexedDB(key);
                case 'localstorage':
                    return this.deleteLocalStorage(key);
                case 'memory':
                default:
                    return this.deleteMemory(key);
            }
        } catch (error) {
            console.error('❌ 数据删除失败:', error);
            throw error;
        }
    }

    /**
     * 列出所有键值
     */
    async list(prefix = '') {
        if (!this.isInitialized) {
            await this.init();
        }

        try {
            if (this.hybridMode && this.hybridAdapter) {
                return await this.hybridAdapter.list(prefix);
            }

            switch (this.storageType) {
                case 'indexeddb':
                    return await this.listIndexedDB(prefix);
                case 'localstorage':
                    return this.listLocalStorage(prefix);
                case 'memory':
                default:
                    return this.listMemory(prefix);
            }
        } catch (error) {
            console.error('❌ 键值列表获取失败:', error);
            return [];
        }
    }

    /**
     * 清空所有数据
     */
    async clear() {
        if (!this.isInitialized) {
            await this.init();
        }

        try {
            if (this.hybridMode && this.hybridAdapter) {
                // 清空本地和云端数据
                const keys = await this.hybridAdapter.list();
                for (const key of keys) {
                    await this.hybridAdapter.delete(key);
                }
                return true;
            }

            switch (this.storageType) {
                case 'indexeddb':
                    return await this.clearIndexedDB();
                case 'localstorage':
                    return this.clearLocalStorage();
                case 'memory':
                default:
                    this.memoryStorage.clear();
                    return true;
            }
        } catch (error) {
            console.error('❌ 数据清空失败:', error);
            throw error;
        }
    }

    /**
     * 手动同步数据（仅在混合模式下有效）
     */
    async syncData() {
        if (this.hybridMode && this.hybridAdapter) {
            try {
                await this.hybridAdapter.syncData();
                console.log('✅ 数据同步完成');
            } catch (error) {
                console.error('❌ 数据同步失败:', error);
                throw error;
            }
        } else {
            console.warn('⚠️ 非混合存储模式，无需同步');
        }
    }

    /**
     * 获取存储统计信息
     */
    async getStorageInfo() {
        try {
            if (this.hybridMode && this.hybridAdapter) {
                return await this.hybridAdapter.getStats();
            }

            const keys = await this.list();
            return {
                type: this.storageType,
                keyCount: keys.length,
                isAvailable: this.isInitialized,
                hybridMode: this.hybridMode,
                cloudSync: this.enableCloudSync
            };
        } catch (error) {
            return {
                type: this.storageType,
                keyCount: 0,
                isAvailable: false,
                error: error.message
            };
        }
    }

    // 以下是各种存储后端的具体实现方法
    // 这些方法与原始存储服务相同，这里省略具体实现
    // 实际使用时需要从原始文件复制相关方法

    isIndexedDBSupported() {
        return typeof window !== 'undefined' && 'indexedDB' in window;
    }

    isLocalStorageSupported() {
        try {
            return typeof window !== 'undefined' && 'localStorage' in window && window.localStorage !== null;
        } catch (e) {
            return false;
        }
    }

    // ... 其他方法的实现
}

// 导出增强存储服务
if (typeof window !== 'undefined') {
    window.EnhancedStorageService = EnhancedStorageService;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedStorageService;
}
