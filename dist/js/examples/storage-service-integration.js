/**
 * Split-Second Spark - 存储服务集成示例
 * 展示如何在游戏中正确集成存储服务，包括用户身份检查
 */

/**
 * 游戏存储管理器
 * 负责管理游戏的存储服务初始化和用户身份验证
 */
class GameStorageManager {
    constructor(gameInstance) {
        this.game = gameInstance;
        this.configManager = null;
        this.storageService = null;
        this.userIdentityManager = null;
        this.isInitialized = false;
        
        console.log('🎮 游戏存储管理器初始化');
    }

    /**
     * 初始化存储系统
     */
    async init() {
        try {
            console.log('🔧 开始初始化游戏存储系统...');
            
            // 1. 导入必要的模块
            await this.loadRequiredModules();
            
            // 2. 获取配置管理器实例
            this.configManager = StorageConfigManager.getInstance();
            
            // 3. 自动选择最佳配置
            const selectedConfig = this.configManager.autoSelectConfig();
            console.log('🎯 自动选择的配置:', selectedConfig);
            
            // 4. 检查存储服务是否已存在，如果存在则引导用户设置身份
            await this.initializeStorageService();
            
            // 5. 设置事件监听器
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('✅ 游戏存储系统初始化完成');
            
            // 触发初始化完成事件
            this.dispatchEvent('gameStorage:initialized', {
                storageService: this.storageService,
                configName: this.configManager.getCurrentConfigName()
            });
            
        } catch (error) {
            console.error('❌ 游戏存储系统初始化失败:', error);
            throw error;
        }
    }

    /**
     * 加载必要的模块
     */
    async loadRequiredModules() {
        try {
            // 动态导入存储配置管理器
            if (typeof StorageConfigManager === 'undefined') {
                const { StorageConfigManager: ConfigManager } = await import('../config/storage-config.js');
                window.StorageConfigManager = ConfigManager;
            }
            
            // 尝试加载用户身份管理器
            try {
                if (typeof UserIdentityManager === 'undefined') {
                    const { default: IdentityManager } = await import('../utils/user-identity-manager.js');
                    window.UserIdentityManager = IdentityManager;
                }
            } catch (error) {
                console.warn('⚠️ 用户身份管理器不可用，将使用基础存储功能');
            }
            
        } catch (error) {
            console.error('❌ 模块加载失败:', error);
            throw error;
        }
    }

    /**
     * 初始化存储服务
     */
    async initializeStorageService() {
        let storageService;
        const existingService = this.configManager.getCurrentStorageService();

        if (existingService) {
            console.log('🔍 检测到已存在的存储服务');
            
            // 检查是否为云存储配置
            const isCloudStorage = this.configManager.isCloudStorageEnabled();
            
            if (isCloudStorage) {
                console.log('☁️ 当前配置支持云存储，检查用户身份状态...');
                
                // 检查用户身份状态
                const hasUserIdentity = await this.checkUserIdentityStatus();
                
                if (!hasUserIdentity) {
                    console.log('⚠️ 需要设置用户身份以使用云存储功能');
                    
                    // 显示用户身份设置提示
                    const shouldSetupIdentity = await this.promptUserIdentitySetup();
                    
                    if (shouldSetupIdentity) {
                        // 引导用户设置身份
                        const userIdentity = await this.setupUserIdentity();
                        
                        if (userIdentity) {
                            console.log('✅ 用户身份设置完成，重新创建存储服务');
                            storageService = await this.configManager.createStorageService();
                        } else {
                            console.log('❌ 用户身份设置失败，使用现有服务');
                            storageService = existingService;
                        }
                    } else {
                        console.log('👤 用户选择跳过身份设置，使用现有服务');
                        storageService = existingService;
                    }
                } else {
                    console.log('✅ 用户身份已设置，使用现有服务');
                    storageService = existingService;
                }
            } else {
                console.log('📱 当前为本地存储配置，使用现有服务');
                storageService = existingService;
            }
        } else {
            console.log('🆕 创建新的存储服务');
            storageService = await this.configManager.createStorageService();
        }

        this.storageService = storageService;
    }

    /**
     * 检查用户身份状态
     */
    async checkUserIdentityStatus() {
        try {
            // 检查是否有用户身份管理器
            if (window.UserIdentityManager && this.userIdentityManager) {
                const userInfo = this.userIdentityManager.getUserInfo();
                return userInfo && !userInfo.isGuest;
            }
            
            // 检查 Firebase 认证状态
            if (window.firebase && window.firebase.auth) {
                const user = window.firebase.auth().currentUser;
                return user && !user.isAnonymous;
            }
            
            return false;
        } catch (error) {
            console.warn('检查用户身份状态失败:', error);
            return false;
        }
    }

    /**
     * 提示用户设置身份
     */
    async promptUserIdentitySetup() {
        return new Promise((resolve) => {
            // 创建游戏内的身份设置提示
            const notification = this.createInGameNotification({
                title: '🔐 启用云存储功能',
                message: '检测到您的游戏配置支持云存储功能，可以在多个设备间同步游戏数据。是否现在设置用户身份以启用云存储？',
                buttons: [
                    {
                        text: '设置身份',
                        style: 'primary',
                        onClick: () => {
                            this.closeNotification(notification);
                            resolve(true);
                        }
                    },
                    {
                        text: '暂时跳过',
                        style: 'secondary',
                        onClick: () => {
                            this.closeNotification(notification);
                            resolve(false);
                        }
                    }
                ]
            });
        });
    }

    /**
     * 设置用户身份
     */
    async setupUserIdentity() {
        try {
            // 如果有用户身份管理器，使用它
            if (window.UserIdentityManager) {
                if (!this.userIdentityManager) {
                    this.userIdentityManager = new UserIdentityManager({
                        enableAnonymousAuth: true,
                        enableEmailAuth: true,
                        enableGuestMode: true
                    });
                    
                    // 这里需要 Firebase 配置，可以从游戏配置中获取
                    const firebaseConfig = this.getFirebaseConfig();
                    if (firebaseConfig) {
                        await this.userIdentityManager.init(firebaseConfig);
                    }
                }
                
                return await this.showUserIdentityDialog();
            }
            
            // 否则使用简单的 Firebase 认证
            if (window.firebase && window.firebase.auth) {
                return await window.firebase.auth().signInAnonymously();
            }
            
            console.warn('未找到可用的用户身份管理系统');
            return null;
            
        } catch (error) {
            console.error('用户身份设置失败:', error);
            return null;
        }
    }

    /**
     * 显示用户身份设置对话框
     */
    async showUserIdentityDialog() {
        return new Promise((resolve) => {
            const dialog = this.createInGameDialog({
                title: '👤 设置用户身份',
                content: `
                    <div class="identity-options">
                        <button class="identity-btn guest-mode" data-mode="guest">
                            🎮 访客模式
                            <small>本地存储，无需注册</small>
                        </button>
                        <button class="identity-btn anonymous-mode" data-mode="anonymous">
                            🎭 匿名登录
                            <small>云端同步，可随时升级</small>
                        </button>
                        <button class="identity-btn email-mode" data-mode="email">
                            📧 邮箱注册
                            <small>完整功能，永久保存</small>
                        </button>
                    </div>
                    <div class="email-form" style="display: none;">
                        <input type="email" id="userEmail" placeholder="邮箱地址">
                        <input type="password" id="userPassword" placeholder="密码">
                        <input type="text" id="displayName" placeholder="显示名称">
                        <button class="confirm-btn">确认注册</button>
                    </div>
                `,
                onClose: () => resolve(null)
            });
            
            // 绑定事件
            dialog.querySelector('.guest-mode').onclick = async () => {
                try {
                    const user = this.userIdentityManager.createGuestUser();
                    this.closeDialog(dialog);
                    resolve(user);
                } catch (error) {
                    console.error('访客模式设置失败:', error);
                    resolve(null);
                }
            };
            
            dialog.querySelector('.anonymous-mode').onclick = async () => {
                try {
                    const user = await this.userIdentityManager.autoSignIn();
                    this.closeDialog(dialog);
                    resolve(user);
                } catch (error) {
                    console.error('匿名登录失败:', error);
                    resolve(null);
                }
            };
            
            dialog.querySelector('.email-mode').onclick = () => {
                dialog.querySelector('.email-form').style.display = 'block';
            };
            
            dialog.querySelector('.confirm-btn').onclick = async () => {
                const email = dialog.querySelector('#userEmail').value.trim();
                const password = dialog.querySelector('#userPassword').value.trim();
                const displayName = dialog.querySelector('#displayName').value.trim();
                
                if (!email || !password) {
                    this.showGameMessage('请填写邮箱和密码', 'error');
                    return;
                }
                
                try {
                    const user = await this.userIdentityManager.signUpWithEmail(email, password, displayName);
                    this.closeDialog(dialog);
                    resolve(user);
                } catch (error) {
                    console.error('邮箱注册失败:', error);
                    this.showGameMessage('注册失败: ' + error.message, 'error');
                }
            };
        });
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听用户身份变更事件
        window.addEventListener('userIdentity:userSignedIn', (event) => {
            const { user } = event.detail;
            console.log('👤 用户已登录，更新存储服务:', user.uid);
            
            // 触发游戏事件
            this.dispatchEvent('gameStorage:userSignedIn', { user });
        });

        window.addEventListener('userIdentity:userSignedOut', () => {
            console.log('👤 用户已登出');
            
            // 触发游戏事件
            this.dispatchEvent('gameStorage:userSignedOut');
        });
    }

    /**
     * 游戏数据操作方法
     */
    async saveGameData(key, value) {
        if (!this.storageService) {
            throw new Error('存储服务未初始化');
        }
        
        try {
            await this.storageService.put(key, value);
            console.log(`💾 游戏数据已保存: ${key}`);
            
            // 触发数据保存事件
            this.dispatchEvent('gameStorage:dataSaved', { key, value });
            
        } catch (error) {
            console.error(`❌ 游戏数据保存失败 [${key}]:`, error);
            throw error;
        }
    }

    async loadGameData(key) {
        if (!this.storageService) {
            throw new Error('存储服务未初始化');
        }
        
        try {
            const value = await this.storageService.get(key);
            console.log(`📥 游戏数据已读取: ${key}`);
            
            // 触发数据读取事件
            this.dispatchEvent('gameStorage:dataLoaded', { key, value });
            
            return value;
        } catch (error) {
            console.error(`❌ 游戏数据读取失败 [${key}]:`, error);
            throw error;
        }
    }

    /**
     * 获取 Firebase 配置
     */
    getFirebaseConfig() {
        // 从游戏配置或环境变量中获取 Firebase 配置
        const currentConfig = this.configManager.getCurrentConfig();
        if (currentConfig && currentConfig.cloudConfig && currentConfig.cloudConfig.config) {
            return currentConfig.cloudConfig.config;
        }
        
        // 或者从全局配置中获取
        if (window.FIREBASE_CONFIG) {
            return window.FIREBASE_CONFIG;
        }
        
        console.warn('⚠️ 未找到 Firebase 配置');
        return null;
    }

    /**
     * 游戏内通知和对话框方法（需要根据具体游戏引擎实现）
     */
    createInGameNotification(options) {
        // 这里需要根据具体的游戏引擎实现
        console.log('📢 游戏内通知:', options.title, options.message);
        
        // 简单的浏览器实现
        if (confirm(`${options.title}\n\n${options.message}`)) {
            options.buttons[0].onClick();
        } else {
            options.buttons[1].onClick();
        }
    }

    createInGameDialog(options) {
        // 这里需要根据具体的游戏引擎实现
        console.log('💬 游戏内对话框:', options.title);
        
        // 简单的浏览器实现（实际游戏中应该使用游戏引擎的UI系统）
        const dialog = document.createElement('div');
        dialog.innerHTML = options.content;
        return dialog;
    }

    closeNotification(notification) {
        // 关闭通知的实现
    }

    closeDialog(dialog) {
        // 关闭对话框的实现
    }

    showGameMessage(message, type = 'info') {
        console.log(`📝 游戏消息 [${type}]:`, message);
    }

    /**
     * 触发自定义事件
     */
    dispatchEvent(eventName, detail) {
        const event = new CustomEvent(eventName, { detail });
        window.dispatchEvent(event);
        
        // 如果游戏实例有事件系统，也可以触发游戏内事件
        if (this.game && this.game.events && this.game.events.emit) {
            this.game.events.emit(eventName, detail);
        }
    }

    /**
     * 获取存储状态信息
     */
    getStorageStatus() {
        return {
            isInitialized: this.isInitialized,
            hasStorageService: !!this.storageService,
            hasUserIdentityManager: !!this.userIdentityManager,
            currentConfig: this.configManager?.getCurrentConfigName(),
            isCloudStorageEnabled: this.configManager?.isCloudStorageEnabled()
        };
    }

    /**
     * 清理资源
     */
    destroy() {
        if (this.userIdentityManager && typeof this.userIdentityManager.destroy === 'function') {
            this.userIdentityManager.destroy();
        }
        
        if (this.configManager) {
            this.configManager.resetStorageService();
        }
        
        this.storageService = null;
        this.userIdentityManager = null;
        this.configManager = null;
        this.isInitialized = false;
        
        console.log('🧹 游戏存储管理器资源已清理');
    }
}

// 导出游戏存储管理器
if (typeof window !== 'undefined') {
    window.GameStorageManager = GameStorageManager;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = GameStorageManager;
}
