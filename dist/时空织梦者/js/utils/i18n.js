/**
 * 时空织梦者 - 国际化服务
 * 支持中英文双语切换和动态文本更新
 * 
 * 功能特性:
 * - 多语言支持 (中文/英文)
 * - 动态语言切换
 * - 自动DOM更新
 * - 本地化存储
 * - 插值和格式化支持
 */

class I18nService {
    constructor() {
        this.currentLanguage = 'zh-CN';
        this.fallbackLanguage = 'zh-CN';
        this.translations = {};
        this.isInitialized = false;
        
        // 支持的语言列表
        this.supportedLanguages = {
            'zh-CN': '中文',
            'en-US': 'English'
        };

        console.log('🌍 国际化服务初始化中...');
        this._initializeTranslations();
    }

    /**
     * 初始化翻译数据
     */
    _initializeTranslations() {
        // 中文翻译
        this.translations['zh-CN'] = {
            // 游戏基本信息
            'game.title': '时空织梦者',
            'game.subtitle': 'Temporal Dream Weaver',
            'game.tagline': '捕捉决定性瞬间，引燃无限可能',
            'game.level': '关卡',
            'game.score': '得分',
            'game.reset': '重置',
            'game.hint': '提示',
            'game.pause': '暂停',
            'game.resume': '继续',
            'game.restart': '重新开始',

            // 加载界面
            'loading.initializing': '正在初始化...',
            'loading.loading_assets': '正在加载资源...',
            'loading.preparing_game': '正在准备游戏...',
            'loading.complete': '加载完成',

            // 主菜单
            'menu.start_game': '开始游戏',
            'menu.level_editor': '关卡编辑器',
            'menu.custom_levels': '自定义关卡',
            'menu.leaderboard': '排行榜',
            'menu.tutorial': '游戏教程',
            'menu.settings': '游戏设置',

            // 玩家系统
            'player.current': '当前玩家',
            'player.change': '切换',
            'player.create': '创建玩家',
            'player.name': '玩家名称',
            'player.stats': '统计数据',
            'player.achievements': '成就',

            // 游戏元素
            'elements.light_orb': '光球',
            'elements.time_gate': '时间门',
            'elements.mirror': '镜面',
            'elements.target': '目标点',
            'elements.obstacle': '障碍物',
            'elements.portal': '传送门',

            // 关卡编辑器
            'editor.title': '关卡编辑器',
            'editor.elements': '元素面板',
            'editor.save': '保存',
            'editor.test': '测试',
            'editor.load': '加载',
            'editor.new': '新建',
            'editor.properties': '属性',
            'editor.level_name': '关卡名称',
            'editor.level_description': '关卡描述',
            'editor.difficulty': '难度等级',

            // 设置界面
            'settings.title': '游戏设置',
            'settings.display': '显示设置',
            'settings.gameplay': '游戏设置',
            'settings.language': '语言',
            'settings.theme': '主题',
            'settings.theme_dark': '深色',
            'settings.theme_light': '浅色',
            'settings.difficulty': '难度',
            'settings.difficulty_easy': '简单',
            'settings.difficulty_normal': '普通',
            'settings.difficulty_hard': '困难',
            'settings.auto_save': '自动保存',
            'settings.sound_effects': '音效',
            'settings.background_music': '背景音乐',

            // 游戏状态
            'status.paused': '游戏已暂停',
            'status.level_complete': '关卡完成！',
            'status.level_failed': '关卡失败',
            'status.game_over': '游戏结束',
            'status.new_record': '新纪录！',
            'status.perfect_score': '完美得分！',

            // 时间控制
            'time.rewind': '时间倒流',
            'time.pause': '暂停时间',
            'time.forward': '时间加速',
            'time.normal': '正常时间',

            // 工具提示
            'tooltip.select_tool': '选择工具 - 点击选择元素',
            'tooltip.connect_tool': '连接工具 - 拖拽连接元素',
            'tooltip.erase_tool': '擦除工具 - 删除连接线',

            // 通用按钮
            'common.ok': '确定',
            'common.cancel': '取消',
            'common.back': '返回',
            'common.next': '下一步',
            'common.previous': '上一步',
            'common.close': '关闭',
            'common.save': '保存',
            'common.load': '加载',
            'common.delete': '删除',
            'common.edit': '编辑',
            'common.copy': '复制',
            'common.paste': '粘贴',

            // 错误信息
            'error.save_failed': '保存失败',
            'error.load_failed': '加载失败',
            'error.invalid_level': '无效的关卡数据',
            'error.storage_full': '存储空间不足',
            'error.network_error': '网络连接错误',

            // 成功信息
            'success.saved': '保存成功',
            'success.loaded': '加载成功',
            'success.level_created': '关卡创建成功',
            'success.settings_updated': '设置已更新'
        };

        // 英文翻译
        this.translations['en-US'] = {
            // Game basic info
            'game.title': 'Temporal Dream Weaver',
            'game.subtitle': '时空织梦者',
            'game.tagline': 'Capture decisive moments, ignite infinite possibilities',
            'game.level': 'Level',
            'game.score': 'Score',
            'game.reset': 'Reset',
            'game.hint': 'Hint',
            'game.pause': 'Pause',
            'game.resume': 'Resume',
            'game.restart': 'Restart',

            // Loading screen
            'loading.initializing': 'Initializing...',
            'loading.loading_assets': 'Loading assets...',
            'loading.preparing_game': 'Preparing game...',
            'loading.complete': 'Loading complete',

            // Main menu
            'menu.start_game': 'Start Game',
            'menu.level_editor': 'Level Editor',
            'menu.custom_levels': 'Custom Levels',
            'menu.leaderboard': 'Leaderboard',
            'menu.tutorial': 'Tutorial',
            'menu.settings': 'Settings',

            // Player system
            'player.current': 'Current Player',
            'player.change': 'Switch',
            'player.create': 'Create Player',
            'player.name': 'Player Name',
            'player.stats': 'Statistics',
            'player.achievements': 'Achievements',

            // Game elements
            'elements.light_orb': 'Light Orb',
            'elements.time_gate': 'Time Gate',
            'elements.mirror': 'Mirror',
            'elements.target': 'Target',
            'elements.obstacle': 'Obstacle',
            'elements.portal': 'Portal',

            // Level editor
            'editor.title': 'Level Editor',
            'editor.elements': 'Element Panel',
            'editor.save': 'Save',
            'editor.test': 'Test',
            'editor.load': 'Load',
            'editor.new': 'New',
            'editor.properties': 'Properties',
            'editor.level_name': 'Level Name',
            'editor.level_description': 'Level Description',
            'editor.difficulty': 'Difficulty',

            // Settings
            'settings.title': 'Game Settings',
            'settings.display': 'Display Settings',
            'settings.gameplay': 'Gameplay Settings',
            'settings.language': 'Language',
            'settings.theme': 'Theme',
            'settings.theme_dark': 'Dark',
            'settings.theme_light': 'Light',
            'settings.difficulty': 'Difficulty',
            'settings.difficulty_easy': 'Easy',
            'settings.difficulty_normal': 'Normal',
            'settings.difficulty_hard': 'Hard',
            'settings.auto_save': 'Auto Save',
            'settings.sound_effects': 'Sound Effects',
            'settings.background_music': 'Background Music',

            // Game status
            'status.paused': 'Game Paused',
            'status.level_complete': 'Level Complete!',
            'status.level_failed': 'Level Failed',
            'status.game_over': 'Game Over',
            'status.new_record': 'New Record!',
            'status.perfect_score': 'Perfect Score!',

            // Time control
            'time.rewind': 'Rewind Time',
            'time.pause': 'Pause Time',
            'time.forward': 'Fast Forward',
            'time.normal': 'Normal Time',

            // Tooltips
            'tooltip.select_tool': 'Select Tool - Click to select elements',
            'tooltip.connect_tool': 'Connect Tool - Drag to connect elements',
            'tooltip.erase_tool': 'Erase Tool - Delete connections',

            // Common buttons
            'common.ok': 'OK',
            'common.cancel': 'Cancel',
            'common.back': 'Back',
            'common.next': 'Next',
            'common.previous': 'Previous',
            'common.close': 'Close',
            'common.save': 'Save',
            'common.load': 'Load',
            'common.delete': 'Delete',
            'common.edit': 'Edit',
            'common.copy': 'Copy',
            'common.paste': 'Paste',

            // Error messages
            'error.save_failed': 'Save failed',
            'error.load_failed': 'Load failed',
            'error.invalid_level': 'Invalid level data',
            'error.storage_full': 'Storage space full',
            'error.network_error': 'Network connection error',

            // Success messages
            'success.saved': 'Saved successfully',
            'success.loaded': 'Loaded successfully',
            'success.level_created': 'Level created successfully',
            'success.settings_updated': 'Settings updated'
        };

        this.isInitialized = true;
        console.log('✅ 国际化服务初始化完成');
    }

    /**
     * 初始化国际化服务
     * 从存储中恢复语言设置
     */
    async init() {
        if (storageService) {
            try {
                const savedLanguage = await storageService.get('game.language');
                if (savedLanguage && this.supportedLanguages[savedLanguage]) {
                    this.currentLanguage = savedLanguage;
                }
            } catch (error) {
                console.warn('⚠️ 无法加载保存的语言设置:', error);
            }
        }

        // 检测浏览器语言
        if (!storageService || this.currentLanguage === 'zh-CN') {
            const browserLang = navigator.language || navigator.userLanguage;
            if (browserLang.startsWith('en') && this.supportedLanguages['en-US']) {
                this.currentLanguage = 'en-US';
            }
        }

        this.updateDOM();
        console.log(`🌍 当前语言: ${this.supportedLanguages[this.currentLanguage]}`);
    }

    /**
     * 获取翻译文本
     * @param {string} key - 翻译键
     * @param {object} params - 插值参数
     * @returns {string} 翻译后的文本
     */
    t(key, params = {}) {
        if (!this.isInitialized) {
            return key;
        }

        let text = this.translations[this.currentLanguage]?.[key] || 
                   this.translations[this.fallbackLanguage]?.[key] || 
                   key;

        // 处理参数插值
        if (params && typeof params === 'object') {
            Object.keys(params).forEach(param => {
                text = text.replace(new RegExp(`{${param}}`, 'g'), params[param]);
            });
        }

        return text;
    }

    /**
     * 设置当前语言
     * @param {string} language - 语言代码
     */
    async setLanguage(language) {
        if (!this.supportedLanguages[language]) {
            console.warn(`⚠️ 不支持的语言: ${language}`);
            return;
        }

        this.currentLanguage = language;
        
        // 保存语言设置
        if (storageService) {
            try {
                await storageService.put('game.language', language);
            } catch (error) {
                console.warn('⚠️ 无法保存语言设置:', error);
            }
        }

        this.updateDOM();
        console.log(`🌍 语言已切换为: ${this.supportedLanguages[language]}`);
    }

    /**
     * 获取当前语言
     * @returns {string} 当前语言代码
     */
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    /**
     * 获取支持的语言列表
     * @returns {object} 支持的语言映射
     */
    getSupportedLanguages() {
        return { ...this.supportedLanguages };
    }

    /**
     * 更新DOM中的翻译文本
     */
    updateDOM() {
        const elements = document.querySelectorAll('[data-i18n]');
        elements.forEach(element => {
            const key = element.getAttribute('data-i18n');
            if (key) {
                const text = this.t(key);
                
                // 根据元素类型更新文本
                if (element.tagName === 'INPUT' && element.type === 'button') {
                    element.value = text;
                } else if (element.tagName === 'INPUT' && element.placeholder !== undefined) {
                    element.placeholder = text;
                } else {
                    element.textContent = text;
                }
            }
        });

        // 更新页面标题
        const titleKey = document.documentElement.getAttribute('data-i18n-title');
        if (titleKey) {
            document.title = this.t(titleKey);
        }

        // 触发语言变更事件
        window.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: this.currentLanguage }
        }));
    }

    /**
     * 切换到下一个语言
     */
    async toggleLanguage() {
        const languages = Object.keys(this.supportedLanguages);
        const currentIndex = languages.indexOf(this.currentLanguage);
        const nextIndex = (currentIndex + 1) % languages.length;
        const nextLanguage = languages[nextIndex];
        
        await this.setLanguage(nextLanguage);
    }
}

// 创建全局国际化服务实例
const i18nService = new I18nService();

// 导出国际化服务
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { I18nService, i18nService };
} else {
    window.I18nService = I18nService;
    window.i18nService = i18nService;
}
