<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时空织梦者 - Temporal Dream Weaver</title>
    <meta name="description" content="捕捉决定性瞬间，引燃无限可能。一款策略解谜类时间操控游戏。">
    <meta name="keywords" content="时空织梦者,策略游戏,解谜游戏,时间操控,梦境编织">
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/game.css">
    <link rel="stylesheet" href="styles/editor.css">
    <link rel="stylesheet" href="styles/responsive.css">
    
    <!-- PWA支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#1a1a2e">
    
    <!-- 图标 -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    <link rel="apple-touch-icon" href="assets/images/icon-192.png">
</head>
<body>
    <!-- 加载屏幕 -->
    <div id="loading-screen" class="screen active">
        <div class="loading-container">
            <div class="loading-logo">
                <div class="time-symbol">⧖</div>
                <h1 data-i18n="game.title">时空织梦者</h1>
                <p data-i18n="game.subtitle">Temporal Dream Weaver</p>
            </div>
            <div class="loading-progress">
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <p class="loading-text" data-i18n="loading.initializing">正在初始化...</p>
            </div>
        </div>
    </div>

    <!-- 主菜单屏幕 -->
    <div id="main-menu-screen" class="screen">
        <div class="menu-container">
            <header class="game-header">
                <div class="title-section">
                    <div class="game-icon">⧖</div>
                    <div class="title-text">
                        <h1 data-i18n="game.title">时空织梦者</h1>
                        <p data-i18n="game.tagline">捕捉决定性瞬间，引燃无限可能</p>
                    </div>
                </div>
                <div class="header-controls">
                    <button class="icon-btn language-btn" id="language-toggle" title="切换语言">
                        <span class="icon">🌐</span>
                    </button>
                    <button class="icon-btn settings-btn" id="settings-btn" title="设置">
                        <span class="icon">⚙️</span>
                    </button>
                </div>
            </header>

            <nav class="main-menu">
                <button class="menu-btn primary" id="start-game-btn">
                    <span class="btn-icon">▶️</span>
                    <span data-i18n="menu.start_game">开始游戏</span>
                </button>
                <button class="menu-btn" id="level-editor-btn">
                    <span class="btn-icon">🛠️</span>
                    <span data-i18n="menu.level_editor">关卡编辑器</span>
                </button>
                <button class="menu-btn" id="custom-levels-btn">
                    <span class="btn-icon">🌟</span>
                    <span data-i18n="menu.custom_levels">自定义关卡</span>
                </button>
                <button class="menu-btn" id="leaderboard-btn">
                    <span class="btn-icon">🏆</span>
                    <span data-i18n="menu.leaderboard">排行榜</span>
                </button>
                <button class="menu-btn" id="tutorial-btn">
                    <span class="btn-icon">📚</span>
                    <span data-i18n="menu.tutorial">游戏教程</span>
                </button>
            </nav>

            <div class="player-info">
                <div class="current-player">
                    <span data-i18n="player.current">当前玩家:</span>
                    <span id="current-player-name">玩家1</span>
                    <button class="change-player-btn" id="change-player-btn" data-i18n="player.change">切换</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 游戏屏幕 -->
    <div id="game-screen" class="screen">
        <div class="game-container">
            <!-- 游戏头部信息 -->
            <header class="game-hud">
                <div class="hud-left">
                    <button class="hud-btn" id="pause-btn" title="暂停游戏">
                        <span class="icon">⏸️</span>
                    </button>
                    <button class="hud-btn" id="back-to-menu-btn" title="返回主菜单">
                        <span class="icon">🏠</span>
                    </button>
                </div>
                
                <div class="hud-center">
                    <div class="level-info">
                        <span data-i18n="game.level">关卡</span>
                        <span id="current-level">1</span>
                    </div>
                    <div class="score-info">
                        <span data-i18n="game.score">得分:</span>
                        <span id="current-score">0</span>
                    </div>
                </div>
                
                <div class="hud-right">
                    <div class="time-controls">
                        <button class="time-btn" id="time-rewind-btn" title="时间倒流">
                            <span class="icon">⏪</span>
                        </button>
                        <button class="time-btn" id="time-pause-btn" title="暂停时间">
                            <span class="icon">⏸️</span>
                        </button>
                        <button class="time-btn" id="time-forward-btn" title="时间加速">
                            <span class="icon">⏩</span>
                        </button>
                    </div>
                </div>
            </header>

            <!-- 游戏画布区域 -->
            <div class="game-canvas-container">
                <canvas id="game-canvas" width="800" height="600"></canvas>
                <div class="canvas-overlay">
                    <!-- 时间线显示 -->
                    <div class="timeline-display" id="timeline-display">
                        <div class="timeline-track">
                            <div class="timeline-cursor" id="timeline-cursor"></div>
                        </div>
                    </div>
                    
                    <!-- 连接线绘制层 -->
                    <svg class="connection-layer" id="connection-layer">
                        <!-- 动态生成的连接线 -->
                    </svg>
                </div>
            </div>

            <!-- 游戏底部工具栏 -->
            <div class="game-toolbar">
                <div class="tool-group">
                    <button class="tool-btn active" id="select-tool" data-tool="select" title="选择工具">
                        <span class="icon">👆</span>
                    </button>
                    <button class="tool-btn" id="connect-tool" data-tool="connect" title="连接工具">
                        <span class="icon">🔗</span>
                    </button>
                    <button class="tool-btn" id="erase-tool" data-tool="erase" title="擦除工具">
                        <span class="icon">🧹</span>
                    </button>
                </div>
                
                <div class="action-group">
                    <button class="action-btn" id="reset-level-btn" title="重置关卡">
                        <span class="icon">🔄</span>
                        <span data-i18n="game.reset">重置</span>
                    </button>
                    <button class="action-btn" id="hint-btn" title="提示">
                        <span class="icon">💡</span>
                        <span data-i18n="game.hint">提示</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 关卡编辑器屏幕 -->
    <div id="level-editor-screen" class="screen">
        <div class="editor-container">
            <header class="editor-header">
                <div class="editor-title">
                    <h2 data-i18n="editor.title">关卡编辑器</h2>
                </div>
                <div class="editor-controls">
                    <button class="editor-btn" id="save-level-btn">
                        <span class="icon">💾</span>
                        <span data-i18n="editor.save">保存</span>
                    </button>
                    <button class="editor-btn" id="test-level-btn">
                        <span class="icon">▶️</span>
                        <span data-i18n="editor.test">测试</span>
                    </button>
                    <button class="editor-btn" id="editor-back-btn">
                        <span class="icon">🏠</span>
                        <span data-i18n="common.back">返回</span>
                    </button>
                </div>
            </header>
            
            <div class="editor-workspace">
                <div class="editor-sidebar">
                    <div class="element-palette">
                        <h3 data-i18n="editor.elements">元素面板</h3>
                        <div class="palette-grid">
                            <button class="palette-item" data-element="light-orb">
                                <span class="element-icon">💫</span>
                                <span data-i18n="elements.light_orb">光球</span>
                            </button>
                            <button class="palette-item" data-element="time-gate">
                                <span class="element-icon">🚪</span>
                                <span data-i18n="elements.time_gate">时间门</span>
                            </button>
                            <button class="palette-item" data-element="mirror">
                                <span class="element-icon">🪞</span>
                                <span data-i18n="elements.mirror">镜面</span>
                            </button>
                            <button class="palette-item" data-element="target">
                                <span class="element-icon">🎯</span>
                                <span data-i18n="elements.target">目标点</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="editor-canvas-area">
                    <canvas id="editor-canvas" width="800" height="600"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 设置屏幕 -->
    <div id="settings-screen" class="screen">
        <div class="settings-container">
            <header class="settings-header">
                <h2 data-i18n="settings.title">游戏设置</h2>
                <button class="close-btn" id="settings-close-btn">✕</button>
            </header>
            
            <div class="settings-content">
                <div class="setting-group">
                    <h3 data-i18n="settings.display">显示设置</h3>
                    <div class="setting-item">
                        <label data-i18n="settings.language">语言:</label>
                        <select id="language-select">
                            <option value="zh-CN">中文</option>
                            <option value="en-US">English</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label data-i18n="settings.theme">主题:</label>
                        <select id="theme-select">
                            <option value="dark" data-i18n="settings.theme_dark">深色</option>
                            <option value="light" data-i18n="settings.theme_light">浅色</option>
                        </select>
                    </div>
                </div>
                
                <div class="setting-group">
                    <h3 data-i18n="settings.gameplay">游戏设置</h3>
                    <div class="setting-item">
                        <label data-i18n="settings.difficulty">难度:</label>
                        <select id="difficulty-select">
                            <option value="easy" data-i18n="settings.difficulty_easy">简单</option>
                            <option value="normal" data-i18n="settings.difficulty_normal">普通</option>
                            <option value="hard" data-i18n="settings.difficulty_hard">困难</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label data-i18n="settings.auto_save">自动保存:</label>
                        <input type="checkbox" id="auto-save-checkbox" checked>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 通用模态框 -->
    <div id="modal-overlay" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">标题</h3>
                <button class="modal-close-btn" id="modal-close-btn">✕</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- 动态内容 -->
            </div>
            <div class="modal-footer" id="modal-footer">
                <!-- 动态按钮 -->
            </div>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/utils/math-utils.js"></script>
    <script src="js/core/time-engine.js"></script>
    <script src="js/core/dream-weaver.js"></script>
    <script src="js/core/level-manager.js"></script>
    <script src="js/core/physics.js"></script>
    <script src="js/core/renderer.js"></script>
    <script src="js/game/game-state.js"></script>
    <script src="js/game/player-manager.js"></script>
    <script src="js/game/level-data.js"></script>
    <script src="js/game/achievements.js"></script>
    <script src="js/ui/screen-manager.js"></script>
    <script src="js/ui/input-handler.js"></script>
    <script src="js/ui/ui-components.js"></script>
    <script src="js/ui/level-editor.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
