/* 时空织梦者 - 游戏界面样式 */

/* 游戏屏幕布局 */
#game-screen {
    background: var(--bg-primary);
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
}

.game-container {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 游戏HUD头部 */
.game-hud {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4) var(--space-6);
    background: var(--bg-overlay);
    border-bottom: 1px solid var(--border-primary);
    backdrop-filter: blur(10px);
    z-index: 100;
}

.hud-left,
.hud-right {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.hud-center {
    display: flex;
    align-items: center;
    gap: var(--space-6);
}

.hud-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--border-primary);
}

.hud-btn:hover {
    background: var(--primary-color);
    border-color: var(--border-accent);
    transform: scale(1.1);
    box-shadow: var(--shadow-glow);
}

.hud-btn .icon {
    font-size: var(--text-base);
}

/* 关卡和得分信息 */
.level-info,
.score-info {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-primary);
    font-weight: 500;
}

.level-info span:last-child,
.score-info span:last-child {
    color: var(--primary-light);
    font-weight: 600;
}

/* 时间控制按钮 */
.time-controls {
    display: flex;
    gap: var(--space-2);
    padding: var(--space-2);
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
}

.time-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: var(--radius-md);
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.time-btn:hover {
    background: var(--primary-color);
    color: var(--text-primary);
    transform: scale(1.1);
}

.time-btn.active {
    background: var(--gradient-time);
    color: var(--text-primary);
    box-shadow: 0 0 10px rgba(79, 172, 254, 0.5);
}

.time-btn .icon {
    font-size: var(--text-sm);
}

/* 游戏画布容器 */
.game-canvas-container {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    margin: var(--space-4);
    border-radius: var(--radius-2xl);
    border: 2px solid var(--border-primary);
    overflow: hidden;
}

#game-canvas {
    display: block;
    border-radius: var(--radius-xl);
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    box-shadow: inset 0 0 50px rgba(0, 0, 0, 0.5);
}

/* 画布覆盖层 */
.canvas-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

/* 时间线显示 */
.timeline-display {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    max-width: 600px;
    pointer-events: auto;
}

.timeline-track {
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
    position: relative;
    border: 1px solid var(--border-primary);
}

.timeline-cursor {
    position: absolute;
    top: -8px;
    width: 20px;
    height: 20px;
    background: var(--gradient-time);
    border-radius: var(--radius-full);
    border: 2px solid var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: 0 0 10px rgba(79, 172, 254, 0.5);
}

.timeline-cursor:hover {
    transform: scale(1.2);
    box-shadow: 0 0 20px rgba(79, 172, 254, 0.8);
}

/* 连接线绘制层 */
.connection-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 5;
}

.connection-line {
    stroke: var(--primary-light);
    stroke-width: 3;
    fill: none;
    stroke-dasharray: 5, 5;
    animation: dash-flow 2s linear infinite;
    filter: drop-shadow(0 0 5px rgba(139, 92, 246, 0.5));
}

.connection-line.active {
    stroke: var(--gradient-time);
    stroke-width: 4;
    stroke-dasharray: none;
    animation: glow-pulse 1s ease-in-out infinite alternate;
}

/* 游戏工具栏 */
.game-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4) var(--space-6);
    background: var(--bg-overlay);
    border-top: 1px solid var(--border-primary);
    backdrop-filter: blur(10px);
}

.tool-group,
.action-group {
    display: flex;
    gap: var(--space-3);
}

.tool-btn {
    width: 48px;
    height: 48px;
    border: none;
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--border-primary);
    position: relative;
}

.tool-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-secondary);
    transform: translateY(-2px);
}

.tool-btn.active {
    background: var(--primary-color);
    color: var(--text-primary);
    border-color: var(--border-accent);
    box-shadow: var(--shadow-glow);
}

.tool-btn .icon {
    font-size: var(--text-lg);
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    border: none;
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: var(--text-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    border: 1px solid var(--border-primary);
}

.action-btn:hover {
    background: var(--primary-color);
    border-color: var(--border-accent);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.action-btn .icon {
    font-size: var(--text-base);
}

/* 游戏元素样式 */
.game-element {
    position: absolute;
    border-radius: var(--radius-full);
    transition: all var(--transition-fast);
    cursor: pointer;
}

.game-element.light-orb {
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, #ffffff 0%, #4facfe 50%, #00f2fe 100%);
    box-shadow: 0 0 20px rgba(79, 172, 254, 0.6);
    animation: orb-pulse 2s ease-in-out infinite;
}

.game-element.time-gate {
    width: 60px;
    height: 80px;
    background: linear-gradient(180deg, transparent 0%, #6366f1 50%, transparent 100%);
    border: 2px solid var(--primary-light);
    border-radius: var(--radius-lg);
    box-shadow: inset 0 0 20px rgba(99, 102, 241, 0.3);
}

.game-element.mirror {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #e5e7eb 0%, #9ca3af 50%, #6b7280 100%);
    border: 2px solid var(--text-secondary);
    transform: rotate(45deg);
    box-shadow: 0 0 10px rgba(156, 163, 175, 0.5);
}

.game-element.target {
    width: 50px;
    height: 50px;
    background: radial-gradient(circle, transparent 30%, #10b981 40%, transparent 50%);
    border: 3px solid var(--success-color);
    animation: target-pulse 1.5s ease-in-out infinite;
}

.game-element:hover {
    transform: scale(1.1);
    z-index: 20;
}

.game-element.selected {
    box-shadow: 0 0 0 3px var(--primary-light);
    z-index: 15;
}

/* 粒子效果 */
.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-light);
    border-radius: var(--radius-full);
    pointer-events: none;
    animation: particle-float 3s ease-out forwards;
}

/* 游戏动画 */
@keyframes dash-flow {
    to { stroke-dashoffset: -10; }
}

@keyframes glow-pulse {
    0% { filter: drop-shadow(0 0 5px rgba(139, 92, 246, 0.5)); }
    100% { filter: drop-shadow(0 0 15px rgba(139, 92, 246, 0.9)); }
}

@keyframes orb-pulse {
    0%, 100% { 
        transform: scale(1);
        box-shadow: 0 0 20px rgba(79, 172, 254, 0.6);
    }
    50% { 
        transform: scale(1.1);
        box-shadow: 0 0 30px rgba(79, 172, 254, 0.9);
    }
}

@keyframes target-pulse {
    0%, 100% { 
        border-color: var(--success-color);
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
    }
    50% { 
        border-color: var(--primary-light);
        box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
    }
}

@keyframes particle-float {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-50px) scale(0.5);
    }
}

/* 游戏状态指示器 */
.game-status {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: var(--space-6) var(--space-8);
    background: var(--bg-overlay);
    border-radius: var(--radius-2xl);
    border: 1px solid var(--border-primary);
    text-align: center;
    backdrop-filter: blur(15px);
    z-index: 50;
}

.game-status h2 {
    font-size: var(--text-2xl);
    margin-bottom: var(--space-4);
    background: var(--gradient-time);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.game-status p {
    color: var(--text-secondary);
    margin-bottom: var(--space-6);
}

.game-status .status-buttons {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
}

.status-btn {
    padding: var(--space-3) var(--space-6);
    border: none;
    border-radius: var(--radius-lg);
    background: var(--primary-color);
    color: var(--text-primary);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.status-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.status-btn.secondary {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
}

.status-btn.secondary:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
}
