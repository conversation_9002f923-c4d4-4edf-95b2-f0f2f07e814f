/* 时空织梦者 - 主样式文件 */

/* CSS变量定义 - 深色主题配色 */
:root {
    /* 主色调 - 深蓝紫色系 */
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #8b5cf6;
    
    /* 背景色 */
    --bg-primary: #0f0f23;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-overlay: rgba(15, 15, 35, 0.95);
    
    /* 文字色 */
    --text-primary: #ffffff;
    --text-secondary: #a1a1aa;
    --text-muted: #71717a;
    
    /* 边框色 */
    --border-primary: #374151;
    --border-secondary: #4b5563;
    --border-accent: #6366f1;
    
    /* 状态色 */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;
    
    /* 渐变色 */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-time: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
    
    /* 动画时长 */
    --transition-fast: 0.15s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    
    /* 字体大小 */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    
    /* 间距 */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    
    /* 圆角 */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-full: 9999px;
}

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* 屏幕管理系统 */
.screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal) ease-in-out;
    z-index: 1;
}

.screen.active {
    opacity: 1;
    visibility: visible;
    z-index: 10;
}

.screen.transitioning {
    z-index: 5;
}

/* 加载屏幕样式 */
#loading-screen {
    background: var(--gradient-primary);
    flex-direction: column;
}

.loading-container {
    text-align: center;
    max-width: 400px;
    padding: var(--space-8);
}

.loading-logo {
    margin-bottom: var(--space-8);
}

.time-symbol {
    font-size: 4rem;
    margin-bottom: var(--space-4);
    animation: pulse 2s infinite;
}

.loading-logo h1 {
    font-size: var(--text-4xl);
    font-weight: 700;
    margin-bottom: var(--space-2);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.loading-logo p {
    font-size: var(--text-lg);
    opacity: 0.9;
    font-style: italic;
}

.loading-progress {
    width: 100%;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--space-4);
}

.progress-fill {
    height: 100%;
    background: var(--gradient-time);
    border-radius: var(--radius-full);
    width: 0%;
    animation: loading-progress 3s ease-in-out infinite;
}

.loading-text {
    font-size: var(--text-sm);
    opacity: 0.8;
    animation: fade-in-out 2s infinite;
}

/* 主菜单样式 */
#main-menu-screen {
    background: var(--bg-primary);
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
}

.menu-container {
    width: 100%;
    max-width: 600px;
    padding: var(--space-8);
    text-align: center;
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-12);
    padding: var(--space-6);
    background: var(--bg-overlay);
    border-radius: var(--radius-2xl);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-primary);
}

.title-section {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.game-icon {
    font-size: 3rem;
    animation: rotate-slow 10s linear infinite;
}

.title-text h1 {
    font-size: var(--text-3xl);
    font-weight: 700;
    margin-bottom: var(--space-1);
    background: var(--gradient-time);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title-text p {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-style: italic;
}

.header-controls {
    display: flex;
    gap: var(--space-2);
}

.icon-btn {
    width: 48px;
    height: 48px;
    border: none;
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--border-primary);
}

.icon-btn:hover {
    background: var(--primary-color);
    border-color: var(--border-accent);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

.icon-btn .icon {
    font-size: var(--text-lg);
}

/* 主菜单按钮 */
.main-menu {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    margin-bottom: var(--space-8);
}

.menu-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-3);
    padding: var(--space-4) var(--space-6);
    border: none;
    border-radius: var(--radius-xl);
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: var(--text-lg);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    border: 1px solid var(--border-primary);
    position: relative;
    overflow: hidden;
}

.menu-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: left var(--transition-normal);
    z-index: -1;
}

.menu-btn:hover::before {
    left: 0;
}

.menu-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
    border-color: var(--border-accent);
}

.menu-btn.primary {
    background: var(--gradient-primary);
    font-weight: 600;
    font-size: var(--text-xl);
    padding: var(--space-5) var(--space-8);
}

.menu-btn.primary::before {
    background: var(--gradient-secondary);
}

.btn-icon {
    font-size: var(--text-xl);
}

/* 玩家信息区域 */
.player-info {
    padding: var(--space-4);
    background: var(--bg-overlay);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
    backdrop-filter: blur(10px);
}

.current-player {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-3);
    font-size: var(--text-base);
}

.change-player-btn {
    padding: var(--space-2) var(--space-4);
    border: none;
    border-radius: var(--radius-md);
    background: var(--primary-color);
    color: var(--text-primary);
    font-size: var(--text-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.change-player-btn:hover {
    background: var(--primary-dark);
    transform: scale(1.05);
}

/* 动画定义 */
@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

@keyframes loading-progress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

@keyframes fade-in-out {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

@keyframes rotate-slow {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px var(--primary-color); }
    50% { box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-light); }
}

/* 通用工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.invisible { visibility: hidden; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.transition { transition: all var(--transition-normal); }
.transition-fast { transition: all var(--transition-fast); }
.transition-slow { transition: all var(--transition-slow); }

/* 响应式断点预留 */
@media (max-width: 768px) {
    .menu-container {
        padding: var(--space-4);
    }
    
    .game-header {
        flex-direction: column;
        gap: var(--space-4);
        text-align: center;
    }
    
    .title-section {
        flex-direction: column;
        gap: var(--space-2);
    }
}
