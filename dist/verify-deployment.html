<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Split-Second Spark 部署验证工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.8;
        }

        .verification-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .verification-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .verification-card h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-left: auto;
        }

        .status.success {
            background: #4caf50;
            color: white;
        }

        .status.error {
            background: #f44336;
            color: white;
        }

        .status.warning {
            background: #ff9800;
            color: white;
        }

        .status.pending {
            background: #2196f3;
            color: white;
        }

        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .test-item:last-child {
            border-bottom: none;
        }

        .test-name {
            flex: 1;
        }

        .test-result {
            font-weight: bold;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.9rem;
        }

        .test-result.pass {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
        }

        .test-result.fail {
            background: rgba(244, 67, 54, 0.2);
            color: #f44336;
        }

        .test-result.warn {
            background: rgba(255, 152, 0, 0.2);
            color: #ff9800;
        }

        .summary {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .summary h2 {
            margin-bottom: 15px;
            font-size: 1.8rem;
        }

        .summary-stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 20px 0;
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .actions {
            margin-top: 30px;
            text-align: center;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: transform 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn.secondary {
            background: rgba(255, 255, 255, 0.2);
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .verification-grid {
                grid-template-columns: 1fr;
            }
            
            .summary-stats {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 部署验证工具</h1>
            <p>检查 Split-Second Spark 部署状态和功能完整性</p>
        </div>

        <div class="verification-grid">
            <!-- 基础文件检查 -->
            <div class="verification-card">
                <h3>
                    📁 基础文件检查
                    <span class="status pending" id="files-status">检查中...</span>
                </h3>
                <div id="files-tests">
                    <div class="test-item">
                        <span class="test-name">主页面 (index.html)</span>
                        <span class="test-result" id="test-index">检查中</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">PWA配置 (manifest.json)</span>
                        <span class="test-result" id="test-manifest">检查中</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">样式文件 (styles/)</span>
                        <span class="test-result" id="test-styles">检查中</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">脚本文件 (js/)</span>
                        <span class="test-result" id="test-scripts">检查中</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">资源文件 (assets/)</span>
                        <span class="test-result" id="test-assets">检查中</span>
                    </div>
                </div>
            </div>

            <!-- 游戏文件检查 -->
            <div class="verification-card">
                <h3>
                    🎮 游戏文件检查
                    <span class="status pending" id="games-status">检查中...</span>
                </h3>
                <div id="games-tests">
                    <div class="test-item">
                        <span class="test-name">时空织梦者</span>
                        <span class="test-result" id="test-temporal">检查中</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">瞬光捕手</span>
                        <span class="test-result" id="test-spark">检查中</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">量子共鸣者</span>
                        <span class="test-result" id="test-quantum">检查中</span>
                    </div>
                </div>
            </div>

            <!-- 浏览器兼容性检查 -->
            <div class="verification-card">
                <h3>
                    🌐 浏览器兼容性
                    <span class="status pending" id="browser-status">检查中...</span>
                </h3>
                <div id="browser-tests">
                    <div class="test-item">
                        <span class="test-name">ES6+ 支持</span>
                        <span class="test-result" id="test-es6">检查中</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">Web Audio API</span>
                        <span class="test-result" id="test-audio">检查中</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">Canvas 2D</span>
                        <span class="test-result" id="test-canvas">检查中</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">LocalStorage</span>
                        <span class="test-result" id="test-storage">检查中</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">Service Worker</span>
                        <span class="test-result" id="test-sw">检查中</span>
                    </div>
                </div>
            </div>

            <!-- 性能检查 -->
            <div class="verification-card">
                <h3>
                    ⚡ 性能检查
                    <span class="status pending" id="performance-status">检查中...</span>
                </h3>
                <div id="performance-tests">
                    <div class="test-item">
                        <span class="test-name">页面加载时间</span>
                        <span class="test-result" id="test-load-time">检查中</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">资源压缩</span>
                        <span class="test-result" id="test-compression">检查中</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">缓存策略</span>
                        <span class="test-result" id="test-cache">检查中</span>
                    </div>
                    <div class="test-item">
                        <span class="test-name">HTTPS 支持</span>
                        <span class="test-result" id="test-https">检查中</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="summary">
            <h2>📊 验证总结</h2>
            <div class="summary-stats">
                <div class="stat">
                    <span class="stat-number" id="pass-count" style="color: #4caf50;">0</span>
                    <span class="stat-label">通过</span>
                </div>
                <div class="stat">
                    <span class="stat-number" id="warn-count" style="color: #ff9800;">0</span>
                    <span class="stat-label">警告</span>
                </div>
                <div class="stat">
                    <span class="stat-number" id="fail-count" style="color: #f44336;">0</span>
                    <span class="stat-label">失败</span>
                </div>
            </div>
            <p id="summary-text">正在进行验证检查...</p>
            
            <div class="actions">
                <a href="index.html" class="btn">🎮 开始游戏</a>
                <a href="#" class="btn secondary" onclick="location.reload()">🔄 重新检查</a>
            </div>
        </div>
    </div>

    <script>
        // 部署验证脚本
        class DeploymentVerifier {
            constructor() {
                this.tests = {
                    pass: 0,
                    warn: 0,
                    fail: 0
                };
                this.startTime = performance.now();
            }

            async runAllTests() {
                console.log('🔍 开始部署验证...');
                
                // 运行各类测试
                await this.testFiles();
                await this.testGames();
                await this.testBrowser();
                await this.testPerformance();
                
                // 更新总结
                this.updateSummary();
                
                console.log('✅ 验证完成');
            }

            async testFiles() {
                const tests = [
                    { id: 'test-index', url: 'index.html', name: '主页面' },
                    { id: 'test-manifest', url: 'manifest.json', name: 'PWA配置' },
                    { id: 'test-styles', url: 'styles/main.css', name: '样式文件' },
                    { id: 'test-scripts', url: 'js/main.js', name: '脚本文件' },
                    { id: 'test-assets', url: 'assets/images/icon-192x192.png', name: '资源文件' }
                ];

                for (const test of tests) {
                    try {
                        const response = await fetch(test.url, { method: 'HEAD' });
                        if (response.ok) {
                            this.setTestResult(test.id, 'pass', '✅ 正常');
                        } else {
                            this.setTestResult(test.id, 'fail', '❌ 缺失');
                        }
                    } catch (error) {
                        this.setTestResult(test.id, 'fail', '❌ 错误');
                    }
                }

                this.updateSectionStatus('files-status');
            }

            async testGames() {
                const games = [
                    { id: 'test-temporal', url: '时空织梦者/index.html' },
                    { id: 'test-spark', url: '瞬光捕手/index.html' },
                    { id: 'test-quantum', url: '量子共鸣者/index.html' }
                ];

                for (const game of games) {
                    try {
                        const response = await fetch(game.url, { method: 'HEAD' });
                        if (response.ok) {
                            this.setTestResult(game.id, 'pass', '✅ 可用');
                        } else {
                            this.setTestResult(game.id, 'fail', '❌ 缺失');
                        }
                    } catch (error) {
                        this.setTestResult(game.id, 'fail', '❌ 错误');
                    }
                }

                this.updateSectionStatus('games-status');
            }

            testBrowser() {
                // ES6+ 支持
                try {
                    eval('const test = () => {}; class Test {}');
                    this.setTestResult('test-es6', 'pass', '✅ 支持');
                } catch (e) {
                    this.setTestResult('test-es6', 'fail', '❌ 不支持');
                }

                // Web Audio API
                if (window.AudioContext || window.webkitAudioContext) {
                    this.setTestResult('test-audio', 'pass', '✅ 支持');
                } else {
                    this.setTestResult('test-audio', 'warn', '⚠️ 不支持');
                }

                // Canvas 2D
                try {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    if (ctx) {
                        this.setTestResult('test-canvas', 'pass', '✅ 支持');
                    } else {
                        this.setTestResult('test-canvas', 'fail', '❌ 不支持');
                    }
                } catch (e) {
                    this.setTestResult('test-canvas', 'fail', '❌ 错误');
                }

                // LocalStorage
                try {
                    localStorage.setItem('test', 'value');
                    localStorage.removeItem('test');
                    this.setTestResult('test-storage', 'pass', '✅ 支持');
                } catch (e) {
                    this.setTestResult('test-storage', 'warn', '⚠️ 受限');
                }

                // Service Worker
                if ('serviceWorker' in navigator) {
                    this.setTestResult('test-sw', 'pass', '✅ 支持');
                } else {
                    this.setTestResult('test-sw', 'warn', '⚠️ 不支持');
                }

                this.updateSectionStatus('browser-status');
            }

            testPerformance() {
                // 页面加载时间
                const loadTime = performance.now() - this.startTime;
                if (loadTime < 1000) {
                    this.setTestResult('test-load-time', 'pass', `✅ ${loadTime.toFixed(0)}ms`);
                } else if (loadTime < 3000) {
                    this.setTestResult('test-load-time', 'warn', `⚠️ ${loadTime.toFixed(0)}ms`);
                } else {
                    this.setTestResult('test-load-time', 'fail', `❌ ${loadTime.toFixed(0)}ms`);
                }

                // HTTPS 支持
                if (location.protocol === 'https:') {
                    this.setTestResult('test-https', 'pass', '✅ HTTPS');
                } else if (location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
                    this.setTestResult('test-https', 'warn', '⚠️ 本地');
                } else {
                    this.setTestResult('test-https', 'warn', '⚠️ HTTP');
                }

                // 资源压缩 (简单检查)
                this.setTestResult('test-compression', 'warn', '⚠️ 未检查');
                
                // 缓存策略 (简单检查)
                this.setTestResult('test-cache', 'warn', '⚠️ 未检查');

                this.updateSectionStatus('performance-status');
            }

            setTestResult(testId, type, text) {
                const element = document.getElementById(testId);
                if (element) {
                    element.textContent = text;
                    element.className = `test-result ${type}`;
                    this.tests[type]++;
                }
            }

            updateSectionStatus(statusId) {
                // 这里可以根据该部分的测试结果更新状态
                const statusElement = document.getElementById(statusId);
                if (statusElement) {
                    statusElement.textContent = '✅ 完成';
                    statusElement.className = 'status success';
                }
            }

            updateSummary() {
                document.getElementById('pass-count').textContent = this.tests.pass;
                document.getElementById('warn-count').textContent = this.tests.warn;
                document.getElementById('fail-count').textContent = this.tests.fail;

                const total = this.tests.pass + this.tests.warn + this.tests.fail;
                let summaryText = '';

                if (this.tests.fail === 0) {
                    if (this.tests.warn === 0) {
                        summaryText = '🎉 所有检查都通过了！部署状态良好，可以正常使用。';
                    } else {
                        summaryText = `✅ 基本功能正常，有 ${this.tests.warn} 个警告项目需要注意。`;
                    }
                } else {
                    summaryText = `❌ 发现 ${this.tests.fail} 个严重问题，需要修复后才能正常使用。`;
                }

                document.getElementById('summary-text').textContent = summaryText;
            }
        }

        // 页面加载完成后开始验证
        document.addEventListener('DOMContentLoaded', () => {
            const verifier = new DeploymentVerifier();
            verifier.runAllTests();
        });
    </script>
</body>
</html>
