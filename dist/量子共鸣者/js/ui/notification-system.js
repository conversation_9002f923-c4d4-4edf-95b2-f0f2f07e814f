/**
 * 量子共鸣者 - 消息通知系统
 * 提供优雅的消息提醒功能，替代传统的alert弹框
 */

class NotificationSystem {
    constructor() {
        this.notifications = [];
        this.container = null;
        this.maxNotifications = 5;
        this.defaultDuration = 4000; // 4秒
        
        // 通知类型配置
        this.types = {
            info: {
                icon: 'ℹ️',
                className: 'notification-info',
                duration: 4000
            },
            success: {
                icon: '✅',
                className: 'notification-success',
                duration: 3000
            },
            warning: {
                icon: '⚠️',
                className: 'notification-warning',
                duration: 5000
            },
            error: {
                icon: '❌',
                className: 'notification-error',
                duration: 6000
            },
            game: {
                icon: '🎮',
                className: 'notification-game',
                duration: 4000
            }
        };
        
        this.init();
        console.log('📢 消息通知系统已创建');
    }

    /**
     * 初始化通知系统
     */
    init() {
        this.createContainer();
        this.addStyles();
    }

    /**
     * 创建通知容器
     */
    createContainer() {
        this.container = document.createElement('div');
        this.container.id = 'notification-container';
        this.container.className = 'notification-container';
        document.body.appendChild(this.container);
    }

    /**
     * 添加样式
     */
    addStyles() {
        if (document.getElementById('notification-styles')) {
            return; // 样式已存在
        }

        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                pointer-events: none;
                max-width: 400px;
            }

            .notification {
                background: var(--ui-surface, #1a1a2e);
                border: 2px solid var(--ui-border, #16213e);
                border-radius: 12px;
                padding: 16px 20px;
                margin-bottom: 12px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                backdrop-filter: blur(10px);
                pointer-events: auto;
                transform: translateX(100%);
                opacity: 0;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
                max-width: 100%;
                word-wrap: break-word;
            }

            .notification.show {
                transform: translateX(0);
                opacity: 1;
            }

            .notification.hide {
                transform: translateX(100%);
                opacity: 0;
            }

            .notification-header {
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 8px;
            }

            .notification-icon {
                font-size: 20px;
                flex-shrink: 0;
            }

            .notification-title {
                font-weight: bold;
                color: var(--text-primary, #ffffff);
                font-size: 16px;
                flex: 1;
            }

            .notification-close {
                background: none;
                border: none;
                color: var(--text-secondary, #8892b0);
                cursor: pointer;
                font-size: 18px;
                padding: 0;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 4px;
                transition: all 0.2s ease;
            }

            .notification-close:hover {
                background: rgba(255, 255, 255, 0.1);
                color: var(--text-primary, #ffffff);
            }

            .notification-message {
                color: var(--text-secondary, #8892b0);
                font-size: 14px;
                line-height: 1.4;
                margin: 0;
            }

            .notification-progress {
                position: absolute;
                bottom: 0;
                left: 0;
                height: 3px;
                background: var(--ui-primary, #00d4ff);
                transition: width linear;
                border-radius: 0 0 12px 12px;
            }

            /* 不同类型的通知样式 */
            .notification-info {
                border-color: var(--ui-primary, #00d4ff);
            }

            .notification-info .notification-progress {
                background: var(--ui-primary, #00d4ff);
            }

            .notification-success {
                border-color: #00ff88;
            }

            .notification-success .notification-progress {
                background: #00ff88;
            }

            .notification-warning {
                border-color: #ffaa00;
            }

            .notification-warning .notification-progress {
                background: #ffaa00;
            }

            .notification-error {
                border-color: #ff4444;
            }

            .notification-error .notification-progress {
                background: #ff4444;
            }

            .notification-game {
                border-color: var(--ui-accent, #ff6b6b);
            }

            .notification-game .notification-progress {
                background: var(--ui-accent, #ff6b6b);
            }

            /* 响应式设计 */
            @media (max-width: 480px) {
                .notification-container {
                    top: 10px;
                    right: 10px;
                    left: 10px;
                    max-width: none;
                }

                .notification {
                    margin-bottom: 8px;
                    padding: 12px 16px;
                }

                .notification-title {
                    font-size: 15px;
                }

                .notification-message {
                    font-size: 13px;
                }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * 显示通知
     * @param {string} message - 消息内容
     * @param {string} type - 通知类型 (info, success, warning, error, game)
     * @param {Object} options - 选项
     */
    show(message, type = 'info', options = {}) {
        const config = this.types[type] || this.types.info;
        const notification = this.createNotification(message, config, options);
        
        this.addNotification(notification);
        this.showNotification(notification);
        
        // 自动隐藏
        if (options.duration !== 0) {
            const duration = options.duration || config.duration;
            this.scheduleHide(notification, duration);
        }
        
        return notification;
    }

    /**
     * 创建通知元素
     */
    createNotification(message, config, options) {
        const notification = document.createElement('div');
        notification.className = `notification ${config.className}`;
        
        const title = options.title || this.getDefaultTitle(config.className);
        
        notification.innerHTML = `
            <div class="notification-header">
                <span class="notification-icon">${config.icon}</span>
                <span class="notification-title">${title}</span>
                <button class="notification-close" aria-label="关闭">×</button>
            </div>
            <p class="notification-message">${message}</p>
            <div class="notification-progress"></div>
        `;
        
        // 添加关闭按钮事件
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => this.hide(notification));
        
        return notification;
    }

    /**
     * 获取默认标题
     */
    getDefaultTitle(className) {
        const titles = {
            'notification-info': '信息',
            'notification-success': '成功',
            'notification-warning': '警告',
            'notification-error': '错误',
            'notification-game': '游戏'
        };
        return titles[className] || '通知';
    }

    /**
     * 添加通知到容器
     */
    addNotification(notification) {
        // 如果通知数量超过限制，移除最旧的
        while (this.notifications.length >= this.maxNotifications) {
            const oldest = this.notifications.shift();
            this.hide(oldest);
        }
        
        this.notifications.push(notification);
        this.container.appendChild(notification);
    }

    /**
     * 显示通知动画
     */
    showNotification(notification) {
        // 强制重排以确保动画生效
        notification.offsetHeight;
        notification.classList.add('show');
    }

    /**
     * 安排自动隐藏
     */
    scheduleHide(notification, duration) {
        const progressBar = notification.querySelector('.notification-progress');
        
        // 设置进度条动画
        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.style.transitionDuration = duration + 'ms';
            
            // 开始进度条动画
            setTimeout(() => {
                progressBar.style.width = '0%';
            }, 50);
        }
        
        // 设置自动隐藏
        setTimeout(() => {
            this.hide(notification);
        }, duration);
    }

    /**
     * 隐藏通知
     */
    hide(notification) {
        if (!notification || !notification.parentNode) {
            return;
        }
        
        notification.classList.remove('show');
        notification.classList.add('hide');
        
        // 从数组中移除
        const index = this.notifications.indexOf(notification);
        if (index > -1) {
            this.notifications.splice(index, 1);
        }
        
        // 动画完成后移除DOM元素
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    /**
     * 清除所有通知
     */
    clear() {
        this.notifications.forEach(notification => this.hide(notification));
        this.notifications = [];
    }

    // 便捷方法
    info(message, options = {}) {
        return this.show(message, 'info', options);
    }

    success(message, options = {}) {
        return this.show(message, 'success', options);
    }

    warning(message, options = {}) {
        return this.show(message, 'warning', options);
    }

    error(message, options = {}) {
        return this.show(message, 'error', options);
    }

    game(message, options = {}) {
        return this.show(message, 'game', options);
    }
}

// 创建全局通知系统实例
window.notificationSystem = new NotificationSystem();

// 提供全局便捷方法
window.showNotification = (message, type, options) => {
    return window.notificationSystem.show(message, type, options);
};

console.log('📢 消息通知系统已加载');
