/**
 * 量子共鸣者 - 屏幕管理器
 * 管理不同游戏屏幕的显示和切换
 */

class ScreenManager {
    constructor() {
        this.screens = new Map();
        this.currentScreen = null;
        this.previousScreen = null;
        this.transitionDuration = 300; // 毫秒
        this.isTransitioning = false;
        
        // 屏幕切换动画类型
        this.transitionTypes = {
            FADE: 'fade',
            SLIDE_LEFT: 'slide-left',
            SLIDE_RIGHT: 'slide-right',
            SLIDE_UP: 'slide-up',
            SLIDE_DOWN: 'slide-down',
            SCALE: 'scale'
        };
        
        this.init();
    }

    /**
     * 初始化屏幕管理器
     */
    init() {
        // 注册所有屏幕
        this.registerScreens();
        
        // 设置CSS动画
        this.setupTransitionStyles();
        
        console.log('📱 屏幕管理器初始化完成');
    }

    /**
     * 注册所有屏幕
     */
    registerScreens() {
        const screenElements = document.querySelectorAll('.screen');
        
        screenElements.forEach(element => {
            const screenId = element.id;
            this.screens.set(screenId, {
                element: element,
                id: screenId,
                isActive: element.classList.contains('active'),
                onEnter: null,
                onExit: null,
                onUpdate: null
            });
        });
        
        // 找到当前活动屏幕
        for (let [id, screen] of this.screens) {
            if (screen.isActive) {
                this.currentScreen = id;
                break;
            }
        }
        
        console.log(`📱 已注册 ${this.screens.size} 个屏幕`);
    }

    /**
     * 设置过渡动画样式
     * 注意：基础.screen样式已在main.css中定义，这里只添加动画相关样式
     */
    setupTransitionStyles() {
        const style = document.createElement('style');
        style.textContent = `
            /* 屏幕管理器动画样式 - 不覆盖main.css中的基础样式 */
            .screen {
                transition: all ${this.transitionDuration}ms ease-in-out;
                transform: translateX(0);
            }

            /* 确保active状态的z-index优先级 */
            .screen.active {
                z-index: 10;
            }
            
            .screen.transitioning {
                z-index: 5;
            }
            
            /* 淡入淡出动画 */
            .screen.fade-enter {
                opacity: 0;
            }
            
            .screen.fade-enter-active {
                opacity: 1;
            }
            
            .screen.fade-exit {
                opacity: 1;
            }
            
            .screen.fade-exit-active {
                opacity: 0;
            }
            
            /* 滑动动画 */
            .screen.slide-left-enter {
                transform: translateX(100%);
            }
            
            .screen.slide-left-enter-active {
                transform: translateX(0);
            }
            
            .screen.slide-left-exit {
                transform: translateX(0);
            }
            
            .screen.slide-left-exit-active {
                transform: translateX(-100%);
            }
            
            .screen.slide-right-enter {
                transform: translateX(-100%);
            }
            
            .screen.slide-right-enter-active {
                transform: translateX(0);
            }
            
            .screen.slide-right-exit {
                transform: translateX(0);
            }
            
            .screen.slide-right-exit-active {
                transform: translateX(100%);
            }
            
            /* 缩放动画 */
            .screen.scale-enter {
                transform: scale(0.8);
                opacity: 0;
            }
            
            .screen.scale-enter-active {
                transform: scale(1);
                opacity: 1;
            }
            
            .screen.scale-exit {
                transform: scale(1);
                opacity: 1;
            }
            
            .screen.scale-exit-active {
                transform: scale(1.2);
                opacity: 0;
            }
        `;
        
        document.head.appendChild(style);
    }

    /**
     * 显示屏幕
     */
    async showScreen(screenId, transitionType = this.transitionTypes.FADE, data = {}) {
        if (this.isTransitioning) {
            console.warn('⚠️ 屏幕正在切换中，请稍后再试');
            return false;
        }
        
        const screen = this.screens.get(screenId);
        if (!screen) {
            console.error(`❌ 屏幕不存在: ${screenId}`);
            return false;
        }
        
        if (this.currentScreen === screenId) {
            console.log(`📱 屏幕 ${screenId} 已经是当前屏幕`);
            return true;
        }
        
        this.isTransitioning = true;
        
        try {
            // 执行屏幕切换
            await this.performTransition(screenId, transitionType, data);
            
            // 更新当前屏幕
            this.previousScreen = this.currentScreen;
            this.currentScreen = screenId;
            
            console.log(`📱 已切换到屏幕: ${screenId}`);
            return true;
            
        } catch (error) {
            console.error('❌ 屏幕切换失败:', error);
            return false;
        } finally {
            this.isTransitioning = false;
        }
    }

    /**
     * 执行屏幕切换动画
     */
    async performTransition(newScreenId, transitionType, data) {
        const newScreen = this.screens.get(newScreenId);
        const currentScreen = this.currentScreen ? this.screens.get(this.currentScreen) : null;
        
        // 调用新屏幕的进入回调
        if (newScreen.onEnter) {
            newScreen.onEnter(data);
        }
        
        // 调用当前屏幕的退出回调
        if (currentScreen && currentScreen.onExit) {
            currentScreen.onExit();
        }
        
        // 执行动画
        await this.animateTransition(currentScreen, newScreen, transitionType);
    }

    /**
     * 执行动画过渡
     */
    animateTransition(currentScreen, newScreen, transitionType) {
        return new Promise((resolve) => {
            // 准备新屏幕
            newScreen.element.classList.add('transitioning');
            newScreen.element.classList.add(`${transitionType}-enter`);
            newScreen.element.style.visibility = 'visible';
            
            // 准备当前屏幕退出动画
            if (currentScreen) {
                currentScreen.element.classList.add('transitioning');
                currentScreen.element.classList.add(`${transitionType}-exit`);
            }
            
            // 强制重绘
            newScreen.element.offsetHeight;
            
            // 开始动画
            requestAnimationFrame(() => {
                newScreen.element.classList.add(`${transitionType}-enter-active`);
                newScreen.element.classList.remove(`${transitionType}-enter`);
                
                if (currentScreen) {
                    currentScreen.element.classList.add(`${transitionType}-exit-active`);
                    currentScreen.element.classList.remove(`${transitionType}-exit`);
                }
                
                // 动画完成后清理
                setTimeout(() => {
                    this.cleanupTransition(currentScreen, newScreen, transitionType);
                    resolve();
                }, this.transitionDuration);
            });
        });
    }

    /**
     * 清理过渡动画
     */
    cleanupTransition(currentScreen, newScreen, transitionType) {
        // 清理新屏幕
        newScreen.element.classList.remove('transitioning');
        newScreen.element.classList.remove(`${transitionType}-enter-active`);
        newScreen.element.classList.add('active');
        newScreen.isActive = true;
        
        // 清理当前屏幕
        if (currentScreen) {
            currentScreen.element.classList.remove('active');
            currentScreen.element.classList.remove('transitioning');
            currentScreen.element.classList.remove(`${transitionType}-exit-active`);
            currentScreen.element.style.visibility = 'hidden';
            currentScreen.isActive = false;
        }
    }

    /**
     * 返回上一个屏幕
     */
    goBack(transitionType = this.transitionTypes.SLIDE_RIGHT) {
        if (this.previousScreen) {
            return this.showScreen(this.previousScreen, transitionType);
        }
        return false;
    }

    /**
     * 注册屏幕回调
     */
    registerScreenCallbacks(screenId, callbacks) {
        const screen = this.screens.get(screenId);
        if (screen) {
            if (callbacks.onEnter) screen.onEnter = callbacks.onEnter;
            if (callbacks.onExit) screen.onExit = callbacks.onExit;
            if (callbacks.onUpdate) screen.onUpdate = callbacks.onUpdate;
        }
    }

    /**
     * 更新屏幕
     */
    updateCurrentScreen(deltaTime) {
        if (this.currentScreen) {
            const screen = this.screens.get(this.currentScreen);
            if (screen && screen.onUpdate) {
                screen.onUpdate(deltaTime);
            }
        }
    }

    /**
     * 获取当前屏幕ID
     */
    getCurrentScreen() {
        return this.currentScreen;
    }

    /**
     * 获取上一个屏幕ID
     */
    getPreviousScreen() {
        return this.previousScreen;
    }

    /**
     * 检查屏幕是否存在
     */
    hasScreen(screenId) {
        return this.screens.has(screenId);
    }

    /**
     * 获取屏幕元素
     */
    getScreenElement(screenId) {
        const screen = this.screens.get(screenId);
        return screen ? screen.element : null;
    }

    /**
     * 设置过渡持续时间
     */
    setTransitionDuration(duration) {
        this.transitionDuration = duration;
    }

    /**
     * 获取所有屏幕信息
     */
    getScreensInfo() {
        const info = {};
        for (let [id, screen] of this.screens) {
            info[id] = {
                id: screen.id,
                isActive: screen.isActive,
                hasCallbacks: {
                    onEnter: !!screen.onEnter,
                    onExit: !!screen.onExit,
                    onUpdate: !!screen.onUpdate
                }
            };
        }
        return info;
    }

    /**
     * 销毁屏幕管理器
     */
    destroy() {
        // 清理所有屏幕状态
        for (let [id, screen] of this.screens) {
            screen.element.classList.remove('active', 'transitioning');
            screen.onEnter = null;
            screen.onExit = null;
            screen.onUpdate = null;
        }
        
        this.screens.clear();
        this.currentScreen = null;
        this.previousScreen = null;
        this.isTransitioning = false;
        
        console.log('📱 屏幕管理器已销毁');
    }
}

// 导出屏幕管理器类
window.ScreenManager = ScreenManager;
