/**
 * 量子共鸣者 - 设置面板组件
 * 负责管理游戏设置界面，包括音频、图形、控制等设置
 */

class SettingsPanel {
    constructor() {
        this.isInitialized = false;
        this.isVisible = false;
        
        // 设置面板元素
        this.elements = {
            container: null,
            tabs: [],
            panels: [],
            saveButton: null,
            resetButton: null,
            cancelButton: null
        };
        
        // 当前设置
        this.currentSettings = {
            audio: {
                masterVolume: 0.8,
                musicVolume: 0.7,
                sfxVolume: 0.9,
                uiVolume: 0.6,
                enableAudio: true,
                audioTheme: 'quantum'
            },
            graphics: {
                quality: 'high',
                renderMode: '3d',
                particleCount: 'medium',
                enableEffects: true,
                enableGlow: true,
                frameRate: 60
            },
            controls: {
                mousesensitivity: 0.5,
                keyboardLayout: 'qwerty',
                enableTouch: true,
                enableMicrophone: false,
                microphoneSensitivity: 0.7
            },
            gameplay: {
                difficulty: 'normal',
                autoSave: true,
                showTutorial: true,
                enableHints: true,
                pauseOnFocusLoss: true
            },
            accessibility: {
                colorBlindMode: 'none',
                highContrast: false,
                largeText: false,
                reduceMotion: false,
                screenReader: false
            },
            language: 'zh-CN'
        };
        
        // 临时设置（用于取消时恢复）
        this.tempSettings = null;
        
        console.log('⚙️ 设置面板组件已创建');
    }

    /**
     * 初始化设置面板
     */
    init() {
        try {
            // 获取设置面板元素
            this.getSettingsElements();
            
            // 创建设置面板结构
            this.createSettingsStructure();
            
            // 加载当前设置
            this.loadSettings();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 初始化标签页
            this.initTabs();
            
            this.isInitialized = true;
            console.log('✅ 设置面板初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 设置面板初始化失败:', error);
            return false;
        }
    }

    /**
     * 获取设置面板元素
     */
    getSettingsElements() {
        // 使用现有的HTML结构
        this.elements.container = document.getElementById('settings-screen');
        this.elements.saveButton = document.getElementById('save-settings-btn');
        this.elements.resetButton = document.getElementById('reset-settings-btn');

        console.log('🔍 设置面板元素查找结果:');
        console.log('  - 容器:', this.elements.container ? '✅' : '❌');
        console.log('  - 保存按钮:', this.elements.saveButton ? '✅' : '❌');
        console.log('  - 重置按钮:', this.elements.resetButton ? '✅' : '❌');
    }

    /**
     * 创建设置面板结构
     */
    createSettingsStructure() {
        if (!this.elements.container) {
            console.warn('⚠️ 设置面板容器不存在，跳过结构创建');
            return;
        }

        // 使用现有的HTML结构，不重新创建
        console.log('✅ 使用现有的设置面板HTML结构');

        // 更新元素引用（如果需要标签页功能的话）
        this.elements.tabs = this.elements.container.querySelectorAll('.settings-tab');
        this.elements.panels = this.elements.container.querySelectorAll('.settings-panel');
        this.elements.saveButton = document.getElementById('settingsSave');
        this.elements.resetButton = document.getElementById('settingsReset');
        this.elements.cancelButton = document.getElementById('settingsCancel');
    }

    /**
     * 创建音频设置面板
     */
    createAudioPanel() {
        return `
            <div class="settings-panel active" data-panel="audio">
                <div class="settings-group">
                    <h3>音量设置</h3>
                    <div class="settings-item">
                        <label>主音量</label>
                        <div class="slider-container">
                            <input type="range" id="masterVolume" min="0" max="100" value="80" class="quantum-slider">
                            <span class="slider-value">80%</span>
                        </div>
                    </div>
                    <div class="settings-item">
                        <label>音乐音量</label>
                        <div class="slider-container">
                            <input type="range" id="musicVolume" min="0" max="100" value="70" class="quantum-slider">
                            <span class="slider-value">70%</span>
                        </div>
                    </div>
                    <div class="settings-item">
                        <label>音效音量</label>
                        <div class="slider-container">
                            <input type="range" id="sfxVolume" min="0" max="100" value="90" class="quantum-slider">
                            <span class="slider-value">90%</span>
                        </div>
                    </div>
                    <div class="settings-item">
                        <label>界面音量</label>
                        <div class="slider-container">
                            <input type="range" id="uiVolume" min="0" max="100" value="60" class="quantum-slider">
                            <span class="slider-value">60%</span>
                        </div>
                    </div>
                </div>
                
                <div class="settings-group">
                    <h3>音频选项</h3>
                    <div class="settings-item">
                        <label>启用音频</label>
                        <input type="checkbox" id="enableAudio" checked class="quantum-checkbox">
                    </div>
                    <div class="settings-item">
                        <label>音频主题</label>
                        <select id="audioTheme" class="quantum-select">
                            <option value="quantum">量子</option>
                            <option value="space">太空</option>
                            <option value="energy">能量</option>
                        </select>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建图形设置面板
     */
    createGraphicsPanel() {
        return `
            <div class="settings-panel" data-panel="graphics">
                <div class="settings-group">
                    <h3>渲染设置</h3>
                    <div class="settings-item">
                        <label>图形质量</label>
                        <select id="quality" class="quantum-select">
                            <option value="low">低</option>
                            <option value="medium">中</option>
                            <option value="high">高</option>
                            <option value="ultra">超高</option>
                        </select>
                    </div>
                    <div class="settings-item">
                        <label>渲染模式</label>
                        <select id="renderMode" class="quantum-select">
                            <option value="2d">2D模式</option>
                            <option value="3d">3D模式</option>
                            <option value="auto">自动</option>
                        </select>
                    </div>
                    <div class="settings-item">
                        <label>粒子数量</label>
                        <select id="particleCount" class="quantum-select">
                            <option value="low">少</option>
                            <option value="medium">中</option>
                            <option value="high">多</option>
                            <option value="ultra">极多</option>
                        </select>
                    </div>
                    <div class="settings-item">
                        <label>目标帧率</label>
                        <select id="frameRate" class="quantum-select">
                            <option value="30">30 FPS</option>
                            <option value="60">60 FPS</option>
                            <option value="120">120 FPS</option>
                            <option value="unlimited">不限制</option>
                        </select>
                    </div>
                </div>
                
                <div class="settings-group">
                    <h3>视觉效果</h3>
                    <div class="settings-item">
                        <label>启用特效</label>
                        <input type="checkbox" id="enableEffects" checked class="quantum-checkbox">
                    </div>
                    <div class="settings-item">
                        <label>启用发光</label>
                        <input type="checkbox" id="enableGlow" checked class="quantum-checkbox">
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建控制设置面板
     */
    createControlsPanel() {
        return `
            <div class="settings-panel" data-panel="controls">
                <div class="settings-group">
                    <h3>鼠标设置</h3>
                    <div class="settings-item">
                        <label>鼠标灵敏度</label>
                        <div class="slider-container">
                            <input type="range" id="mouseSensitivity" min="0.1" max="2.0" step="0.1" value="0.5" class="quantum-slider">
                            <span class="slider-value">0.5</span>
                        </div>
                    </div>
                </div>
                
                <div class="settings-group">
                    <h3>键盘设置</h3>
                    <div class="settings-item">
                        <label>键盘布局</label>
                        <select id="keyboardLayout" class="quantum-select">
                            <option value="qwerty">QWERTY</option>
                            <option value="azerty">AZERTY</option>
                            <option value="dvorak">Dvorak</option>
                        </select>
                    </div>
                </div>
                
                <div class="settings-group">
                    <h3>输入选项</h3>
                    <div class="settings-item">
                        <label>启用触摸控制</label>
                        <input type="checkbox" id="enableTouch" checked class="quantum-checkbox">
                    </div>
                    <div class="settings-item">
                        <label>启用麦克风输入</label>
                        <input type="checkbox" id="enableMicrophone" class="quantum-checkbox">
                    </div>
                    <div class="settings-item">
                        <label>麦克风灵敏度</label>
                        <div class="slider-container">
                            <input type="range" id="microphoneSensitivity" min="0.1" max="1.0" step="0.1" value="0.7" class="quantum-slider">
                            <span class="slider-value">0.7</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建游戏设置面板
     */
    createGameplayPanel() {
        return `
            <div class="settings-panel" data-panel="gameplay">
                <div class="settings-group">
                    <h3>游戏选项</h3>
                    <div class="settings-item">
                        <label>难度等级</label>
                        <select id="difficulty" class="quantum-select">
                            <option value="easy">简单</option>
                            <option value="normal">普通</option>
                            <option value="hard">困难</option>
                            <option value="expert">专家</option>
                        </select>
                    </div>
                    <div class="settings-item">
                        <label>自动保存</label>
                        <input type="checkbox" id="autoSave" checked class="quantum-checkbox">
                    </div>
                    <div class="settings-item">
                        <label>显示教程</label>
                        <input type="checkbox" id="showTutorial" checked class="quantum-checkbox">
                    </div>
                    <div class="settings-item">
                        <label>启用提示</label>
                        <input type="checkbox" id="enableHints" checked class="quantum-checkbox">
                    </div>
                    <div class="settings-item">
                        <label>失去焦点时暂停</label>
                        <input type="checkbox" id="pauseOnFocusLoss" checked class="quantum-checkbox">
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建辅助功能面板
     */
    createAccessibilityPanel() {
        return `
            <div class="settings-panel" data-panel="accessibility">
                <div class="settings-group">
                    <h3>视觉辅助</h3>
                    <div class="settings-item">
                        <label>色盲模式</label>
                        <select id="colorBlindMode" class="quantum-select">
                            <option value="none">无</option>
                            <option value="protanopia">红色盲</option>
                            <option value="deuteranopia">绿色盲</option>
                            <option value="tritanopia">蓝色盲</option>
                        </select>
                    </div>
                    <div class="settings-item">
                        <label>高对比度</label>
                        <input type="checkbox" id="highContrast" class="quantum-checkbox">
                    </div>
                    <div class="settings-item">
                        <label>大字体</label>
                        <input type="checkbox" id="largeText" class="quantum-checkbox">
                    </div>
                    <div class="settings-item">
                        <label>减少动画</label>
                        <input type="checkbox" id="reduceMotion" class="quantum-checkbox">
                    </div>
                </div>
                
                <div class="settings-group">
                    <h3>其他辅助</h3>
                    <div class="settings-item">
                        <label>屏幕阅读器支持</label>
                        <input type="checkbox" id="screenReader" class="quantum-checkbox">
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建语言设置面板
     */
    createLanguagePanel() {
        return `
            <div class="settings-panel" data-panel="language">
                <div class="settings-group">
                    <h3>语言选择</h3>
                    <div class="settings-item">
                        <label>界面语言</label>
                        <select id="language" class="quantum-select">
                            <option value="zh-CN">简体中文</option>
                            <option value="zh-TW">繁體中文</option>
                            <option value="en-US">English</option>
                            <option value="ja-JP">日本語</option>
                            <option value="ko-KR">한국어</option>
                        </select>
                    </div>
                </div>
                
                <div class="settings-group">
                    <h3>语言说明</h3>
                    <p class="settings-description">
                        更改语言设置后需要重新启动游戏才能生效。
                    </p>
                </div>
            </div>
        `;
    }

    /**
     * 加载设置
     */
    loadSettings() {
        if (window.storageService) {
            const savedSettings = storageService.getSettings();
            if (savedSettings) {
                this.currentSettings = { ...this.currentSettings, ...savedSettings };
            }
        }
        
        this.applySettingsToUI();
    }

    /**
     * 应用设置到UI
     */
    applySettingsToUI() {
        // 音频设置
        this.setSliderValue('masterVolume', this.currentSettings.audio.masterVolume * 100);
        this.setSliderValue('musicVolume', this.currentSettings.audio.musicVolume * 100);
        this.setSliderValue('sfxVolume', this.currentSettings.audio.sfxVolume * 100);
        this.setSliderValue('uiVolume', this.currentSettings.audio.uiVolume * 100);
        this.setCheckboxValue('enableAudio', this.currentSettings.audio.enableAudio);
        this.setSelectValue('audioTheme', this.currentSettings.audio.audioTheme);
        
        // 图形设置
        this.setSelectValue('quality', this.currentSettings.graphics.quality);
        this.setSelectValue('renderMode', this.currentSettings.graphics.renderMode);
        this.setSelectValue('particleCount', this.currentSettings.graphics.particleCount);
        this.setSelectValue('frameRate', this.currentSettings.graphics.frameRate);
        this.setCheckboxValue('enableEffects', this.currentSettings.graphics.enableEffects);
        this.setCheckboxValue('enableGlow', this.currentSettings.graphics.enableGlow);
        
        // 控制设置
        this.setSliderValue('mouseSensitivity', this.currentSettings.controls.mousesensitivity);
        this.setSelectValue('keyboardLayout', this.currentSettings.controls.keyboardLayout);
        this.setCheckboxValue('enableTouch', this.currentSettings.controls.enableTouch);
        this.setCheckboxValue('enableMicrophone', this.currentSettings.controls.enableMicrophone);
        this.setSliderValue('microphoneSensitivity', this.currentSettings.controls.microphoneSensitivity);
        
        // 游戏设置
        this.setSelectValue('difficulty', this.currentSettings.gameplay.difficulty);
        this.setCheckboxValue('autoSave', this.currentSettings.gameplay.autoSave);
        this.setCheckboxValue('showTutorial', this.currentSettings.gameplay.showTutorial);
        this.setCheckboxValue('enableHints', this.currentSettings.gameplay.enableHints);
        this.setCheckboxValue('pauseOnFocusLoss', this.currentSettings.gameplay.pauseOnFocusLoss);
        
        // 辅助功能设置
        this.setSelectValue('colorBlindMode', this.currentSettings.accessibility.colorBlindMode);
        this.setCheckboxValue('highContrast', this.currentSettings.accessibility.highContrast);
        this.setCheckboxValue('largeText', this.currentSettings.accessibility.largeText);
        this.setCheckboxValue('reduceMotion', this.currentSettings.accessibility.reduceMotion);
        this.setCheckboxValue('screenReader', this.currentSettings.accessibility.screenReader);
        
        // 语言设置
        this.setSelectValue('language', this.currentSettings.language);
    }

    /**
     * 设置滑块值
     * @param {string} id - 元素ID
     * @param {number} value - 值
     */
    setSliderValue(id, value) {
        const slider = document.getElementById(id);
        const valueDisplay = slider?.parentNode.querySelector('.slider-value');
        
        if (slider) {
            slider.value = value;
            if (valueDisplay) {
                if (id.includes('Volume')) {
                    valueDisplay.textContent = `${Math.round(value)}%`;
                } else {
                    valueDisplay.textContent = value.toString();
                }
            }
        }
    }

    /**
     * 设置复选框值
     * @param {string} id - 元素ID
     * @param {boolean} value - 值
     */
    setCheckboxValue(id, value) {
        const checkbox = document.getElementById(id);
        if (checkbox) {
            checkbox.checked = value;
        }
    }

    /**
     * 设置选择框值
     * @param {string} id - 元素ID
     * @param {string} value - 值
     */
    setSelectValue(id, value) {
        const select = document.getElementById(id);
        if (select) {
            select.value = value;
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        console.log('🔧 设置事件监听器...');

        // 滑块事件 - 使用HTML中的实际类名
        const sliders = this.elements.container.querySelectorAll('.setting-slider');
        console.log(`📊 找到 ${sliders.length} 个滑块`);
        sliders.forEach(slider => {
            slider.addEventListener('input', (e) => this.onSliderChange(e));
            console.log(`✅ 已绑定滑块事件: ${slider.id}`);
        });

        // 复选框事件
        const checkboxes = this.elements.container.querySelectorAll('.setting-checkbox');
        console.log(`☑️ 找到 ${checkboxes.length} 个复选框`);
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (e) => this.onCheckboxChange(e));
            console.log(`✅ 已绑定复选框事件: ${checkbox.id}`);
        });

        // 选择框事件
        const selects = this.elements.container.querySelectorAll('.setting-select');
        console.log(`📋 找到 ${selects.length} 个选择框`);
        selects.forEach(select => {
            select.addEventListener('change', (e) => this.onSelectChange(e));
            console.log(`✅ 已绑定选择框事件: ${select.id}`);
        });

        // 按钮事件
        if (this.elements.saveButton) {
            this.elements.saveButton.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('💾 保存设置按钮被点击');
                this.saveSettings();
            });
            console.log('✅ 已绑定保存按钮事件');
        } else {
            console.warn('⚠️ 保存按钮未找到');
        }

        if (this.elements.resetButton) {
            this.elements.resetButton.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('🔄 重置设置按钮被点击');
                this.resetSettings();
            });
            console.log('✅ 已绑定重置按钮事件');
        } else {
            console.warn('⚠️ 重置按钮未找到');
        }

        if (this.elements.cancelButton) {
            this.elements.cancelButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.cancelSettings();
            });
        }
    }

    /**
     * 初始化标签页
     */
    initTabs() {
        // 显示第一个标签页
        this.switchTab('audio');
    }

    /**
     * 切换标签页
     * @param {string} tabName - 标签页名称
     */
    switchTab(tabName) {
        // 更新标签页状态
        this.elements.tabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });
        
        // 更新面板显示
        this.elements.panels.forEach(panel => {
            panel.classList.toggle('active', panel.dataset.panel === tabName);
        });
    }

    /**
     * 滑块变化事件
     * @param {Event} event - 事件对象
     */
    onSliderChange(event) {
        const slider = event.target;
        console.log(`🎚️ 滑块变化: ${slider.id} = ${slider.value}`);

        // 更新显示值 - 使用HTML中的实际结构
        const valueDisplay = document.getElementById(slider.id + '-value');

        if (valueDisplay) {
            if (slider.id.includes('volume')) {
                valueDisplay.textContent = `${Math.round(slider.value)}%`;
            } else if (slider.id === 'sensitivity') {
                valueDisplay.textContent = `${parseFloat(slider.value).toFixed(1)}x`;
            } else {
                valueDisplay.textContent = slider.value;
            }
            console.log(`📊 更新显示值: ${valueDisplay.textContent}`);
        }

        // 实时预览音量变化
        if (slider.id.includes('volume') && window.audioEngine) {
            const volume = slider.value / 100;
            console.log(`🔊 设置音量: ${slider.id} = ${volume}`);

            // 根据滑块类型设置对应的音量
            if (slider.id === 'master-volume') {
                audioEngine.setMasterVolume(volume);
            } else if (slider.id === 'music-volume') {
                audioEngine.setMusicVolume(volume);
            } else if (slider.id === 'sfx-volume') {
                audioEngine.setSfxVolume(volume);
            }
        }

        // 更新临时设置
        this.updateTempSetting(slider.id, slider.value);
    }

    /**
     * 更新临时设置
     * @param {string} settingId - 设置ID
     * @param {*} value - 设置值
     */
    updateTempSetting(settingId, value) {
        if (!this.tempSettings) {
            this.tempSettings = JSON.parse(JSON.stringify(this.currentSettings));
        }

        // 根据设置ID更新对应的临时设置
        if (settingId.includes('volume')) {
            const volumeType = settingId.replace('-volume', '');
            if (volumeType === 'master') {
                this.tempSettings.audio.masterVolume = value / 100;
            } else if (volumeType === 'music') {
                this.tempSettings.audio.musicVolume = value / 100;
            } else if (volumeType === 'sfx') {
                this.tempSettings.audio.sfxVolume = value / 100;
            }
        } else if (settingId === 'sensitivity') {
            this.tempSettings.controls.mousesensitivity = parseFloat(value);
        } else if (settingId === 'particle-quality') {
            this.tempSettings.graphics.quality = value;
        } else if (settingId === 'visual-effects') {
            this.tempSettings.graphics.enableEffects = value;
        }

        console.log(`📝 临时设置已更新: ${settingId} = ${value}`);
    }

    /**
     * 复选框变化事件
     * @param {Event} event - 事件对象
     */
    onCheckboxChange(event) {
        const checkbox = event.target;
        console.log(`☑️ 复选框变化: ${checkbox.id} = ${checkbox.checked}`);

        // 更新临时设置
        this.updateTempSetting(checkbox.id, checkbox.checked);

        // 实时预览某些设置
        if (checkbox.id === 'enableAudio' && window.audioManager) {
            audioManager.setEnabled(checkbox.checked);
        }
    }

    /**
     * 选择框变化事件
     * @param {Event} event - 事件对象
     */
    onSelectChange(event) {
        const select = event.target;
        console.log(`📋 选择框变化: ${select.id} = ${select.value}`);

        // 更新临时设置
        this.updateTempSetting(select.id, select.value);

        // 实时预览某些设置
        if (select.id === 'particle-quality' && window.renderEngine) {
            renderEngine.setParticleQuality(select.value);
        }
    }

    /**
     * 保存设置
     */
    saveSettings() {
        console.log('💾 开始保存设置...');

        // 使用临时设置或收集当前UI中的设置
        if (this.tempSettings) {
            this.currentSettings = JSON.parse(JSON.stringify(this.tempSettings));
        } else {
            this.collectSettingsFromUI();
        }

        // 保存到存储
        if (window.storageService) {
            storageService.saveSettings(this.currentSettings);
            console.log('✅ 设置已保存到存储');
        } else {
            console.warn('⚠️ 存储服务不可用，设置未保存');
        }

        // 应用设置到游戏系统
        this.applySettingsToGame();

        // 显示保存成功消息
        console.log('✅ 设置保存完成');
        alert('设置已保存！');

        // 清除临时设置
        this.tempSettings = null;
    }

    /**
     * 从UI收集设置
     */
    collectSettingsFromUI() {
        console.log('📊 从UI收集设置...');

        // 音频设置 - 使用HTML中的实际ID
        const masterVolume = document.getElementById('master-volume');
        const musicVolume = document.getElementById('music-volume');
        const sfxVolume = document.getElementById('sfx-volume');

        if (masterVolume) this.currentSettings.audio.masterVolume = masterVolume.value / 100;
        if (musicVolume) this.currentSettings.audio.musicVolume = musicVolume.value / 100;
        if (sfxVolume) this.currentSettings.audio.sfxVolume = sfxVolume.value / 100;

        // 图形设置
        const particleQuality = document.getElementById('particle-quality');
        const visualEffects = document.getElementById('visual-effects');

        if (particleQuality) this.currentSettings.graphics.quality = particleQuality.value;
        if (visualEffects) this.currentSettings.graphics.enableEffects = visualEffects.checked;

        // 控制设置
        const sensitivity = document.getElementById('sensitivity');
        if (sensitivity) this.currentSettings.controls.mousesensitivity = parseFloat(sensitivity.value);

        console.log('📊 设置收集完成:', this.currentSettings);
        this.currentSettings.controls.enableTouch = this.getCheckboxValue('enableTouch');
        this.currentSettings.controls.enableMicrophone = this.getCheckboxValue('enableMicrophone');
        this.currentSettings.controls.microphoneSensitivity = parseFloat(this.getSliderValue('microphoneSensitivity'));
        
        // 游戏设置
        this.currentSettings.gameplay.difficulty = this.getSelectValue('difficulty');
        this.currentSettings.gameplay.autoSave = this.getCheckboxValue('autoSave');
        this.currentSettings.gameplay.showTutorial = this.getCheckboxValue('showTutorial');
        this.currentSettings.gameplay.enableHints = this.getCheckboxValue('enableHints');
        this.currentSettings.gameplay.pauseOnFocusLoss = this.getCheckboxValue('pauseOnFocusLoss');
        
        // 辅助功能设置
        this.currentSettings.accessibility.colorBlindMode = this.getSelectValue('colorBlindMode');
        this.currentSettings.accessibility.highContrast = this.getCheckboxValue('highContrast');
        this.currentSettings.accessibility.largeText = this.getCheckboxValue('largeText');
        this.currentSettings.accessibility.reduceMotion = this.getCheckboxValue('reduceMotion');
        this.currentSettings.accessibility.screenReader = this.getCheckboxValue('screenReader');
        
        // 语言设置
        this.currentSettings.language = this.getSelectValue('language');
    }

    /**
     * 获取滑块值
     * @param {string} id - 元素ID
     * @returns {number} 值
     */
    getSliderValue(id) {
        const slider = document.getElementById(id);
        return slider ? parseFloat(slider.value) : 0;
    }

    /**
     * 获取复选框值
     * @param {string} id - 元素ID
     * @returns {boolean} 值
     */
    getCheckboxValue(id) {
        const checkbox = document.getElementById(id);
        return checkbox ? checkbox.checked : false;
    }

    /**
     * 获取选择框值
     * @param {string} id - 元素ID
     * @returns {string} 值
     */
    getSelectValue(id) {
        const select = document.getElementById(id);
        return select ? select.value : '';
    }

    /**
     * 应用设置到游戏系统
     */
    applySettingsToGame() {
        // 应用音频设置
        if (window.audioManager) {
            audioManager.applySettings(this.currentSettings.audio);
        }
        
        // 应用图形设置
        if (window.renderEngine) {
            renderEngine.applySettings(this.currentSettings.graphics);
        }
        
        // 应用控制设置
        if (window.inputManager) {
            inputManager.applySettings(this.currentSettings.controls);
        }
        
        // 应用语言设置
        if (window.i18n && this.currentSettings.language !== i18n.getCurrentLanguage()) {
            i18n.setLanguage(this.currentSettings.language);
        }
        
        // 应用辅助功能设置
        this.applyAccessibilitySettings();
    }

    /**
     * 应用辅助功能设置
     */
    applyAccessibilitySettings() {
        const root = document.documentElement;
        
        // 高对比度
        root.classList.toggle('high-contrast', this.currentSettings.accessibility.highContrast);
        
        // 大字体
        root.classList.toggle('large-text', this.currentSettings.accessibility.largeText);
        
        // 减少动画
        root.classList.toggle('reduce-motion', this.currentSettings.accessibility.reduceMotion);
        
        // 色盲模式
        root.className = root.className.replace(/colorblind-\w+/g, '');
        if (this.currentSettings.accessibility.colorBlindMode !== 'none') {
            root.classList.add(`colorblind-${this.currentSettings.accessibility.colorBlindMode}`);
        }
    }

    /**
     * 重置设置
     */
    resetSettings() {
        console.log('🔄 重置设置...');

        if (confirm('确定要重置所有设置为默认值吗？此操作无法撤销。')) {
            this.doResetSettings();
        }
    }

    /**
     * 执行重置设置
     */
    doResetSettings() {
        console.log('🔄 执行重置设置...');

        // 重置UI元素到默认值
        const masterVolume = document.getElementById('master-volume');
        const musicVolume = document.getElementById('music-volume');
        const sfxVolume = document.getElementById('sfx-volume');
        const sensitivity = document.getElementById('sensitivity');
        const particleQuality = document.getElementById('particle-quality');
        const visualEffects = document.getElementById('visual-effects');

        if (masterVolume) {
            masterVolume.value = 80;
            document.getElementById('master-volume-value').textContent = '80%';
        }
        if (musicVolume) {
            musicVolume.value = 70;
            document.getElementById('music-volume-value').textContent = '70%';
        }
        if (sfxVolume) {
            sfxVolume.value = 90;
            document.getElementById('sfx-volume-value').textContent = '90%';
        }
        if (sensitivity) {
            sensitivity.value = 1.0;
            document.getElementById('sensitivity-value').textContent = '1.0x';
        }
        if (particleQuality) {
            particleQuality.value = 'medium';
        }
        if (visualEffects) {
            visualEffects.checked = true;
        }

        // 重置内部设置
        this.currentSettings = {
            audio: {
                masterVolume: 0.8,
                musicVolume: 0.7,
                sfxVolume: 0.9
            },
            graphics: {
                quality: 'medium',
                enableEffects: true
            },
            controls: {
                mousesensitivity: 1.0
            }
        };

        // 清除临时设置
        this.tempSettings = null;

        console.log('✅ 设置重置完成');
        alert('设置已重置为默认值！');
    }

    /**
     * 取消设置
     */
    cancelSettings() {
        // 恢复临时设置
        if (this.tempSettings) {
            this.currentSettings = { ...this.tempSettings };
            this.applySettingsToUI();
            this.applySettingsToGame();
        }
        
        this.hide();
    }

    /**
     * 显示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型
     */
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageElement = document.createElement('div');
        messageElement.className = `settings-message ${type}`;
        messageElement.textContent = message;
        
        // 添加到设置面板
        this.elements.container.appendChild(messageElement);
        
        // 自动移除
        setTimeout(() => {
            if (messageElement.parentNode) {
                messageElement.parentNode.removeChild(messageElement);
            }
        }, 3000);
    }

    /**
     * 显示设置面板
     */
    show() {
        if (!this.isInitialized) {
            this.init();
        }
        
        // 保存当前设置作为临时设置
        this.tempSettings = { ...this.currentSettings };
        
        // 显示面板
        if (this.elements.container) {
            this.elements.container.style.display = 'flex';
            this.elements.container.classList.add('settings-enter');
            
            setTimeout(() => {
                this.elements.container.classList.remove('settings-enter');
            }, 300);
        }
        
        this.isVisible = true;
    }

    /**
     * 隐藏设置面板
     */
    hide() {
        if (this.elements.container) {
            this.elements.container.classList.add('settings-exit');
            
            setTimeout(() => {
                this.elements.container.style.display = 'none';
                this.elements.container.classList.remove('settings-exit');
            }, 300);
        }
        
        this.isVisible = false;
        
        // 返回上一个屏幕
        if (window.uiManager) {
            uiManager.showScreen('mainMenuScreen');
        }
    }

    /**
     * 销毁设置面板
     */
    destroy() {
        // 清理元素引用
        Object.keys(this.elements).forEach(key => {
            this.elements[key] = null;
        });
        
        this.isInitialized = false;
        console.log('⚙️ 设置面板已销毁');
    }
}

// 创建全局设置面板实例
window.settingsPanel = new SettingsPanel();
