/**
 * 量子共鸣者 - UI动画效果组件
 * 负责管理所有UI动画效果，包括过渡、粒子效果、量子主题动画
 */

class UIAnimations {
    constructor() {
        this.isInitialized = false;
        this.animationFrameId = null;
        
        // 动画配置
        this.config = {
            transitionDuration: 500,
            easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
            easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
            easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
            bounceOut: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
        };
        
        // 活跃动画列表
        this.activeAnimations = new Map();
        
        // 粒子效果系统
        this.particleSystem = {
            particles: [],
            maxParticles: 50,
            canvas: null,
            ctx: null
        };
        
        // 量子效果配置
        this.quantumEffects = {
            glowIntensity: 1.0,
            pulseSpeed: 0.02,
            waveAmplitude: 0.5,
            colorShift: 0
        };
        
        console.log('✨ UI动画组件已创建');
    }

    /**
     * 初始化动画系统
     */
    init() {
        try {
            // 创建动画样式
            this.createAnimationStyles();
            
            // 初始化粒子系统
            this.initParticleSystem();
            
            // 设置量子效果
            this.setupQuantumEffects();
            
            // 开始动画循环
            this.startAnimationLoop();
            
            this.isInitialized = true;
            console.log('✅ UI动画系统初始化完成');
            return true;
        } catch (error) {
            console.error('❌ UI动画系统初始化失败:', error);
            return false;
        }
    }

    /**
     * 创建动画样式
     */
    createAnimationStyles() {
        const style = document.createElement('style');
        style.textContent = `
            /* 屏幕过渡动画 */
            .screen-enter {
                animation: screenEnter ${this.config.transitionDuration}ms ${this.config.easeOut} forwards;
            }
            
            .screen-exit {
                animation: screenExit ${this.config.transitionDuration}ms ${this.config.easeIn} forwards;
            }
            
            @keyframes screenEnter {
                from {
                    opacity: 0;
                    transform: translateY(20px) scale(0.95);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
            }
            
            @keyframes screenExit {
                from {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
                to {
                    opacity: 0;
                    transform: translateY(-20px) scale(0.95);
                }
            }
            
            /* 按钮动画 */
            .quantum-button {
                position: relative;
                overflow: hidden;
                transition: all 0.3s ${this.config.easeOut};
            }
            
            .quantum-button:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0, 255, 255, 0.3);
            }
            
            .quantum-glow {
                box-shadow: 0 0 20px rgba(0, 255, 255, 0.6),
                           0 0 40px rgba(0, 255, 255, 0.4),
                           0 0 60px rgba(0, 255, 255, 0.2);
                animation: quantumPulse 2s ease-in-out infinite;
            }
            
            @keyframes quantumPulse {
                0%, 100% { 
                    box-shadow: 0 0 20px rgba(0, 255, 255, 0.6),
                               0 0 40px rgba(0, 255, 255, 0.4),
                               0 0 60px rgba(0, 255, 255, 0.2);
                }
                50% { 
                    box-shadow: 0 0 30px rgba(0, 255, 255, 0.8),
                               0 0 60px rgba(0, 255, 255, 0.6),
                               0 0 90px rgba(0, 255, 255, 0.4);
                }
            }
            
            /* 涟漪效果 */
            .quantum-ripple {
                position: absolute;
                border-radius: 50%;
                background: radial-gradient(circle, rgba(0, 255, 255, 0.6) 0%, transparent 70%);
                transform: scale(0);
                animation: rippleEffect 0.6s ease-out;
                pointer-events: none;
            }
            
            @keyframes rippleEffect {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
            
            /* HUD动画 */
            .hud-enter {
                animation: hudEnter 0.5s ${this.config.easeOut} forwards;
            }
            
            .hud-exit {
                animation: hudExit 0.3s ${this.config.easeIn} forwards;
            }
            
            @keyframes hudEnter {
                from {
                    opacity: 0;
                    transform: translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            @keyframes hudExit {
                from {
                    opacity: 1;
                    transform: translateY(0);
                }
                to {
                    opacity: 0;
                    transform: translateY(-20px);
                }
            }
            
            /* 分数增加动画 */
            .score-increase {
                animation: scoreIncrease 0.5s ${this.config.bounceOut};
            }
            
            @keyframes scoreIncrease {
                0% { transform: scale(1); }
                50% { transform: scale(1.2); color: #00ff00; }
                100% { transform: scale(1); }
            }
            
            /* 连击闪烁动画 */
            .combo-flash {
                animation: comboFlash 0.3s ease-in-out;
            }
            
            @keyframes comboFlash {
                0%, 100% { 
                    color: #ffffff;
                    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
                }
                50% { 
                    color: #ffff00;
                    text-shadow: 0 0 20px rgba(255, 255, 0, 0.8);
                    transform: scale(1.1);
                }
            }
            
            /* 模态框动画 */
            .modal-enter {
                animation: modalEnter 0.3s ${this.config.easeOut};
            }
            
            .modal-exit {
                animation: modalExit 0.3s ${this.config.easeIn};
            }
            
            @keyframes modalEnter {
                from {
                    opacity: 0;
                    transform: scale(0.8);
                }
                to {
                    opacity: 1;
                    transform: scale(1);
                }
            }
            
            @keyframes modalExit {
                from {
                    opacity: 1;
                    transform: scale(1);
                }
                to {
                    opacity: 0;
                    transform: scale(0.8);
                }
            }
            
            /* 菜单按钮进入动画 */
            .button-enter {
                animation: buttonEnter 0.6s ${this.config.bounceOut} forwards;
            }
            
            @keyframes buttonEnter {
                from {
                    opacity: 0;
                    transform: translateX(-50px) rotate(-10deg);
                }
                to {
                    opacity: 1;
                    transform: translateX(0) rotate(0deg);
                }
            }
            
            /* 设置项进入动画 */
            .settings-item-enter {
                animation: settingsItemEnter 0.4s ${this.config.easeOut} forwards;
            }
            
            @keyframes settingsItemEnter {
                from {
                    opacity: 0;
                    transform: translateX(30px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
            
            /* 玩家卡片进入动画 */
            .card-enter {
                animation: cardEnter 0.5s ${this.config.bounceOut} forwards;
            }
            
            @keyframes cardEnter {
                from {
                    opacity: 0;
                    transform: translateY(50px) rotateX(45deg);
                }
                to {
                    opacity: 1;
                    transform: translateY(0) rotateX(0deg);
                }
            }
            
            /* 量子背景动画 */
            .menu-background-animated {
                position: relative;
                overflow: hidden;
            }
            
            .menu-background-animated::before {
                content: '';
                position: absolute;
                top: -50%;
                left: -50%;
                width: 200%;
                height: 200%;
                background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, transparent 50%);
                animation: quantumRotate 20s linear infinite;
                pointer-events: none;
            }
            
            @keyframes quantumRotate {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        
        document.head.appendChild(style);
    }

    /**
     * 初始化粒子系统
     */
    initParticleSystem() {
        // 创建粒子画布
        this.particleSystem.canvas = document.createElement('canvas');
        this.particleSystem.canvas.id = 'uiParticleCanvas';
        this.particleSystem.canvas.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1000;
        `;
        
        document.body.appendChild(this.particleSystem.canvas);
        this.particleSystem.ctx = this.particleSystem.canvas.getContext('2d');
        
        // 设置画布大小
        this.resizeParticleCanvas();
        
        // 监听窗口大小变化
        window.addEventListener('resize', () => this.resizeParticleCanvas());
    }

    /**
     * 调整粒子画布大小
     */
    resizeParticleCanvas() {
        const canvas = this.particleSystem.canvas;
        const ctx = this.particleSystem.ctx;
        
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        
        // 重新设置绘制属性
        ctx.globalCompositeOperation = 'screen';
    }

    /**
     * 设置量子效果
     */
    setupQuantumEffects() {
        // 创建量子效果的CSS变量
        const root = document.documentElement;
        
        // 设置初始值
        root.style.setProperty('--quantum-glow-intensity', this.quantumEffects.glowIntensity);
        root.style.setProperty('--quantum-pulse-speed', `${2 / this.quantumEffects.pulseSpeed}s`);
        root.style.setProperty('--quantum-wave-amplitude', this.quantumEffects.waveAmplitude);
    }

    /**
     * 开始动画循环
     */
    startAnimationLoop() {
        const animate = (timestamp) => {
            this.updateQuantumEffects(timestamp);
            this.updateParticles(timestamp);
            this.renderParticles();
            
            this.animationFrameId = requestAnimationFrame(animate);
        };
        
        this.animationFrameId = requestAnimationFrame(animate);
    }

    /**
     * 更新量子效果
     * @param {number} timestamp - 时间戳
     */
    updateQuantumEffects(timestamp) {
        // 更新颜色偏移
        this.quantumEffects.colorShift = (timestamp * 0.001) % (Math.PI * 2);
        
        // 计算动态发光强度
        const pulseIntensity = 0.7 + 0.3 * Math.sin(timestamp * this.quantumEffects.pulseSpeed);
        this.quantumEffects.glowIntensity = pulseIntensity;
        
        // 更新CSS变量
        const root = document.documentElement;
        root.style.setProperty('--quantum-glow-intensity', pulseIntensity);
    }

    /**
     * 创建粒子
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {Object} options - 选项
     */
    createParticle(x, y, options = {}) {
        if (this.particleSystem.particles.length >= this.particleSystem.maxParticles) {
            // 移除最老的粒子
            this.particleSystem.particles.shift();
        }
        
        const particle = {
            x: x,
            y: y,
            vx: (Math.random() - 0.5) * 4,
            vy: (Math.random() - 0.5) * 4,
            life: 1.0,
            decay: 0.01 + Math.random() * 0.02,
            size: 2 + Math.random() * 4,
            color: options.color || `hsl(${180 + Math.random() * 60}, 100%, 70%)`,
            glow: options.glow !== false
        };
        
        this.particleSystem.particles.push(particle);
    }

    /**
     * 更新粒子
     * @param {number} timestamp - 时间戳
     */
    updateParticles(timestamp) {
        for (let i = this.particleSystem.particles.length - 1; i >= 0; i--) {
            const particle = this.particleSystem.particles[i];
            
            // 更新位置
            particle.x += particle.vx;
            particle.y += particle.vy;
            
            // 更新生命值
            particle.life -= particle.decay;
            
            // 添加重力效果
            particle.vy += 0.1;
            
            // 添加阻力
            particle.vx *= 0.99;
            particle.vy *= 0.99;
            
            // 移除死亡的粒子
            if (particle.life <= 0) {
                this.particleSystem.particles.splice(i, 1);
            }
        }
    }

    /**
     * 渲染粒子
     */
    renderParticles() {
        const ctx = this.particleSystem.ctx;
        const canvas = this.particleSystem.canvas;
        
        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 绘制粒子
        this.particleSystem.particles.forEach(particle => {
            ctx.save();
            
            // 设置透明度
            ctx.globalAlpha = particle.life;
            
            // 绘制发光效果
            if (particle.glow) {
                ctx.shadowColor = particle.color;
                ctx.shadowBlur = particle.size * 3;
            }
            
            // 绘制粒子
            ctx.fillStyle = particle.color;
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.restore();
        });
    }

    /**
     * 创建按钮点击效果
     * @param {HTMLElement} button - 按钮元素
     * @param {Event} event - 点击事件
     */
    createButtonClickEffect(button, event) {
        const rect = button.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        // 创建多个粒子
        for (let i = 0; i < 8; i++) {
            this.createParticle(centerX, centerY, {
                color: `hsl(${180 + Math.random() * 60}, 100%, 70%)`,
                glow: true
            });
        }
    }

    /**
     * 创建分数增加效果
     * @param {HTMLElement} element - 分数元素
     * @param {number} points - 增加的分数
     */
    createScoreEffect(element, points) {
        const rect = element.getBoundingClientRect();
        const x = rect.left + rect.width / 2;
        const y = rect.top;
        
        // 创建分数粒子
        for (let i = 0; i < 5; i++) {
            this.createParticle(x + (Math.random() - 0.5) * 50, y, {
                color: '#00ff00',
                glow: true
            });
        }
    }

    /**
     * 创建连击效果
     * @param {HTMLElement} element - 连击元素
     * @param {number} combo - 连击数
     */
    createComboEffect(element, combo) {
        const rect = element.getBoundingClientRect();
        const x = rect.left + rect.width / 2;
        const y = rect.top + rect.height / 2;
        
        // 根据连击数创建不同数量的粒子
        const particleCount = Math.min(combo, 20);
        
        for (let i = 0; i < particleCount; i++) {
            this.createParticle(x, y, {
                color: `hsl(${60 - combo * 2}, 100%, 70%)`,
                glow: true
            });
        }
    }

    /**
     * 停止动画循环
     */
    stopAnimationLoop() {
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
    }

    /**
     * 销毁动画系统
     */
    destroy() {
        // 停止动画循环
        this.stopAnimationLoop();
        
        // 移除粒子画布
        if (this.particleSystem.canvas && this.particleSystem.canvas.parentNode) {
            this.particleSystem.canvas.parentNode.removeChild(this.particleSystem.canvas);
        }
        
        // 清理粒子
        this.particleSystem.particles = [];
        
        // 清理活跃动画
        this.activeAnimations.clear();
        
        this.isInitialized = false;
        console.log('✨ UI动画系统已销毁');
    }
}

// 创建全局UI动画实例
window.uiAnimations = new UIAnimations();
