/**
 * 量子共鸣者 - 排行榜UI
 * 负责排行榜显示、排名查询、数据统计等界面功能
 */

class LeaderboardUI {
    constructor() {
        this.isInitialized = false;
        this.isVisible = false;
        
        // 元素引用
        this.elements = {
            container: null,
            leaderboardList: null,
            categoryTabs: null,
            playerRank: null
        };
        
        // 排行榜类别
        this.categories = {
            'highScores': {
                name: '最高分数',
                icon: '🏆',
                valueKey: 'score',
                format: (value) => value.toLocaleString()
            },
            'fastestTimes': {
                name: '最快时间',
                icon: '⚡',
                valueKey: 'time',
                format: (value) => this.formatTime(value)
            },
            'longestCombos': {
                name: '最长连击',
                icon: '🔥',
                valueKey: 'combo',
                format: (value) => `${value} 连击`
            },
            'mostChainReactions': {
                name: '最多连锁',
                icon: '🔗',
                valueKey: 'chainReactions',
                format: (value) => `${value} 次`
            }
        };
        
        // 当前选中的类别
        this.currentCategory = 'highScores';
        
        console.log('🏆 排行榜UI已创建');
    }

    /**
     * 初始化排行榜UI
     */
    init() {
        try {
            // 获取容器元素
            this.getElements();
            
            // 创建排行榜界面结构
            this.createLeaderboardStructure();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('✅ 排行榜UI初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 排行榜UI初始化失败:', error);
            return false;
        }
    }

    /**
     * 获取元素引用
     */
    getElements() {
        this.elements.container = document.getElementById('leaderboardScreen') || 
                                 document.querySelector('.leaderboard-screen');
    }

    /**
     * 创建排行榜界面结构
     */
    createLeaderboardStructure() {
        if (!this.elements.container) {
            console.warn('⚠️ 排行榜界面容器不存在，跳过结构创建');
            return;
        }

        this.elements.container.innerHTML = `
            <div class="leaderboard-overlay">
                <div class="leaderboard-content">
                    <div class="leaderboard-header">
                        <h1 class="leaderboard-title">排行榜</h1>
                        <button class="close-btn" id="closeLeaderboardBtn">✕</button>
                    </div>
                    
                    <div class="leaderboard-tabs">
                        ${Object.entries(this.categories).map(([key, category]) => `
                            <button class="tab-btn ${key === this.currentCategory ? 'active' : ''}" 
                                    data-category="${key}">
                                <span class="tab-icon">${category.icon}</span>
                                <span class="tab-text">${category.name}</span>
                            </button>
                        `).join('')}
                    </div>
                    
                    <div class="player-rank-info" id="playerRankInfo">
                        <!-- 玩家排名信息将在这里显示 -->
                    </div>
                    
                    <div class="leaderboard-main">
                        <div class="leaderboard-list" id="leaderboardList">
                            <!-- 排行榜列表将在这里动态生成 -->
                        </div>
                    </div>
                    
                    <div class="leaderboard-footer">
                        <div class="refresh-info">
                            <span class="refresh-text">数据更新时间:</span>
                            <span class="refresh-time" id="refreshTime">--</span>
                        </div>
                        <button class="refresh-btn" id="refreshLeaderboardBtn">
                            <span class="btn-icon">🔄</span>
                            <span class="btn-text">刷新数据</span>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 更新元素引用
        this.elements.leaderboardList = document.getElementById('leaderboardList');
        this.elements.categoryTabs = document.querySelectorAll('.tab-btn');
        this.elements.playerRank = document.getElementById('playerRankInfo');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 关闭按钮
        const closeBtn = document.getElementById('closeLeaderboardBtn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hide();
            });
        }
        
        // 类别标签
        this.elements.categoryTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const category = e.currentTarget.dataset.category;
                this.switchCategory(category);
            });
        });
        
        // 刷新按钮
        const refreshBtn = document.getElementById('refreshLeaderboardBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshLeaderboard();
            });
        }
        
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (this.isVisible && e.key === 'Escape') {
                this.hide();
            }
        });
    }

    /**
     * 显示排行榜界面
     */
    show() {
        if (!this.isInitialized) {
            this.init();
        }
        
        // 显示容器
        if (this.elements.container) {
            this.elements.container.style.display = 'flex';
            this.elements.container.classList.add('screen-enter');
            
            setTimeout(() => {
                this.elements.container.classList.remove('screen-enter');
            }, 500);
        }
        
        // 更新排行榜显示
        this.updateLeaderboardDisplay();
        
        this.isVisible = true;
        console.log('🏆 排行榜界面已显示');
    }

    /**
     * 隐藏排行榜界面
     */
    hide() {
        if (this.elements.container) {
            this.elements.container.classList.add('screen-exit');
            
            setTimeout(() => {
                this.elements.container.style.display = 'none';
                this.elements.container.classList.remove('screen-exit');
            }, 500);
        }
        
        this.isVisible = false;
        console.log('🏆 排行榜界面已隐藏');
    }

    /**
     * 切换排行榜类别
     * @param {string} category - 类别名称
     */
    switchCategory(category) {
        if (!this.categories[category]) {
            console.error('❌ 排行榜类别不存在:', category);
            return;
        }
        
        // 更新当前类别
        this.currentCategory = category;
        
        // 更新标签状态
        this.elements.categoryTabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.category === category);
        });
        
        // 更新显示
        this.updateLeaderboardDisplay();
        
        console.log(`🏆 切换到排行榜类别: ${this.categories[category].name}`);
    }

    /**
     * 更新排行榜显示
     */
    updateLeaderboardDisplay() {
        if (!window.playerManager || !this.elements.leaderboardList) return;
        
        // 获取排行榜数据
        const leaderboardData = playerManager.getLeaderboard(this.currentCategory, 50);
        
        // 更新玩家排名信息
        this.updatePlayerRankInfo();
        
        // 生成排行榜列表
        this.generateLeaderboardList(leaderboardData);
        
        // 更新刷新时间
        this.updateRefreshTime();
    }

    /**
     * 更新玩家排名信息
     */
    updatePlayerRankInfo() {
        if (!this.elements.playerRank || !window.playerManager) return;
        
        const currentPlayer = playerManager.getCurrentPlayer();
        if (!currentPlayer) {
            this.elements.playerRank.innerHTML = '<p class="no-player">请先创建玩家档案</p>';
            return;
        }
        
        const playerRank = playerManager.getPlayerRank(this.currentCategory);
        const category = this.categories[this.currentCategory];
        
        // 获取玩家在该类别的最佳成绩
        const leaderboardData = playerManager.getLeaderboard(this.currentCategory, 1000);
        const playerEntry = leaderboardData.find(entry => entry.playerId === currentPlayer.id);
        
        let rankHTML = '';
        if (playerRank > 0 && playerEntry) {
            rankHTML = `
                <div class="player-rank-card">
                    <div class="rank-info">
                        <span class="rank-position">#${playerRank}</span>
                        <span class="rank-category">${category.name}</span>
                    </div>
                    <div class="rank-details">
                        <span class="player-name">${currentPlayer.name}</span>
                        <span class="player-score">${category.format(playerEntry[category.valueKey])}</span>
                    </div>
                </div>
            `;
        } else {
            rankHTML = `
                <div class="player-rank-card no-rank">
                    <div class="rank-info">
                        <span class="rank-position">未上榜</span>
                        <span class="rank-category">${category.name}</span>
                    </div>
                    <div class="rank-details">
                        <span class="player-name">${currentPlayer.name}</span>
                        <span class="rank-hint">继续游戏以获得排名！</span>
                    </div>
                </div>
            `;
        }
        
        this.elements.playerRank.innerHTML = rankHTML;
    }

    /**
     * 生成排行榜列表
     * @param {Array} leaderboardData - 排行榜数据
     */
    generateLeaderboardList(leaderboardData) {
        if (!this.elements.leaderboardList) return;
        
        if (leaderboardData.length === 0) {
            this.elements.leaderboardList.innerHTML = `
                <div class="empty-leaderboard">
                    <div class="empty-icon">🏆</div>
                    <div class="empty-text">暂无排行榜数据</div>
                    <div class="empty-hint">开始游戏来创建第一个记录吧！</div>
                </div>
            `;
            return;
        }
        
        const category = this.categories[this.currentCategory];
        const currentPlayer = playerManager.getCurrentPlayer();
        
        const html = leaderboardData.map((entry, index) => {
            const isCurrentPlayer = currentPlayer && entry.playerId === currentPlayer.id;
            const rankClass = this.getRankClass(index + 1);
            
            return `
                <div class="leaderboard-entry ${isCurrentPlayer ? 'current-player' : ''} ${rankClass}">
                    <div class="entry-rank">
                        <span class="rank-number">${index + 1}</span>
                        ${index < 3 ? `<span class="rank-medal">${this.getRankMedal(index + 1)}</span>` : ''}
                    </div>
                    <div class="entry-player">
                        <span class="player-name">${entry.playerName || '匿名玩家'}</span>
                        <span class="entry-date">${new Date(entry.timestamp).toLocaleDateString()}</span>
                    </div>
                    <div class="entry-score">
                        <span class="score-value">${category.format(entry[category.valueKey])}</span>
                        ${entry.levelName ? `<span class="level-name">${entry.levelName}</span>` : ''}
                    </div>
                </div>
            `;
        }).join('');
        
        this.elements.leaderboardList.innerHTML = html;
    }

    /**
     * 获取排名样式类
     * @param {number} rank - 排名
     * @returns {string} 样式类名
     */
    getRankClass(rank) {
        if (rank === 1) return 'rank-first';
        if (rank === 2) return 'rank-second';
        if (rank === 3) return 'rank-third';
        if (rank <= 10) return 'rank-top10';
        return '';
    }

    /**
     * 获取排名奖牌
     * @param {number} rank - 排名
     * @returns {string} 奖牌图标
     */
    getRankMedal(rank) {
        switch (rank) {
            case 1: return '🥇';
            case 2: return '🥈';
            case 3: return '🥉';
            default: return '';
        }
    }

    /**
     * 刷新排行榜
     */
    refreshLeaderboard() {
        // 显示刷新动画
        const refreshBtn = document.getElementById('refreshLeaderboardBtn');
        if (refreshBtn) {
            refreshBtn.classList.add('refreshing');
            refreshBtn.disabled = true;
        }
        
        // 模拟刷新延迟
        setTimeout(() => {
            this.updateLeaderboardDisplay();
            
            if (refreshBtn) {
                refreshBtn.classList.remove('refreshing');
                refreshBtn.disabled = false;
            }
            
            // 显示刷新成功提示
            if (window.uiManager) {
                uiManager.showToast('排行榜数据已刷新');
            }
        }, 1000);
    }

    /**
     * 更新刷新时间
     */
    updateRefreshTime() {
        const refreshTimeEl = document.getElementById('refreshTime');
        if (refreshTimeEl) {
            refreshTimeEl.textContent = new Date().toLocaleTimeString();
        }
    }

    /**
     * 格式化时间
     * @param {number} seconds - 秒数
     * @returns {string} 格式化的时间字符串
     */
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        const milliseconds = Math.floor((seconds % 1) * 1000);
        
        if (minutes > 0) {
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(3, '0')}`;
        } else {
            return `${remainingSeconds}.${milliseconds.toString().padStart(3, '0')}s`;
        }
    }

    /**
     * 获取排行榜统计信息
     * @returns {Object} 统计信息
     */
    getLeaderboardStats() {
        if (!window.playerManager) return null;
        
        const stats = {};
        
        Object.keys(this.categories).forEach(category => {
            const leaderboard = playerManager.getLeaderboard(category, 1000);
            const playerRank = playerManager.getPlayerRank(category);
            
            stats[category] = {
                totalEntries: leaderboard.length,
                playerRank: playerRank,
                topScore: leaderboard.length > 0 ? leaderboard[0] : null
            };
        });
        
        return stats;
    }

    /**
     * 导出排行榜数据
     * @param {string} category - 类别（可选，默认当前类别）
     * @returns {string} CSV格式的数据
     */
    exportLeaderboard(category = this.currentCategory) {
        if (!window.playerManager) return '';
        
        const leaderboard = playerManager.getLeaderboard(category, 1000);
        const categoryInfo = this.categories[category];
        
        if (leaderboard.length === 0) return '';
        
        // CSV头部
        let csv = '排名,玩家名称,成绩,关卡,日期\n';
        
        // 数据行
        leaderboard.forEach((entry, index) => {
            const rank = index + 1;
            const playerName = entry.playerName || '匿名玩家';
            const score = categoryInfo.format(entry[categoryInfo.valueKey]);
            const levelName = entry.levelName || '';
            const date = new Date(entry.timestamp).toLocaleDateString();
            
            csv += `${rank},"${playerName}","${score}","${levelName}","${date}"\n`;
        });
        
        return csv;
    }

    /**
     * 销毁排行榜UI
     */
    destroy() {
        // 清理元素引用
        Object.keys(this.elements).forEach(key => {
            this.elements[key] = null;
        });
        
        this.isInitialized = false;
        
        console.log('🏆 排行榜UI已销毁');
    }
}

// 创建全局排行榜UI实例
window.leaderboardUI = new LeaderboardUI();
