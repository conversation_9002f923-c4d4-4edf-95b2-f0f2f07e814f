/**
 * 量子共鸣者 - 输入处理器
 * 处理键盘、鼠标、触摸等各种输入事件
 */

class InputHandler {
    constructor() {
        // 输入状态
        this.keys = {};
        this.mouse = {
            x: 0,
            y: 0,
            buttons: {},
            wheel: 0
        };
        this.touches = new Map();
        
        // 输入监听器
        this.listeners = {
            keydown: [],
            keyup: [],
            mousedown: [],
            mouseup: [],
            mousemove: [],
            wheel: [],
            touchstart: [],
            touchmove: [],
            touchend: [],
            gamepad: []
        };
        
        // 手柄支持
        this.gamepads = {};
        this.gamepadConnected = false;
        
        // 输入配置
        this.config = {
            preventDefaults: true,
            enableGamepad: true,
            touchSensitivity: 1.0,
            mouseSensitivity: 1.0
        };
        
        // 绑定事件处理器
        this.bindEventHandlers();
        
        this.init();
    }

    /**
     * 初始化输入处理器
     */
    init() {
        // 设置事件监听器
        this.setupEventListeners();
        
        // 初始化手柄支持
        if (this.config.enableGamepad) {
            this.initGamepadSupport();
        }
        
        console.log('🎮 输入处理器初始化完成');
    }

    /**
     * 绑定事件处理器
     */
    bindEventHandlers() {
        this.handleKeyDown = this.handleKeyDown.bind(this);
        this.handleKeyUp = this.handleKeyUp.bind(this);
        this.handleMouseDown = this.handleMouseDown.bind(this);
        this.handleMouseUp = this.handleMouseUp.bind(this);
        this.handleMouseMove = this.handleMouseMove.bind(this);
        this.handleWheel = this.handleWheel.bind(this);
        this.handleTouchStart = this.handleTouchStart.bind(this);
        this.handleTouchMove = this.handleTouchMove.bind(this);
        this.handleTouchEnd = this.handleTouchEnd.bind(this);
        this.handleGamepadConnected = this.handleGamepadConnected.bind(this);
        this.handleGamepadDisconnected = this.handleGamepadDisconnected.bind(this);
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', this.handleKeyDown);
        document.addEventListener('keyup', this.handleKeyUp);
        
        // 鼠标事件
        document.addEventListener('mousedown', this.handleMouseDown);
        document.addEventListener('mouseup', this.handleMouseUp);
        document.addEventListener('mousemove', this.handleMouseMove);
        document.addEventListener('wheel', this.handleWheel);
        
        // 触摸事件
        document.addEventListener('touchstart', this.handleTouchStart, { passive: false });
        document.addEventListener('touchmove', this.handleTouchMove, { passive: false });
        document.addEventListener('touchend', this.handleTouchEnd, { passive: false });
        
        // 手柄事件
        window.addEventListener('gamepadconnected', this.handleGamepadConnected);
        window.addEventListener('gamepaddisconnected', this.handleGamepadDisconnected);
    }

    /**
     * 键盘按下事件
     */
    handleKeyDown(event) {
        const key = event.code || event.key;
        
        if (!this.keys[key]) {
            this.keys[key] = {
                pressed: true,
                justPressed: true,
                timestamp: Date.now()
            };
            
            // 通知监听器
            this.notifyListeners('keydown', {
                key: key,
                code: event.code,
                altKey: event.altKey,
                ctrlKey: event.ctrlKey,
                shiftKey: event.shiftKey,
                metaKey: event.metaKey
            });
        }
        
        if (this.config.preventDefaults) {
            event.preventDefault();
        }
    }

    /**
     * 键盘释放事件
     */
    handleKeyUp(event) {
        const key = event.code || event.key;
        
        if (this.keys[key]) {
            this.keys[key].pressed = false;
            this.keys[key].justReleased = true;
            
            // 通知监听器
            this.notifyListeners('keyup', {
                key: key,
                code: event.code,
                duration: Date.now() - this.keys[key].timestamp
            });
        }
        
        if (this.config.preventDefaults) {
            event.preventDefault();
        }
    }

    /**
     * 鼠标按下事件
     */
    handleMouseDown(event) {
        const button = event.button;
        
        this.mouse.buttons[button] = {
            pressed: true,
            justPressed: true,
            timestamp: Date.now()
        };
        
        // 通知监听器
        this.notifyListeners('mousedown', {
            button: button,
            x: event.clientX,
            y: event.clientY,
            altKey: event.altKey,
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey
        });
        
        if (this.config.preventDefaults) {
            event.preventDefault();
        }
    }

    /**
     * 鼠标释放事件
     */
    handleMouseUp(event) {
        const button = event.button;
        
        if (this.mouse.buttons[button]) {
            this.mouse.buttons[button].pressed = false;
            this.mouse.buttons[button].justReleased = true;
            
            // 通知监听器
            this.notifyListeners('mouseup', {
                button: button,
                x: event.clientX,
                y: event.clientY,
                duration: Date.now() - this.mouse.buttons[button].timestamp
            });
        }
        
        if (this.config.preventDefaults) {
            event.preventDefault();
        }
    }

    /**
     * 鼠标移动事件
     */
    handleMouseMove(event) {
        const deltaX = event.clientX - this.mouse.x;
        const deltaY = event.clientY - this.mouse.y;
        
        this.mouse.x = event.clientX;
        this.mouse.y = event.clientY;
        
        // 通知监听器
        this.notifyListeners('mousemove', {
            x: this.mouse.x,
            y: this.mouse.y,
            deltaX: deltaX * this.config.mouseSensitivity,
            deltaY: deltaY * this.config.mouseSensitivity
        });
    }

    /**
     * 鼠标滚轮事件
     */
    handleWheel(event) {
        this.mouse.wheel = event.deltaY;
        
        // 通知监听器
        this.notifyListeners('wheel', {
            deltaX: event.deltaX,
            deltaY: event.deltaY,
            deltaZ: event.deltaZ
        });
        
        if (this.config.preventDefaults) {
            event.preventDefault();
        }
    }

    /**
     * 触摸开始事件
     */
    handleTouchStart(event) {
        for (let touch of event.changedTouches) {
            this.touches.set(touch.identifier, {
                id: touch.identifier,
                x: touch.clientX,
                y: touch.clientY,
                startX: touch.clientX,
                startY: touch.clientY,
                timestamp: Date.now()
            });
        }
        
        // 通知监听器
        this.notifyListeners('touchstart', {
            touches: Array.from(this.touches.values()),
            changedTouches: Array.from(event.changedTouches)
        });
        
        if (this.config.preventDefaults) {
            event.preventDefault();
        }
    }

    /**
     * 触摸移动事件
     */
    handleTouchMove(event) {
        for (let touch of event.changedTouches) {
            const existingTouch = this.touches.get(touch.identifier);
            if (existingTouch) {
                existingTouch.x = touch.clientX;
                existingTouch.y = touch.clientY;
            }
        }
        
        // 通知监听器
        this.notifyListeners('touchmove', {
            touches: Array.from(this.touches.values()),
            changedTouches: Array.from(event.changedTouches)
        });
        
        if (this.config.preventDefaults) {
            event.preventDefault();
        }
    }

    /**
     * 触摸结束事件
     */
    handleTouchEnd(event) {
        for (let touch of event.changedTouches) {
            this.touches.delete(touch.identifier);
        }
        
        // 通知监听器
        this.notifyListeners('touchend', {
            touches: Array.from(this.touches.values()),
            changedTouches: Array.from(event.changedTouches)
        });
        
        if (this.config.preventDefaults) {
            event.preventDefault();
        }
    }

    /**
     * 手柄连接事件
     */
    handleGamepadConnected(event) {
        this.gamepads[event.gamepad.index] = event.gamepad;
        this.gamepadConnected = true;
        console.log(`🎮 手柄已连接: ${event.gamepad.id}`);
    }

    /**
     * 手柄断开事件
     */
    handleGamepadDisconnected(event) {
        delete this.gamepads[event.gamepad.index];
        this.gamepadConnected = Object.keys(this.gamepads).length > 0;
        console.log(`🎮 手柄已断开: ${event.gamepad.id}`);
    }

    /**
     * 初始化手柄支持
     */
    initGamepadSupport() {
        // 检查现有手柄
        const gamepads = navigator.getGamepads();
        for (let i = 0; i < gamepads.length; i++) {
            if (gamepads[i]) {
                this.gamepads[i] = gamepads[i];
                this.gamepadConnected = true;
            }
        }
    }

    /**
     * 更新手柄状态
     */
    updateGamepads() {
        if (!this.config.enableGamepad || !this.gamepadConnected) return;
        
        const gamepads = navigator.getGamepads();
        for (let i = 0; i < gamepads.length; i++) {
            if (gamepads[i]) {
                this.gamepads[i] = gamepads[i];
                
                // 检查按钮状态变化
                this.checkGamepadButtons(gamepads[i]);
            }
        }
    }

    /**
     * 检查手柄按钮状态
     */
    checkGamepadButtons(gamepad) {
        // 这里可以添加手柄按钮状态检查逻辑
        // 通知监听器手柄状态变化
    }

    /**
     * 添加输入监听器
     */
    addListener(eventType, callback) {
        if (this.listeners[eventType]) {
            this.listeners[eventType].push(callback);
        }
    }

    /**
     * 移除输入监听器
     */
    removeListener(eventType, callback) {
        if (this.listeners[eventType]) {
            const index = this.listeners[eventType].indexOf(callback);
            if (index > -1) {
                this.listeners[eventType].splice(index, 1);
            }
        }
    }

    /**
     * 通知监听器
     */
    notifyListeners(eventType, data) {
        if (this.listeners[eventType]) {
            this.listeners[eventType].forEach(callback => {
                callback(data);
            });
        }
    }

    /**
     * 检查按键是否按下
     */
    isKeyPressed(key) {
        return this.keys[key] && this.keys[key].pressed;
    }

    /**
     * 检查按键是否刚刚按下
     */
    isKeyJustPressed(key) {
        return this.keys[key] && this.keys[key].justPressed;
    }

    /**
     * 检查鼠标按钮是否按下
     */
    isMouseButtonPressed(button) {
        return this.mouse.buttons[button] && this.mouse.buttons[button].pressed;
    }

    /**
     * 获取鼠标位置
     */
    getMousePosition() {
        return { x: this.mouse.x, y: this.mouse.y };
    }

    /**
     * 获取触摸点
     */
    getTouches() {
        return Array.from(this.touches.values());
    }

    /**
     * 更新输入状态
     */
    update() {
        // 清除"刚刚按下"和"刚刚释放"状态
        for (let key in this.keys) {
            this.keys[key].justPressed = false;
            this.keys[key].justReleased = false;
        }
        
        for (let button in this.mouse.buttons) {
            this.mouse.buttons[button].justPressed = false;
            this.mouse.buttons[button].justReleased = false;
        }
        
        // 重置鼠标滚轮
        this.mouse.wheel = 0;
        
        // 更新手柄状态
        this.updateGamepads();
    }

    /**
     * 销毁输入处理器
     */
    destroy() {
        // 移除事件监听器
        document.removeEventListener('keydown', this.handleKeyDown);
        document.removeEventListener('keyup', this.handleKeyUp);
        document.removeEventListener('mousedown', this.handleMouseDown);
        document.removeEventListener('mouseup', this.handleMouseUp);
        document.removeEventListener('mousemove', this.handleMouseMove);
        document.removeEventListener('wheel', this.handleWheel);
        document.removeEventListener('touchstart', this.handleTouchStart);
        document.removeEventListener('touchmove', this.handleTouchMove);
        document.removeEventListener('touchend', this.handleTouchEnd);
        window.removeEventListener('gamepadconnected', this.handleGamepadConnected);
        window.removeEventListener('gamepaddisconnected', this.handleGamepadDisconnected);
        
        // 清理状态
        this.keys = {};
        this.mouse = { x: 0, y: 0, buttons: {}, wheel: 0 };
        this.touches.clear();
        this.gamepads = {};
        this.listeners = {};
        
        console.log('🎮 输入处理器已销毁');
    }
}

// 导出输入处理器类
window.InputHandler = InputHandler;
