/**
 * 量子共鸣者 - 3D场景管理器
 * 管理3D场景的渲染、相机控制、后处理效果
 */

class SceneManager3D {
    constructor() {
        this.webglRenderer = null;
        this.particleSystem = null;
        this.isInitialized = false;
        
        // 场景设置
        this.scene = {
            background: {
                type: 'gradient', // 'solid', 'gradient', 'starfield'
                colors: [
                    [0.04, 0.06, 0.14, 1.0], // 深蓝
                    [0.02, 0.03, 0.08, 1.0]  // 更深的蓝
                ]
            },
            fog: {
                enabled: true,
                color: [0.04, 0.06, 0.14, 1.0],
                near: 5.0,
                far: 20.0
            },
            lighting: {
                ambient: [0.2, 0.2, 0.4, 1.0],
                directional: {
                    direction: [0.5, -1.0, -0.5],
                    color: [1.0, 1.0, 1.0, 1.0],
                    intensity: 0.8
                },
                point: {
                    position: [0, 0, 5],
                    color: [0.0, 1.0, 1.0, 1.0],
                    intensity: 1.0,
                    range: 10.0
                }
            }
        };
        
        // 相机控制
        this.cameraController = {
            autoRotate: true,
            rotationSpeed: 0.2,
            zoomSpeed: 0.1,
            panSpeed: 0.5,
            minDistance: 3.0,
            maxDistance: 15.0,
            currentDistance: 8.0,
            targetDistance: 8.0,
            azimuth: 0,
            elevation: 15,
            targetAzimuth: 0,
            targetElevation: 15,
            smoothing: 0.1
        };
        
        // 后处理效果
        this.postProcessing = {
            bloom: {
                enabled: true,
                threshold: 0.8,
                intensity: 1.2,
                radius: 0.5
            },
            glow: {
                enabled: true,
                intensity: 1.0,
                size: 2.0
            },
            distortion: {
                enabled: false,
                strength: 0.1,
                frequency: 2.0
            }
        };
        
        // 动画参数
        this.animation = {
            time: 0,
            deltaTime: 0,
            lastFrameTime: 0,
            timeScale: 1.0,
            paused: false
        };
        
        // 交互状态
        this.interaction = {
            mouseDown: false,
            lastMouseX: 0,
            lastMouseY: 0,
            touchCount: 0,
            lastTouchDistance: 0
        };
        
        // 性能监控
        this.performance = {
            frameCount: 0,
            fps: 60,
            frameTime: 16.67,
            lastFpsUpdate: 0
        };
        
        console.log('🌌 3D场景管理器已创建');
    }

    /**
     * 初始化3D场景管理器
     * @param {HTMLCanvasElement} canvas - Canvas元素
     */
    async init(canvas) {
        try {
            // 初始化WebGL渲染器
            this.webglRenderer = new WebGLRenderer();
            const success = await this.webglRenderer.init(canvas);
            
            if (!success) {
                throw new Error('WebGL渲染器初始化失败');
            }
            
            // 初始化粒子系统
            this.particleSystem = new ParticleSystem3D(this.webglRenderer);
            this.particleSystem.init();
            
            // 设置事件监听器
            this.setupEventListeners(canvas);
            
            // 初始化相机
            this.updateCamera();
            
            this.isInitialized = true;
            console.log('✅ 3D场景管理器初始化完成');
            
            return true;
        } catch (error) {
            console.error('❌ 3D场景管理器初始化失败:', error);
            return false;
        }
    }

    /**
     * 设置事件监听器
     * @param {HTMLCanvasElement} canvas - Canvas元素
     */
    setupEventListeners(canvas) {
        // 鼠标事件
        canvas.addEventListener('mousedown', (e) => this.onMouseDown(e));
        canvas.addEventListener('mousemove', (e) => this.onMouseMove(e));
        canvas.addEventListener('mouseup', (e) => this.onMouseUp(e));
        canvas.addEventListener('wheel', (e) => this.onWheel(e));
        
        // 触摸事件
        canvas.addEventListener('touchstart', (e) => this.onTouchStart(e));
        canvas.addEventListener('touchmove', (e) => this.onTouchMove(e));
        canvas.addEventListener('touchend', (e) => this.onTouchEnd(e));
        
        // 键盘事件
        window.addEventListener('keydown', (e) => this.onKeyDown(e));
        
        // 窗口大小变化
        window.addEventListener('resize', () => this.onResize());
    }

    /**
     * 更新场景
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        if (!this.isInitialized || this.animation.paused) return;
        
        this.animation.deltaTime = deltaTime * this.animation.timeScale;
        this.animation.time += this.animation.deltaTime;
        
        // 更新相机控制
        this.updateCameraController();
        
        // 更新粒子系统
        this.particleSystem.update(this.animation.deltaTime);
        
        // 更新光照
        this.updateLighting();
        
        // 更新性能统计
        this.updatePerformanceStats();
    }

    /**
     * 渲染场景
     */
    render() {
        if (!this.isInitialized) return;
        
        // 开始渲染帧
        this.webglRenderer.beginFrame();
        
        // 渲染背景
        this.renderBackground();
        
        // 渲染粒子系统
        this.particleSystem.render();
        
        // 应用后处理效果
        this.applyPostProcessing();
        
        // 结束渲染帧
        this.webglRenderer.endFrame();
        
        this.performance.frameCount++;
    }

    /**
     * 更新相机控制器
     */
    updateCameraController() {
        const controller = this.cameraController;
        
        // 自动旋转
        if (controller.autoRotate) {
            controller.targetAzimuth += controller.rotationSpeed * this.animation.deltaTime;
        }
        
        // 平滑插值
        controller.azimuth += (controller.targetAzimuth - controller.azimuth) * controller.smoothing;
        controller.elevation += (controller.targetElevation - controller.elevation) * controller.smoothing;
        controller.currentDistance += (controller.targetDistance - controller.currentDistance) * controller.smoothing;
        
        // 限制角度
        controller.elevation = Math.max(-80, Math.min(80, controller.elevation));
        controller.currentDistance = Math.max(controller.minDistance, Math.min(controller.maxDistance, controller.currentDistance));
        
        // 更新相机位置
        this.updateCamera();
    }

    /**
     * 更新相机
     */
    updateCamera() {
        if (!this.webglRenderer) return;
        
        const controller = this.cameraController;
        const azimuthRad = controller.azimuth * Math.PI / 180;
        const elevationRad = controller.elevation * Math.PI / 180;
        
        // 计算相机位置
        const x = controller.currentDistance * Math.cos(elevationRad) * Math.cos(azimuthRad);
        const y = controller.currentDistance * Math.sin(elevationRad);
        const z = controller.currentDistance * Math.cos(elevationRad) * Math.sin(azimuthRad);
        
        this.webglRenderer.camera.position = [x, y, z];
        this.webglRenderer.camera.target = [0, 0, 0];
        this.webglRenderer.camera.up = [0, 1, 0];
        
        this.webglRenderer.updateCamera();
    }

    /**
     * 更新光照
     */
    updateLighting() {
        // 动态光照效果
        const time = this.animation.time * 0.001;
        
        // 点光源位置动画
        this.scene.lighting.point.position = [
            Math.sin(time * 0.5) * 3,
            Math.cos(time * 0.3) * 2 + 2,
            Math.cos(time * 0.7) * 3
        ];
        
        // 点光源颜色动画
        const hue = (time * 30) % 360;
        const color = this.hslToRgb(hue, 0.8, 0.6);
        this.scene.lighting.point.color = [...color, 1.0];
        
        // 更新渲染器光照
        this.webglRenderer.lighting = this.scene.lighting;
    }

    /**
     * 渲染背景
     */
    renderBackground() {
        // 背景渲染逻辑
        // 这里可以添加星空、渐变等背景效果
    }

    /**
     * 应用后处理效果
     */
    applyPostProcessing() {
        // 后处理效果实现
        // 这里可以添加辉光、模糊、色彩调整等效果
    }

    /**
     * 更新性能统计
     */
    updatePerformanceStats() {
        const now = performance.now();
        
        if (now - this.performance.lastFpsUpdate > 1000) {
            this.performance.fps = this.performance.frameCount;
            this.performance.frameCount = 0;
            this.performance.lastFpsUpdate = now;
        }
        
        this.performance.frameTime = now - this.animation.lastFrameTime;
        this.animation.lastFrameTime = now;
    }

    /**
     * 鼠标按下事件
     * @param {MouseEvent} event - 鼠标事件
     */
    onMouseDown(event) {
        this.interaction.mouseDown = true;
        this.interaction.lastMouseX = event.clientX;
        this.interaction.lastMouseY = event.clientY;
        this.cameraController.autoRotate = false;
        
        // 检查是否点击了粒子
        this.handleParticleClick(event.clientX, event.clientY);
    }

    /**
     * 鼠标移动事件
     * @param {MouseEvent} event - 鼠标事件
     */
    onMouseMove(event) {
        if (!this.interaction.mouseDown) return;
        
        const deltaX = event.clientX - this.interaction.lastMouseX;
        const deltaY = event.clientY - this.interaction.lastMouseY;
        
        this.cameraController.targetAzimuth += deltaX * 0.5;
        this.cameraController.targetElevation -= deltaY * 0.5;
        
        this.interaction.lastMouseX = event.clientX;
        this.interaction.lastMouseY = event.clientY;
    }

    /**
     * 鼠标释放事件
     * @param {MouseEvent} event - 鼠标事件
     */
    onMouseUp(event) {
        this.interaction.mouseDown = false;
        
        // 延迟恢复自动旋转
        setTimeout(() => {
            if (!this.interaction.mouseDown) {
                this.cameraController.autoRotate = true;
            }
        }, 2000);
    }

    /**
     * 鼠标滚轮事件
     * @param {WheelEvent} event - 滚轮事件
     */
    onWheel(event) {
        event.preventDefault();
        
        const delta = event.deltaY > 0 ? 1 : -1;
        this.cameraController.targetDistance += delta * this.cameraController.zoomSpeed;
    }

    /**
     * 触摸开始事件
     * @param {TouchEvent} event - 触摸事件
     */
    onTouchStart(event) {
        event.preventDefault();
        
        this.interaction.touchCount = event.touches.length;
        this.cameraController.autoRotate = false;
        
        if (event.touches.length === 1) {
            const touch = event.touches[0];
            this.interaction.lastMouseX = touch.clientX;
            this.interaction.lastMouseY = touch.clientY;
            
            // 处理粒子点击
            this.handleParticleClick(touch.clientX, touch.clientY);
        } else if (event.touches.length === 2) {
            const touch1 = event.touches[0];
            const touch2 = event.touches[1];
            const distance = Math.sqrt(
                Math.pow(touch2.clientX - touch1.clientX, 2) +
                Math.pow(touch2.clientY - touch1.clientY, 2)
            );
            this.interaction.lastTouchDistance = distance;
        }
    }

    /**
     * 触摸移动事件
     * @param {TouchEvent} event - 触摸事件
     */
    onTouchMove(event) {
        event.preventDefault();
        
        if (event.touches.length === 1) {
            const touch = event.touches[0];
            const deltaX = touch.clientX - this.interaction.lastMouseX;
            const deltaY = touch.clientY - this.interaction.lastMouseY;
            
            this.cameraController.targetAzimuth += deltaX * 0.5;
            this.cameraController.targetElevation -= deltaY * 0.5;
            
            this.interaction.lastMouseX = touch.clientX;
            this.interaction.lastMouseY = touch.clientY;
        } else if (event.touches.length === 2) {
            const touch1 = event.touches[0];
            const touch2 = event.touches[1];
            const distance = Math.sqrt(
                Math.pow(touch2.clientX - touch1.clientX, 2) +
                Math.pow(touch2.clientY - touch1.clientY, 2)
            );
            
            const deltaDistance = distance - this.interaction.lastTouchDistance;
            this.cameraController.targetDistance -= deltaDistance * 0.01;
            this.interaction.lastTouchDistance = distance;
        }
    }

    /**
     * 触摸结束事件
     * @param {TouchEvent} event - 触摸事件
     */
    onTouchEnd(event) {
        event.preventDefault();
        
        this.interaction.touchCount = event.touches.length;
        
        if (event.touches.length === 0) {
            // 延迟恢复自动旋转
            setTimeout(() => {
                if (this.interaction.touchCount === 0) {
                    this.cameraController.autoRotate = true;
                }
            }, 2000);
        }
    }

    /**
     * 键盘按下事件
     * @param {KeyboardEvent} event - 键盘事件
     */
    onKeyDown(event) {
        switch (event.code) {
            case 'Space':
                event.preventDefault();
                this.animation.paused = !this.animation.paused;
                break;
            case 'KeyR':
                this.resetCamera();
                break;
            case 'KeyF':
                this.toggleFullscreen();
                break;
        }
    }

    /**
     * 窗口大小变化事件
     */
    onResize() {
        if (this.webglRenderer) {
            this.webglRenderer.resize();
        }
    }

    /**
     * 处理粒子点击
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     */
    handleParticleClick(x, y) {
        // 生成随机频率
        const frequency = 200 + Math.random() * 1600;
        
        // 激活粒子
        this.particleSystem.activateParticle(x, y, frequency);
        
        // 播放音效
        if (window.audioManager) {
            audioManager.playParticleActivation(frequency, 1.0);
        }
    }

    /**
     * 重置相机
     */
    resetCamera() {
        this.cameraController.targetAzimuth = 0;
        this.cameraController.targetElevation = 15;
        this.cameraController.targetDistance = 8;
        this.cameraController.autoRotate = true;
    }

    /**
     * 切换全屏
     */
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            this.webglRenderer.canvas.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    /**
     * HSL转RGB
     * @param {number} h - 色相
     * @param {number} s - 饱和度
     * @param {number} l - 亮度
     * @returns {Array} RGB数组
     */
    hslToRgb(h, s, l) {
        h /= 360;
        const c = (1 - Math.abs(2 * l - 1)) * s;
        const x = c * (1 - Math.abs((h * 6) % 2 - 1));
        const m = l - c / 2;
        
        let r, g, b;
        
        if (h < 1/6) {
            [r, g, b] = [c, x, 0];
        } else if (h < 2/6) {
            [r, g, b] = [x, c, 0];
        } else if (h < 3/6) {
            [r, g, b] = [0, c, x];
        } else if (h < 4/6) {
            [r, g, b] = [0, x, c];
        } else if (h < 5/6) {
            [r, g, b] = [x, 0, c];
        } else {
            [r, g, b] = [c, 0, x];
        }
        
        return [r + m, g + m, b + m];
    }

    /**
     * 获取场景统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            fps: this.performance.fps,
            frameTime: this.performance.frameTime,
            particles: this.particleSystem ? this.particleSystem.getStats() : null,
            renderer: this.webglRenderer ? this.webglRenderer.getStats() : null
        };
    }

    /**
     * 销毁场景管理器
     */
    destroy() {
        if (this.particleSystem) {
            this.particleSystem = null;
        }
        
        if (this.webglRenderer) {
            this.webglRenderer.destroy();
            this.webglRenderer = null;
        }
        
        this.isInitialized = false;
        
        console.log('🌌 3D场景管理器已销毁');
    }
}

// 创建全局3D场景管理器实例
window.sceneManager3D = new SceneManager3D();
