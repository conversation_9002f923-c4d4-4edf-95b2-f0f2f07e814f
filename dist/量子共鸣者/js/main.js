/**
 * 量子共鸣者 - 主应用程序入口
 * 负责初始化游戏系统和管理应用程序生命周期
 */

class QuantumResonanceApp {
    constructor() {
        this.isInitialized = false;
        this.loadingProgress = 0;
        this.systems = {};
        
        // 绑定事件处理器
        this.handleResize = this.handleResize.bind(this);
        this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
    }

    /**
     * 初始化应用程序
     */
    async init() {
        try {
            console.log('🚀 开始初始化量子共鸣者应用程序...');

            // 显示加载屏幕
            this.showLoadingScreen();

            // 等待DOM完全加载
            await this.waitForDOM();

            // 初始化核心系统
            await this.initializeSystems();

            // 设置事件监听器
            this.setupEventListeners();

            // 初始化UI
            this.initializeUI();

            // 完成初始化
            this.isInitialized = true;
            console.log('✅ 量子共鸣者应用程序初始化完成');

            // 隐藏加载屏幕，显示主菜单
            this.hideLoadingScreen();

        } catch (error) {
            console.error('❌ 应用程序初始化失败:', error);
            this.showErrorMessage('初始化失败，请刷新页面重试');
        }
    }

    /**
     * 等待DOM完全加载
     */
    async waitForDOM() {
        return new Promise((resolve) => {
            if (document.readyState === 'complete') {
                resolve();
            } else {
                window.addEventListener('load', resolve, { once: true });
            }
        });
    }

    /**
     * 显示加载屏幕
     */
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.classList.add('active');
        }
    }

    /**
     * 隐藏加载屏幕
     */
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const mainMenu = document.getElementById('main-menu-screen');
        
        if (loadingScreen && mainMenu) {
            setTimeout(() => {
                loadingScreen.classList.remove('active');
                mainMenu.classList.add('active');
            }, 1000);
        }
    }

    /**
     * 初始化核心系统
     */
    async initializeSystems() {
        const systemsToInit = [
            { name: 'storage', class: 'StorageService', progress: 10 },
            { name: 'i18n', class: 'I18nService', progress: 20 },
            { name: 'audio', class: 'AudioEngine', progress: 40 },
            { name: 'physics', class: 'PhysicsEngine', progress: 60 },
            { name: 'quantum', class: 'QuantumEngine', progress: 70 },
            { name: 'render', class: 'RenderEngine', progress: 85 },
            { name: 'ui', class: 'UIManager', progress: 95 }
        ];

        for (const system of systemsToInit) {
            try {
                this.updateLoadingProgress(system.progress, `正在初始化${system.name}系统...`);

                // 特殊处理存储系统 - 使用全局实例
                if (system.name === 'storage') {
                    if (window.storageService) {
                        await window.storageService.waitForInit();
                        this.systems[system.name] = window.storageService;
                        console.log(`✅ ${system.name}系统初始化完成`);
                    } else {
                        console.warn(`⚠️ 全局存储服务未找到`);
                    }
                } else {
                    // 检查系统类是否存在
                    if (window[system.class]) {
                        this.systems[system.name] = new window[system.class]();
                        if (this.systems[system.name].init) {
                            // 特殊处理渲染引擎，需要传入canvas参数
                            if (system.name === 'render') {
                                const gameCanvas = document.getElementById('game-canvas');
                                if (gameCanvas) {
                                    await this.systems[system.name].init(gameCanvas);
                                } else {
                                    console.error('❌ 未找到游戏画布元素 #game-canvas');
                                    throw new Error('游戏画布元素不存在');
                                }
                            } else {
                                await this.systems[system.name].init();
                            }
                        }

                        // 将重要系统设置为全局变量以便其他模块访问
                        if (system.name === 'ui') {
                            window.uiManager = this.systems[system.name];
                            console.log('🌐 UI Manager已设置为全局变量');
                        }

                        console.log(`✅ ${system.name}系统初始化完成`);
                    } else {
                        console.warn(`⚠️ ${system.class}类未找到，跳过${system.name}系统初始化`);
                    }
                }

                // 模拟加载时间
                await this.delay(100);

            } catch (error) {
                console.error(`❌ ${system.name}系统初始化失败:`, error);
                // 继续初始化其他系统，不要因为一个系统失败就停止
            }
        }

        this.updateLoadingProgress(100, '初始化完成！');
    }

    /**
     * 更新加载进度
     */
    updateLoadingProgress(progress, text) {
        this.loadingProgress = progress;
        
        const progressFill = document.getElementById('loading-progress-fill');
        const loadingText = document.getElementById('loading-text');
        
        if (progressFill) {
            progressFill.style.width = `${progress}%`;
        }
        
        if (loadingText) {
            loadingText.textContent = text;
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 窗口大小变化
        window.addEventListener('resize', this.handleResize);
        
        // 页面可见性变化
        document.addEventListener('visibilitychange', this.handleVisibilityChange);
        
        // 错误处理
        window.addEventListener('error', this.handleError.bind(this));
        window.addEventListener('unhandledrejection', this.handleUnhandledRejection.bind(this));
    }

    /**
     * 初始化UI
     */
    initializeUI() {
        // 初始化主菜单按钮事件
        this.setupMainMenuEvents();
        
        // 初始化设置面板
        this.setupSettingsEvents();
        
        // 初始化玩家管理
        this.setupPlayerEvents();
    }

    /**
     * 设置主菜单事件
     */
    setupMainMenuEvents() {
        const buttons = {
            'start-game-btn': () => this.startGame(),
            'level-editor-btn': () => this.openLevelEditor(),
            'custom-levels-btn': () => this.openCustomLevels(),
            'achievements-btn': () => this.openAchievements(),
            'leaderboard-btn': () => this.openLeaderboard(),
            'settings-btn': () => this.openSettings()
        };

        Object.entries(buttons).forEach(([id, handler]) => {
            const button = document.getElementById(id);
            if (button) {
                button.addEventListener('click', handler);
            }
        });
    }

    /**
     * 设置设置面板事件
     */
    setupSettingsEvents() {
        // 设置按钮事件将在 SettingsPanel 类中处理
        console.log('📋 设置面板事件已配置');
    }

    /**
     * 设置玩家管理事件
     */
    setupPlayerEvents() {
        // 玩家管理事件将在 PlayerManager 类中处理
        console.log('👤 玩家管理事件已配置');
    }

    /**
     * 开始游戏
     */
    startGame() {
        console.log('🎮 开始游戏');
        if (this.systems.ui && this.systems.ui.showScreen) {
            this.systems.ui.showScreen('levelSelectScreen');
        }
    }

    /**
     * 打开关卡编辑器
     */
    openLevelEditor() {
        console.log('🛠️ 打开关卡编辑器');
        if (this.systems.ui && this.systems.ui.showScreen) {
            this.systems.ui.showScreen('levelEditorScreen');
        }
    }

    /**
     * 打开自定义关卡
     */
    openCustomLevels() {
        console.log('🌐 打开自定义关卡');
        // 实现自定义关卡逻辑
    }

    /**
     * 打开成就系统
     */
    openAchievements() {
        console.log('🏅 打开成就系统');
        if (this.systems.ui && this.systems.ui.showScreen) {
            this.systems.ui.showScreen('achievementsScreen');
        }
    }

    /**
     * 打开排行榜
     */
    openLeaderboard() {
        console.log('🏆 打开排行榜');
        if (this.systems.ui && this.systems.ui.showScreen) {
            this.systems.ui.showScreen('leaderboardScreen');
        }
    }

    /**
     * 打开设置
     */
    openSettings() {
        console.log('⚙️ 打开设置');
        if (this.systems.ui && this.systems.ui.showScreen) {
            this.systems.ui.showScreen('settings-screen');
        }
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        if (this.systems.render && this.systems.render.handleResize) {
            this.systems.render.handleResize();
        }
    }

    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange() {
        if (document.hidden) {
            // 页面隐藏时暂停游戏
            if (this.systems.audio && this.systems.audio.pause) {
                this.systems.audio.pause();
            }
        } else {
            // 页面显示时恢复游戏
            if (this.systems.audio && this.systems.audio.resume) {
                this.systems.audio.resume();
            }
        }
    }

    /**
     * 处理错误
     */
    handleError(event) {
        console.error('🚨 应用程序错误:', event.error);
        this.showErrorMessage('发生了一个错误，请检查控制台获取详细信息');
    }

    /**
     * 处理未捕获的Promise拒绝
     */
    handleUnhandledRejection(event) {
        console.error('🚨 未处理的Promise拒绝:', event.reason);
        this.showErrorMessage('发生了一个异步错误，请检查控制台获取详细信息');
    }

    /**
     * 显示错误消息
     */
    showErrorMessage(message) {
        // 使用通知系统替代alert弹框
        if (window.notificationSystem) {
            window.notificationSystem.error(message, {
                title: '应用程序错误',
                duration: 8000 // 错误消息显示更长时间
            });
        } else {
            // 备用方案：如果通知系统未加载，仍使用alert
            alert(message);
        }
    }

    /**
     * 显示成功消息
     */
    showSuccessMessage(message, title = '成功') {
        if (window.notificationSystem) {
            window.notificationSystem.success(message, { title });
        }
    }

    /**
     * 显示信息消息
     */
    showInfoMessage(message, title = '信息') {
        if (window.notificationSystem) {
            window.notificationSystem.info(message, { title });
        }
    }

    /**
     * 显示警告消息
     */
    showWarningMessage(message, title = '警告') {
        if (window.notificationSystem) {
            window.notificationSystem.warning(message, { title });
        }
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取系统实例
     */
    getSystem(name) {
        return this.systems[name];
    }

    /**
     * 销毁应用程序
     */
    destroy() {
        // 移除事件监听器
        window.removeEventListener('resize', this.handleResize);
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);
        
        // 销毁所有系统
        Object.values(this.systems).forEach(system => {
            if (system.destroy) {
                system.destroy();
            }
        });
        
        this.systems = {};
        this.isInitialized = false;
        
        console.log('🔄 应用程序已销毁');
    }
}

// 全局应用程序实例
let quantumApp = null;

// 当DOM加载完成时初始化应用程序
document.addEventListener('DOMContentLoaded', async () => {
    try {
        quantumApp = new QuantumResonanceApp();
        await quantumApp.init();
        
        // 将应用程序实例暴露到全局作用域以便调试
        window.quantumApp = quantumApp;
        
    } catch (error) {
        console.error('❌ 应用程序启动失败:', error);
    }
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (quantumApp) {
        quantumApp.destroy();
    }
});
