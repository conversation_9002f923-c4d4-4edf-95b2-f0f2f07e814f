/**
 * 量子共鸣者 - 音频合成器
 * 用于生成复杂的音色和音效
 */

class AudioSynthesizer {
    constructor(audioEngine, config = {}) {
        this.audioEngine = audioEngine;
        this.config = {
            waveform: config.waveform || 'sine',
            attack: config.attack || 0.01,
            decay: config.decay || 0.1,
            sustain: config.sustain || 0.7,
            release: config.release || 0.3,
            filterType: config.filterType || 'lowpass',
            filterFrequency: config.filterFrequency || 1000,
            filterQ: config.filterQ || 1,
            ...config
        };
        
        this.voices = new Map(); // 当前播放的声音
        this.voiceCount = 0;
        
        console.log('🎹 音频合成器已创建');
    }

    /**
     * 播放音符
     * @param {number} frequency - 频率
     * @param {number} velocity - 力度 (0-1)
     * @param {number} startTime - 开始时间
     * @returns {string} 声音ID
     */
    playNote(frequency, velocity = 0.8, startTime = null) {
        if (!this.audioEngine.audioContext) return null;
        
        const audioContext = this.audioEngine.audioContext;
        const now = startTime || audioContext.currentTime;
        const voiceId = `voice_${Date.now()}_${this.voiceCount++}`;
        
        // 创建振荡器
        const oscillator = audioContext.createOscillator();
        oscillator.type = this.config.waveform;
        oscillator.frequency.setValueAtTime(frequency, now);
        
        // 创建滤波器
        const filter = audioContext.createBiquadFilter();
        filter.type = this.config.filterType;
        filter.frequency.setValueAtTime(this.config.filterFrequency, now);
        filter.Q.setValueAtTime(this.config.filterQ, now);
        
        // 创建包络
        const envelope = audioContext.createGain();
        envelope.gain.setValueAtTime(0, now);
        
        // ADSR包络
        const attackTime = this.config.attack;
        const decayTime = this.config.decay;
        const sustainLevel = this.config.sustain * velocity;
        
        envelope.gain.linearRampToValueAtTime(velocity, now + attackTime);
        envelope.gain.linearRampToValueAtTime(sustainLevel, now + attackTime + decayTime);
        
        // 连接音频节点
        oscillator.connect(filter);
        filter.connect(envelope);
        envelope.connect(this.audioEngine.sfxGain);
        
        // 开始播放
        oscillator.start(now);
        
        // 保存声音引用
        this.voices.set(voiceId, {
            oscillator,
            filter,
            envelope,
            frequency,
            startTime: now
        });
        
        return voiceId;
    }

    /**
     * 停止音符
     * @param {string} voiceId - 声音ID
     * @param {number} stopTime - 停止时间
     */
    stopNote(voiceId, stopTime = null) {
        const voice = this.voices.get(voiceId);
        if (!voice) return;
        
        const audioContext = this.audioEngine.audioContext;
        const now = stopTime || audioContext.currentTime;
        const releaseTime = this.config.release;
        
        // 释放包络
        voice.envelope.gain.cancelScheduledValues(now);
        voice.envelope.gain.setValueAtTime(voice.envelope.gain.value, now);
        voice.envelope.gain.linearRampToValueAtTime(0, now + releaseTime);
        
        // 停止振荡器
        voice.oscillator.stop(now + releaseTime);
        
        // 清理引用
        setTimeout(() => {
            this.voices.delete(voiceId);
        }, (releaseTime + 0.1) * 1000);
    }

    /**
     * 停止所有音符
     */
    stopAllNotes() {
        for (const voiceId of this.voices.keys()) {
            this.stopNote(voiceId);
        }
    }

    /**
     * 设置滤波器频率
     * @param {number} frequency - 滤波器频率
     * @param {number} time - 变化时间
     */
    setFilterFrequency(frequency, time = null) {
        const audioContext = this.audioEngine.audioContext;
        const now = time || audioContext.currentTime;
        
        this.config.filterFrequency = frequency;
        
        // 更新所有活跃声音的滤波器
        for (const voice of this.voices.values()) {
            voice.filter.frequency.setValueAtTime(frequency, now);
        }
    }

    /**
     * 设置合成器参数
     * @param {Object} params - 参数对象
     */
    setParameters(params) {
        Object.assign(this.config, params);
    }

    /**
     * 创建量子共鸣音效
     * @param {number} baseFrequency - 基础频率
     * @param {number} resonanceStrength - 共鸣强度
     * @param {number} duration - 持续时间
     */
    playQuantumResonance(baseFrequency, resonanceStrength, duration = 1.0) {
        if (!this.audioEngine.audioContext) return;
        
        const audioContext = this.audioEngine.audioContext;
        const now = audioContext.currentTime;
        
        // 创建多个谐波
        const harmonics = [1, 2, 3, 4, 5];
        const voices = [];
        
        harmonics.forEach((harmonic, index) => {
            const frequency = baseFrequency * harmonic;
            const amplitude = resonanceStrength / (harmonic * harmonic); // 谐波衰减
            
            // 创建振荡器
            const oscillator = audioContext.createOscillator();
            oscillator.type = 'sine';
            oscillator.frequency.setValueAtTime(frequency, now);
            
            // 创建包络
            const envelope = audioContext.createGain();
            envelope.gain.setValueAtTime(0, now);
            envelope.gain.linearRampToValueAtTime(amplitude, now + 0.1);
            envelope.gain.exponentialRampToValueAtTime(0.001, now + duration);
            
            // 添加频率调制
            const lfo = audioContext.createOscillator();
            const lfoGain = audioContext.createGain();
            lfo.type = 'sine';
            lfo.frequency.setValueAtTime(5 + index, now);
            lfoGain.gain.setValueAtTime(frequency * 0.01, now);
            
            lfo.connect(lfoGain);
            lfoGain.connect(oscillator.frequency);
            
            // 连接音频链
            oscillator.connect(envelope);
            envelope.connect(this.audioEngine.sfxGain);
            
            // 开始播放
            oscillator.start(now);
            lfo.start(now);
            
            // 停止播放
            oscillator.stop(now + duration);
            lfo.stop(now + duration);
            
            voices.push({ oscillator, envelope, lfo, lfoGain });
        });
        
        return voices;
    }

    /**
     * 创建粒子激活音效
     * @param {number} frequency - 粒子频率
     * @param {number} energy - 能量级别
     */
    playParticleActivation(frequency, energy = 1.0) {
        if (!this.audioEngine.audioContext) return;
        
        const audioContext = this.audioEngine.audioContext;
        const now = audioContext.currentTime;
        
        // 主音调
        const mainOsc = audioContext.createOscillator();
        mainOsc.type = 'sine';
        mainOsc.frequency.setValueAtTime(frequency, now);
        
        // 调制振荡器
        const modOsc = audioContext.createOscillator();
        const modGain = audioContext.createGain();
        modOsc.type = 'sine';
        modOsc.frequency.setValueAtTime(frequency * 0.1, now);
        modGain.gain.setValueAtTime(frequency * 0.05, now);
        
        // 包络
        const envelope = audioContext.createGain();
        envelope.gain.setValueAtTime(0, now);
        envelope.gain.linearRampToValueAtTime(energy * 0.3, now + 0.05);
        envelope.gain.exponentialRampToValueAtTime(0.001, now + 0.5);
        
        // 滤波器
        const filter = audioContext.createBiquadFilter();
        filter.type = 'lowpass';
        filter.frequency.setValueAtTime(frequency * 4, now);
        filter.frequency.exponentialRampToValueAtTime(frequency, now + 0.3);
        
        // 连接音频链
        modOsc.connect(modGain);
        modGain.connect(mainOsc.frequency);
        
        mainOsc.connect(filter);
        filter.connect(envelope);
        envelope.connect(this.audioEngine.sfxGain);
        
        // 开始播放
        mainOsc.start(now);
        modOsc.start(now);
        
        // 停止播放
        mainOsc.stop(now + 0.5);
        modOsc.stop(now + 0.5);
    }

    /**
     * 创建连锁反应音效
     * @param {Array} frequencies - 频率数组
     * @param {number} delay - 延迟时间
     */
    playChainReaction(frequencies, delay = 0.1) {
        frequencies.forEach((frequency, index) => {
            setTimeout(() => {
                this.playParticleActivation(frequency, 1.0 - index * 0.1);
            }, index * delay * 1000);
        });
    }

    /**
     * 创建环境音效
     * @param {string} type - 环境类型
     */
    playAmbientSound(type = 'quantum') {
        if (!this.audioEngine.audioContext) return;
        
        const audioContext = this.audioEngine.audioContext;
        
        switch (type) {
            case 'quantum':
                this.createQuantumAmbient();
                break;
            case 'space':
                this.createSpaceAmbient();
                break;
            case 'energy':
                this.createEnergyAmbient();
                break;
        }
    }

    /**
     * 创建量子环境音
     */
    createQuantumAmbient() {
        const audioContext = this.audioEngine.audioContext;
        const now = audioContext.currentTime;
        
        // 低频背景音
        const baseOsc = audioContext.createOscillator();
        baseOsc.type = 'sine';
        baseOsc.frequency.setValueAtTime(55, now);
        
        const baseGain = audioContext.createGain();
        baseGain.gain.setValueAtTime(0.1, now);
        
        // 高频闪烁音
        const sparkleOsc = audioContext.createOscillator();
        sparkleOsc.type = 'sine';
        sparkleOsc.frequency.setValueAtTime(2000, now);
        
        const sparkleGain = audioContext.createGain();
        sparkleGain.gain.setValueAtTime(0, now);
        
        // LFO调制闪烁
        const lfo = audioContext.createOscillator();
        lfo.type = 'triangle';
        lfo.frequency.setValueAtTime(0.3, now);
        
        const lfoGain = audioContext.createGain();
        lfoGain.gain.setValueAtTime(0.05, now);
        
        // 连接音频链
        lfo.connect(lfoGain);
        lfoGain.connect(sparkleGain.gain);
        
        baseOsc.connect(baseGain);
        sparkleOsc.connect(sparkleGain);
        
        baseGain.connect(this.audioEngine.musicGain);
        sparkleGain.connect(this.audioEngine.musicGain);
        
        // 开始播放
        baseOsc.start(now);
        sparkleOsc.start(now);
        lfo.start(now);
        
        // 保存引用以便停止
        this.ambientSounds = {
            baseOsc,
            sparkleOsc,
            lfo,
            baseGain,
            sparkleGain,
            lfoGain
        };
    }

    /**
     * 停止环境音
     */
    stopAmbientSound() {
        if (this.ambientSounds) {
            const now = this.audioEngine.audioContext.currentTime;
            
            // 淡出
            this.ambientSounds.baseGain.gain.linearRampToValueAtTime(0, now + 1);
            this.ambientSounds.sparkleGain.gain.linearRampToValueAtTime(0, now + 1);
            
            // 停止振荡器
            setTimeout(() => {
                this.ambientSounds.baseOsc.stop();
                this.ambientSounds.sparkleOsc.stop();
                this.ambientSounds.lfo.stop();
                this.ambientSounds = null;
            }, 1100);
        }
    }

    /**
     * 销毁合成器
     */
    destroy() {
        this.stopAllNotes();
        this.stopAmbientSound();
        this.voices.clear();
        
        console.log('🎹 音频合成器已销毁');
    }
}

// 导出类
window.AudioSynthesizer = AudioSynthesizer;
