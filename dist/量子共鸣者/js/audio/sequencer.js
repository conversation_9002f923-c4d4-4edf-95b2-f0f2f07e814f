/**
 * 量子共鸣者 - 音频序列器
 * 用于管理音乐序列和节拍
 */

class AudioSequencer {
    constructor(audioEngine) {
        this.audioEngine = audioEngine;
        this.isPlaying = false;
        this.bpm = 120;
        this.currentStep = 0;
        this.sequence = [];
        this.stepInterval = null;
        this.stepDuration = 0;
        
        // 默认序列模式
        this.patterns = {
            quantum: [
                { note: 'C4', velocity: 0.8, duration: 0.25 },
                { note: 'E4', velocity: 0.6, duration: 0.25 },
                { note: 'G4', velocity: 0.7, duration: 0.25 },
                { note: 'C5', velocity: 0.9, duration: 0.25 }
            ],
            space: [
                { note: 'A3', velocity: 0.7, duration: 0.5 },
                { note: 'D4', velocity: 0.8, duration: 0.5 },
                { note: 'F4', velocity: 0.6, duration: 0.5 },
                { note: 'A4', velocity: 0.9, duration: 0.5 }
            ],
            energy: [
                { note: 'E4', velocity: 0.9, duration: 0.125 },
                { note: 'G4', velocity: 0.8, duration: 0.125 },
                { note: 'B4', velocity: 0.7, duration: 0.125 },
                { note: 'E5', velocity: 1.0, duration: 0.125 }
            ]
        };
        
        this.calculateStepDuration();
        
        console.log('🎼 音频序列器已创建');
    }

    /**
     * 计算步骤持续时间
     */
    calculateStepDuration() {
        // 60秒 / BPM / 4 (16分音符)
        this.stepDuration = (60 / this.bpm / 4) * 1000;
    }

    /**
     * 设置BPM
     * @param {number} bpm - 每分钟节拍数
     */
    setBPM(bpm) {
        this.bpm = bpm;
        this.calculateStepDuration();
        
        if (this.isPlaying) {
            this.stop();
            this.start();
        }
    }

    /**
     * 设置序列模式
     * @param {string} patternName - 模式名称
     */
    setPattern(patternName) {
        if (this.patterns[patternName]) {
            this.sequence = [...this.patterns[patternName]];
            this.currentStep = 0;
            console.log(`🎼 设置序列模式: ${patternName}`);
        }
    }

    /**
     * 开始播放序列
     */
    start() {
        if (this.isPlaying) return;
        
        this.isPlaying = true;
        this.currentStep = 0;
        
        this.stepInterval = setInterval(() => {
            this.playCurrentStep();
            this.nextStep();
        }, this.stepDuration);
        
        console.log('🎼 序列器开始播放');
    }

    /**
     * 停止播放序列
     */
    stop() {
        if (!this.isPlaying) return;
        
        this.isPlaying = false;
        
        if (this.stepInterval) {
            clearInterval(this.stepInterval);
            this.stepInterval = null;
        }
        
        console.log('🎼 序列器停止播放');
    }

    /**
     * 播放当前步骤
     */
    playCurrentStep() {
        if (!this.sequence.length) return;
        
        const step = this.sequence[this.currentStep];
        if (step) {
            const frequency = this.noteToFrequency(step.note);
            
            // 使用音频引擎播放音符
            if (this.audioEngine && this.audioEngine.isReady()) {
                const oscillator = this.audioEngine.audioContext.createOscillator();
                const gainNode = this.audioEngine.audioContext.createGain();
                
                oscillator.frequency.value = frequency;
                oscillator.type = 'sine';
                
                gainNode.gain.setValueAtTime(0, this.audioEngine.audioContext.currentTime);
                gainNode.gain.linearRampToValueAtTime(
                    step.velocity * 0.3, 
                    this.audioEngine.audioContext.currentTime + 0.01
                );
                gainNode.gain.exponentialRampToValueAtTime(
                    0.001, 
                    this.audioEngine.audioContext.currentTime + step.duration
                );
                
                oscillator.connect(gainNode);
                gainNode.connect(this.audioEngine.musicGain);
                
                oscillator.start();
                oscillator.stop(this.audioEngine.audioContext.currentTime + step.duration);
            }
        }
    }

    /**
     * 移动到下一步
     */
    nextStep() {
        this.currentStep = (this.currentStep + 1) % this.sequence.length;
    }

    /**
     * 音符转频率
     * @param {string} note - 音符名称 (如 'C4', 'A#3')
     * @returns {number} 频率值
     */
    noteToFrequency(note) {
        const noteMap = {
            'C': 0, 'C#': 1, 'Db': 1, 'D': 2, 'D#': 3, 'Eb': 3,
            'E': 4, 'F': 5, 'F#': 6, 'Gb': 6, 'G': 7, 'G#': 8,
            'Ab': 8, 'A': 9, 'A#': 10, 'Bb': 10, 'B': 11
        };
        
        const match = note.match(/^([A-G][#b]?)(\d+)$/);
        if (!match) return 440; // 默认A4
        
        const noteName = match[1];
        const octave = parseInt(match[2]);
        
        const noteNumber = noteMap[noteName];
        if (noteNumber === undefined) return 440;
        
        // A4 = 440Hz, MIDI note 69
        const midiNote = (octave + 1) * 12 + noteNumber;
        const frequency = 440 * Math.pow(2, (midiNote - 69) / 12);
        
        return frequency;
    }

    /**
     * 添加自定义序列
     * @param {string} name - 序列名称
     * @param {Array} pattern - 序列模式
     */
    addPattern(name, pattern) {
        this.patterns[name] = pattern;
        console.log(`🎼 添加序列模式: ${name}`);
    }

    /**
     * 获取当前步骤
     * @returns {number} 当前步骤索引
     */
    getCurrentStep() {
        return this.currentStep;
    }

    /**
     * 获取序列长度
     * @returns {number} 序列长度
     */
    getSequenceLength() {
        return this.sequence.length;
    }

    /**
     * 是否正在播放
     * @returns {boolean} 播放状态
     */
    getIsPlaying() {
        return this.isPlaying;
    }

    /**
     * 销毁序列器
     */
    destroy() {
        this.stop();
        this.sequence = [];
        this.patterns = {};
        console.log('🎼 音频序列器已销毁');
    }
}

// 导出类到全局作用域
window.AudioSequencer = AudioSequencer;
