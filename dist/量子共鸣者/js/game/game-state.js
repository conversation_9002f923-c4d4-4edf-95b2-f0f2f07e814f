/**
 * 量子共鸣者 - 游戏状态管理器
 * 管理游戏的各种状态和状态转换
 */

class GameState {
    constructor() {
        // 游戏状态枚举
        this.STATES = {
            LOADING: 'loading',
            MAIN_MENU: 'main_menu',
            LEVEL_SELECT: 'level_select',
            PLAYING: 'playing',
            PAUSED: 'paused',
            GAME_OVER: 'game_over',
            LEVEL_COMPLETE: 'level_complete',
            SETTINGS: 'settings',
            ACHIEVEMENTS: 'achievements',
            LEADERBOARD: 'leaderboard',
            LEVEL_EDITOR: 'level_editor'
        };
        
        // 当前状态
        this.currentState = this.STATES.LOADING;
        this.previousState = null;
        
        // 状态历史
        this.stateHistory = [];
        this.maxHistoryLength = 10;
        
        // 状态变化监听器
        this.listeners = {};
        
        // 游戏数据
        this.gameData = {
            currentLevel: 1,
            score: 0,
            combo: 0,
            maxCombo: 0,
            lives: 3,
            timeElapsed: 0,
            frequency: 440,
            resonanceLevel: 0,
            multiplier: 1,
            achievements: [],
            statistics: {
                totalScore: 0,
                levelsCompleted: 0,
                perfectHits: 0,
                totalHits: 0,
                playTime: 0
            }
        };
        
        // 状态转换规则
        this.transitionRules = this.setupTransitionRules();
        
        this.init();
    }

    /**
     * 初始化游戏状态管理器
     */
    init() {
        // 加载保存的游戏数据
        this.loadGameData();
        
        console.log('🎮 游戏状态管理器初始化完成');
    }

    /**
     * 设置状态转换规则
     */
    setupTransitionRules() {
        return {
            [this.STATES.LOADING]: [
                this.STATES.MAIN_MENU
            ],
            [this.STATES.MAIN_MENU]: [
                this.STATES.LEVEL_SELECT,
                this.STATES.SETTINGS,
                this.STATES.ACHIEVEMENTS,
                this.STATES.LEADERBOARD,
                this.STATES.LEVEL_EDITOR
            ],
            [this.STATES.LEVEL_SELECT]: [
                this.STATES.PLAYING,
                this.STATES.MAIN_MENU
            ],
            [this.STATES.PLAYING]: [
                this.STATES.PAUSED,
                this.STATES.GAME_OVER,
                this.STATES.LEVEL_COMPLETE,
                this.STATES.MAIN_MENU
            ],
            [this.STATES.PAUSED]: [
                this.STATES.PLAYING,
                this.STATES.MAIN_MENU,
                this.STATES.SETTINGS
            ],
            [this.STATES.GAME_OVER]: [
                this.STATES.PLAYING,
                this.STATES.LEVEL_SELECT,
                this.STATES.MAIN_MENU
            ],
            [this.STATES.LEVEL_COMPLETE]: [
                this.STATES.PLAYING,
                this.STATES.LEVEL_SELECT,
                this.STATES.MAIN_MENU
            ],
            [this.STATES.SETTINGS]: [
                this.STATES.MAIN_MENU,
                this.STATES.PAUSED
            ],
            [this.STATES.ACHIEVEMENTS]: [
                this.STATES.MAIN_MENU
            ],
            [this.STATES.LEADERBOARD]: [
                this.STATES.MAIN_MENU
            ],
            [this.STATES.LEVEL_EDITOR]: [
                this.STATES.MAIN_MENU
            ]
        };
    }

    /**
     * 改变游戏状态
     */
    changeState(newState, data = {}) {
        // 验证状态转换是否合法
        if (!this.isValidTransition(newState)) {
            console.warn(`⚠️ 非法状态转换: ${this.currentState} -> ${newState}`);
            return false;
        }
        
        const oldState = this.currentState;
        
        // 执行状态退出逻辑
        this.onStateExit(oldState);
        
        // 更新状态
        this.previousState = oldState;
        this.currentState = newState;
        
        // 添加到历史记录
        this.addToHistory(oldState);
        
        // 执行状态进入逻辑
        this.onStateEnter(newState, data);
        
        // 通知监听器
        this.notifyListeners(oldState, newState, data);
        
        console.log(`🔄 状态变化: ${oldState} -> ${newState}`);
        return true;
    }

    /**
     * 验证状态转换是否合法
     */
    isValidTransition(newState) {
        const allowedStates = this.transitionRules[this.currentState];
        return allowedStates && allowedStates.includes(newState);
    }

    /**
     * 状态退出处理
     */
    onStateExit(state) {
        switch (state) {
            case this.STATES.PLAYING:
                // 暂停游戏逻辑
                this.pauseGameLogic();
                break;
                
            case this.STATES.PAUSED:
                // 清理暂停状态
                break;
        }
    }

    /**
     * 状态进入处理
     */
    onStateEnter(state, data) {
        switch (state) {
            case this.STATES.MAIN_MENU:
                this.onEnterMainMenu();
                break;
                
            case this.STATES.PLAYING:
                this.onEnterPlaying(data);
                break;
                
            case this.STATES.PAUSED:
                this.onEnterPaused();
                break;
                
            case this.STATES.GAME_OVER:
                this.onEnterGameOver(data);
                break;
                
            case this.STATES.LEVEL_COMPLETE:
                this.onEnterLevelComplete(data);
                break;
        }
    }

    /**
     * 进入主菜单状态
     */
    onEnterMainMenu() {
        // 重置游戏数据（保留统计信息）
        this.resetGameSession();
    }

    /**
     * 进入游戏状态
     */
    onEnterPlaying(data) {
        if (data.level) {
            this.gameData.currentLevel = data.level;
        }
        
        // 重置关卡数据
        this.gameData.score = 0;
        this.gameData.combo = 0;
        this.gameData.timeElapsed = 0;
        this.gameData.resonanceLevel = 0;
        this.gameData.multiplier = 1;
        
        // 开始游戏逻辑
        this.startGameLogic();
    }

    /**
     * 进入暂停状态
     */
    onEnterPaused() {
        this.pauseGameLogic();
    }

    /**
     * 进入游戏结束状态
     */
    onEnterGameOver(data) {
        // 更新统计信息
        this.updateStatistics();
        
        // 保存游戏数据
        this.saveGameData();
    }

    /**
     * 进入关卡完成状态
     */
    onEnterLevelComplete(data) {
        // 更新统计信息
        this.gameData.statistics.levelsCompleted++;
        this.updateStatistics();
        
        // 检查成就
        this.checkAchievements();
        
        // 保存游戏数据
        this.saveGameData();
    }

    /**
     * 添加状态监听器
     */
    addListener(eventType, callback) {
        if (!this.listeners[eventType]) {
            this.listeners[eventType] = [];
        }
        this.listeners[eventType].push(callback);
    }

    /**
     * 移除状态监听器
     */
    removeListener(eventType, callback) {
        if (this.listeners[eventType]) {
            const index = this.listeners[eventType].indexOf(callback);
            if (index > -1) {
                this.listeners[eventType].splice(index, 1);
            }
        }
    }

    /**
     * 通知监听器
     */
    notifyListeners(oldState, newState, data) {
        const eventType = 'stateChange';
        if (this.listeners[eventType]) {
            this.listeners[eventType].forEach(callback => {
                callback(oldState, newState, data);
            });
        }
    }

    /**
     * 添加到历史记录
     */
    addToHistory(state) {
        this.stateHistory.push({
            state: state,
            timestamp: Date.now()
        });
        
        // 限制历史记录长度
        if (this.stateHistory.length > this.maxHistoryLength) {
            this.stateHistory.shift();
        }
    }

    /**
     * 更新游戏数据
     */
    updateGameData(updates) {
        Object.assign(this.gameData, updates);
    }

    /**
     * 增加分数
     */
    addScore(points) {
        this.gameData.score += points * this.gameData.multiplier;
        this.gameData.statistics.totalScore += points * this.gameData.multiplier;
    }

    /**
     * 增加连击
     */
    addCombo() {
        this.gameData.combo++;
        this.gameData.maxCombo = Math.max(this.gameData.maxCombo, this.gameData.combo);
        
        // 更新倍数
        this.gameData.multiplier = Math.min(1 + Math.floor(this.gameData.combo / 10) * 0.5, 5);
    }

    /**
     * 重置连击
     */
    resetCombo() {
        this.gameData.combo = 0;
        this.gameData.multiplier = 1;
    }

    /**
     * 重置游戏会话
     */
    resetGameSession() {
        this.gameData.currentLevel = 1;
        this.gameData.score = 0;
        this.gameData.combo = 0;
        this.gameData.maxCombo = 0;
        this.gameData.lives = 3;
        this.gameData.timeElapsed = 0;
        this.gameData.resonanceLevel = 0;
        this.gameData.multiplier = 1;
    }

    /**
     * 开始游戏逻辑
     */
    startGameLogic() {
        // 游戏逻辑将在其他模块中实现
        console.log('🎮 游戏逻辑已开始');
    }

    /**
     * 暂停游戏逻辑
     */
    pauseGameLogic() {
        // 游戏逻辑将在其他模块中实现
        console.log('⏸️ 游戏逻辑已暂停');
    }

    /**
     * 更新统计信息
     */
    updateStatistics() {
        this.gameData.statistics.totalScore = Math.max(
            this.gameData.statistics.totalScore,
            this.gameData.score
        );
    }

    /**
     * 检查成就
     */
    checkAchievements() {
        // 成就检查逻辑将在成就系统中实现
        console.log('🏅 检查成就...');
    }

    /**
     * 加载游戏数据
     */
    loadGameData() {
        try {
            const savedData = localStorage.getItem('quantumResonance_gameData');
            if (savedData) {
                const parsed = JSON.parse(savedData);
                this.gameData = { ...this.gameData, ...parsed };
                console.log('💾 游戏数据已加载');
            }
        } catch (error) {
            console.error('❌ 加载游戏数据失败:', error);
        }
    }

    /**
     * 保存游戏数据
     */
    saveGameData() {
        try {
            localStorage.setItem('quantumResonance_gameData', JSON.stringify(this.gameData));
            console.log('💾 游戏数据已保存');
        } catch (error) {
            console.error('❌ 保存游戏数据失败:', error);
        }
    }

    /**
     * 获取当前状态
     */
    getCurrentState() {
        return this.currentState;
    }

    /**
     * 获取游戏数据
     */
    getGameData() {
        return { ...this.gameData };
    }

    /**
     * 获取状态历史
     */
    getStateHistory() {
        return [...this.stateHistory];
    }

    /**
     * 销毁游戏状态管理器
     */
    destroy() {
        this.listeners = {};
        this.stateHistory = [];
        console.log('🎮 游戏状态管理器已销毁');
    }
}

// 导出游戏状态类
window.GameState = GameState;
