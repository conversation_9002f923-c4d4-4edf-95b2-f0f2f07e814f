/**
 * 量子共鸣者 - 关卡管理器
 * 负责管理游戏关卡，包括关卡加载、保存、验证等功能
 */

class LevelManager {
    constructor() {
        this.isInitialized = false;
        
        // 当前关卡数据
        this.currentLevel = null;
        this.currentDifficulty = 'normal';
        
        // 关卡配置
        this.levelConfig = {
            maxParticles: 50,
            maxConnections: 100,
            timeLimit: 300, // 5分钟
            targetScore: 10000
        };
        
        // 内置关卡数据
        this.builtinLevels = new Map();
        
        // 自定义关卡数据
        this.customLevels = new Map();
        
        // 关卡进度数据
        this.levelProgress = new Map();
        
        console.log('🎯 关卡管理器已创建');
    }

    /**
     * 初始化关卡管理器
     */
    init() {
        try {
            // 加载内置关卡
            this.loadBuiltinLevels();
            
            // 加载自定义关卡
            this.loadCustomLevels();
            
            // 加载关卡进度
            this.loadLevelProgress();
            
            this.isInitialized = true;
            console.log('✅ 关卡管理器初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 关卡管理器初始化失败:', error);
            return false;
        }
    }

    /**
     * 加载内置关卡
     */
    loadBuiltinLevels() {
        // 教程关卡
        this.builtinLevels.set('tutorial', {
            id: 'tutorial',
            name: '量子入门',
            description: '学习基础的量子共鸣操作',
            author: 'Quantum Team',
            version: '1.0',
            difficulty: {
                easy: {
                    particles: [
                        { x: 0.3, y: 0.3, frequency: 440, energy: 50 },
                        { x: 0.7, y: 0.3, frequency: 880, energy: 50 },
                        { x: 0.5, y: 0.7, frequency: 660, energy: 50 }
                    ],
                    connections: [
                        { from: 0, to: 1, strength: 0.5 },
                        { from: 1, to: 2, strength: 0.5 }
                    ],
                    objectives: [
                        { type: 'activate_particles', target: 3, description: '激活所有粒子' },
                        { type: 'create_resonance', target: 1, description: '创造一次共鸣' }
                    ],
                    timeLimit: 120,
                    targetScore: 1000
                }
            },
            theme: 'quantum',
            music: 'tutorial_theme.mp3',
            // 教学提示配置
            tutorial: {
                enabled: true,
                tutorialId: 'level1',
                autoStart: true,
                showOnFirstPlay: true
            }
        });

        // 共鸣基础关卡
        this.builtinLevels.set('resonance_basics', {
            id: 'resonance_basics',
            name: '共鸣基础',
            description: '掌握粒子共鸣的基本原理',
            author: 'Quantum Team',
            version: '1.0',
            difficulty: {
                easy: {
                    particles: [
                        { x: 0.2, y: 0.2, frequency: 220, energy: 40 },
                        { x: 0.8, y: 0.2, frequency: 440, energy: 40 },
                        { x: 0.2, y: 0.8, frequency: 880, energy: 40 },
                        { x: 0.8, y: 0.8, frequency: 1760, energy: 40 }
                    ],
                    connections: [
                        { from: 0, to: 1, strength: 0.3 },
                        { from: 1, to: 3, strength: 0.3 },
                        { from: 2, to: 3, strength: 0.3 },
                        { from: 0, to: 2, strength: 0.3 }
                    ],
                    objectives: [
                        { type: 'chain_reaction', target: 2, description: '创造2次连锁反应' },
                        { type: 'score', target: 2000, description: '达到2000分' }
                    ],
                    timeLimit: 180,
                    targetScore: 2000
                },
                normal: {
                    particles: [
                        { x: 0.15, y: 0.15, frequency: 220, energy: 35 },
                        { x: 0.85, y: 0.15, frequency: 440, energy: 35 },
                        { x: 0.15, y: 0.85, frequency: 880, energy: 35 },
                        { x: 0.85, y: 0.85, frequency: 1760, energy: 35 },
                        { x: 0.5, y: 0.5, frequency: 660, energy: 60 }
                    ],
                    connections: [
                        { from: 0, to: 4, strength: 0.4 },
                        { from: 1, to: 4, strength: 0.4 },
                        { from: 2, to: 4, strength: 0.4 },
                        { from: 3, to: 4, strength: 0.4 }
                    ],
                    objectives: [
                        { type: 'chain_reaction', target: 3, description: '创造3次连锁反应' },
                        { type: 'score', target: 3500, description: '达到3500分' },
                        { type: 'time_bonus', target: 60, description: '在60秒内完成' }
                    ],
                    timeLimit: 150,
                    targetScore: 3500
                }
            },
            theme: 'quantum',
            music: 'resonance_theme.mp3'
        });

        // 连锁反应关卡
        this.builtinLevels.set('chain_reaction', {
            id: 'chain_reaction',
            name: '连锁反应',
            description: '创造壮观的连锁反应效果',
            author: 'Quantum Team',
            version: '1.0',
            difficulty: {
                normal: {
                    particles: [
                        { x: 0.1, y: 0.5, frequency: 110, energy: 30 },
                        { x: 0.3, y: 0.3, frequency: 220, energy: 30 },
                        { x: 0.3, y: 0.7, frequency: 220, energy: 30 },
                        { x: 0.5, y: 0.2, frequency: 440, energy: 30 },
                        { x: 0.5, y: 0.8, frequency: 440, energy: 30 },
                        { x: 0.7, y: 0.3, frequency: 880, energy: 30 },
                        { x: 0.7, y: 0.7, frequency: 880, energy: 30 },
                        { x: 0.9, y: 0.5, frequency: 1760, energy: 30 }
                    ],
                    connections: [
                        { from: 0, to: 1, strength: 0.3 },
                        { from: 0, to: 2, strength: 0.3 },
                        { from: 1, to: 3, strength: 0.3 },
                        { from: 2, to: 4, strength: 0.3 },
                        { from: 3, to: 5, strength: 0.3 },
                        { from: 4, to: 6, strength: 0.3 },
                        { from: 5, to: 7, strength: 0.3 },
                        { from: 6, to: 7, strength: 0.3 }
                    ],
                    objectives: [
                        { type: 'chain_reaction', target: 5, description: '创造5次连锁反应' },
                        { type: 'max_chain', target: 4, description: '单次连锁激活4个粒子' },
                        { type: 'score', target: 5000, description: '达到5000分' }
                    ],
                    timeLimit: 200,
                    targetScore: 5000
                },
                hard: {
                    particles: [
                        { x: 0.1, y: 0.5, frequency: 110, energy: 25 },
                        { x: 0.25, y: 0.25, frequency: 220, energy: 25 },
                        { x: 0.25, y: 0.75, frequency: 220, energy: 25 },
                        { x: 0.4, y: 0.1, frequency: 440, energy: 25 },
                        { x: 0.4, y: 0.5, frequency: 440, energy: 25 },
                        { x: 0.4, y: 0.9, frequency: 440, energy: 25 },
                        { x: 0.6, y: 0.1, frequency: 880, energy: 25 },
                        { x: 0.6, y: 0.5, frequency: 880, energy: 25 },
                        { x: 0.6, y: 0.9, frequency: 880, energy: 25 },
                        { x: 0.75, y: 0.25, frequency: 1760, energy: 25 },
                        { x: 0.75, y: 0.75, frequency: 1760, energy: 25 },
                        { x: 0.9, y: 0.5, frequency: 3520, energy: 25 }
                    ],
                    connections: [
                        { from: 0, to: 1, strength: 0.2 },
                        { from: 0, to: 2, strength: 0.2 },
                        { from: 1, to: 3, strength: 0.2 },
                        { from: 1, to: 4, strength: 0.2 },
                        { from: 2, to: 4, strength: 0.2 },
                        { from: 2, to: 5, strength: 0.2 },
                        { from: 3, to: 6, strength: 0.2 },
                        { from: 4, to: 7, strength: 0.2 },
                        { from: 5, to: 8, strength: 0.2 },
                        { from: 6, to: 9, strength: 0.2 },
                        { from: 7, to: 9, strength: 0.2 },
                        { from: 7, to: 10, strength: 0.2 },
                        { from: 8, to: 10, strength: 0.2 },
                        { from: 9, to: 11, strength: 0.2 },
                        { from: 10, to: 11, strength: 0.2 }
                    ],
                    objectives: [
                        { type: 'chain_reaction', target: 8, description: '创造8次连锁反应' },
                        { type: 'max_chain', target: 6, description: '单次连锁激活6个粒子' },
                        { type: 'score', target: 8000, description: '达到8000分' },
                        { type: 'efficiency', target: 0.8, description: '达到80%效率' }
                    ],
                    timeLimit: 180,
                    targetScore: 8000
                }
            },
            theme: 'energy',
            music: 'chain_reaction_theme.mp3'
        });

        console.log(`📚 已加载 ${this.builtinLevels.size} 个内置关卡`);
    }

    /**
     * 加载自定义关卡
     */
    loadCustomLevels() {
        if (window.storageService) {
            const customLevels = storageService.getCustomLevels();
            if (customLevels) {
                Object.entries(customLevels).forEach(([id, levelData]) => {
                    this.customLevels.set(id, levelData);
                });
            }
        }
        
        console.log(`🎨 已加载 ${this.customLevels.size} 个自定义关卡`);
    }

    /**
     * 加载关卡进度
     */
    loadLevelProgress() {
        if (window.storageService) {
            const progress = storageService.getLevelProgress();
            if (progress) {
                Object.entries(progress).forEach(([levelId, progressData]) => {
                    this.levelProgress.set(levelId, progressData);
                });
            }
        }
        
        console.log(`📊 已加载 ${this.levelProgress.size} 个关卡进度`);
    }

    /**
     * 获取关卡数据
     * @param {string} levelId - 关卡ID
     * @returns {Object|null} 关卡数据
     */
    getLevel(levelId) {
        return this.builtinLevels.get(levelId) || this.customLevels.get(levelId) || null;
    }

    /**
     * 获取关卡配置
     * @param {string} levelId - 关卡ID
     * @param {string} difficulty - 难度
     * @returns {Object|null} 关卡配置
     */
    getLevelConfig(levelId, difficulty = 'normal') {
        const level = this.getLevel(levelId);
        if (!level || !level.difficulty[difficulty]) {
            return null;
        }
        
        return {
            ...level,
            config: level.difficulty[difficulty]
        };
    }

    /**
     * 加载关卡
     * @param {string} levelId - 关卡ID
     * @param {string} difficulty - 难度
     * @returns {Promise<boolean>} 是否加载成功
     */
    async loadLevel(levelId, difficulty = 'normal') {
        try {
            const levelConfig = this.getLevelConfig(levelId, difficulty);
            if (!levelConfig) {
                throw new Error(`关卡 ${levelId} (${difficulty}) 不存在`);
            }
            
            this.currentLevel = levelConfig;
            this.currentDifficulty = difficulty;
            
            // 初始化游戏状态
            if (window.gameController) {
                await gameController.initializeLevel(levelConfig);
            }
            
            console.log(`🎮 已加载关卡: ${levelConfig.name} (${difficulty})`);
            return true;
        } catch (error) {
            console.error('❌ 关卡加载失败:', error);
            return false;
        }
    }

    /**
     * 验证关卡数据
     * @param {Object} levelData - 关卡数据
     * @returns {Object} 验证结果
     */
    validateLevel(levelData) {
        const errors = [];
        const warnings = [];
        
        // 检查必需字段
        if (!levelData.id) errors.push('缺少关卡ID');
        if (!levelData.name) errors.push('缺少关卡名称');
        if (!levelData.difficulty) errors.push('缺少难度配置');
        
        // 检查难度配置
        if (levelData.difficulty) {
            Object.entries(levelData.difficulty).forEach(([diff, config]) => {
                if (!config.particles || !Array.isArray(config.particles)) {
                    errors.push(`难度 ${diff} 缺少粒子配置`);
                }
                
                if (!config.objectives || !Array.isArray(config.objectives)) {
                    errors.push(`难度 ${diff} 缺少目标配置`);
                }
                
                // 检查粒子数量
                if (config.particles && config.particles.length > this.levelConfig.maxParticles) {
                    warnings.push(`难度 ${diff} 粒子数量过多 (${config.particles.length}/${this.levelConfig.maxParticles})`);
                }
                
                // 检查连接数量
                if (config.connections && config.connections.length > this.levelConfig.maxConnections) {
                    warnings.push(`难度 ${diff} 连接数量过多 (${config.connections.length}/${this.levelConfig.maxConnections})`);
                }
                
                // 检查时间限制
                if (config.timeLimit && config.timeLimit > this.levelConfig.timeLimit) {
                    warnings.push(`难度 ${diff} 时间限制过长 (${config.timeLimit}/${this.levelConfig.timeLimit})`);
                }
            });
        }
        
        return {
            valid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * 保存自定义关卡
     * @param {Object} levelData - 关卡数据
     * @returns {boolean} 是否保存成功
     */
    saveCustomLevel(levelData) {
        try {
            // 验证关卡数据
            const validation = this.validateLevel(levelData);
            if (!validation.valid) {
                throw new Error(`关卡验证失败: ${validation.errors.join(', ')}`);
            }
            
            // 添加元数据
            levelData.author = levelData.author || '未知作者';
            levelData.version = levelData.version || '1.0';
            levelData.created = new Date().toISOString();
            levelData.modified = new Date().toISOString();
            
            // 保存到内存
            this.customLevels.set(levelData.id, levelData);
            
            // 保存到存储
            if (window.storageService) {
                storageService.saveCustomLevel(levelData.id, levelData);
            }
            
            console.log(`💾 已保存自定义关卡: ${levelData.name}`);
            return true;
        } catch (error) {
            console.error('❌ 自定义关卡保存失败:', error);
            return false;
        }
    }

    /**
     * 删除自定义关卡
     * @param {string} levelId - 关卡ID
     * @returns {boolean} 是否删除成功
     */
    deleteCustomLevel(levelId) {
        try {
            if (!this.customLevels.has(levelId)) {
                throw new Error(`自定义关卡 ${levelId} 不存在`);
            }
            
            // 从内存中删除
            this.customLevels.delete(levelId);
            
            // 从存储中删除
            if (window.storageService) {
                storageService.deleteCustomLevel(levelId);
            }
            
            console.log(`🗑️ 已删除自定义关卡: ${levelId}`);
            return true;
        } catch (error) {
            console.error('❌ 自定义关卡删除失败:', error);
            return false;
        }
    }

    /**
     * 更新关卡进度
     * @param {string} levelId - 关卡ID
     * @param {string} difficulty - 难度
     * @param {Object} result - 游戏结果
     */
    updateLevelProgress(levelId, difficulty, result) {
        const progressKey = `${levelId}_${difficulty}`;
        const currentProgress = this.levelProgress.get(progressKey) || {
            levelId,
            difficulty,
            bestScore: 0,
            bestTime: Infinity,
            stars: 0,
            completed: false,
            attempts: 0
        };
        
        // 更新进度
        currentProgress.attempts++;
        currentProgress.bestScore = Math.max(currentProgress.bestScore, result.score || 0);
        currentProgress.bestTime = Math.min(currentProgress.bestTime, result.time || Infinity);
        currentProgress.completed = result.completed || false;
        
        // 计算星级
        if (result.completed) {
            let stars = 1; // 完成关卡获得1星
            
            const level = this.getLevelConfig(levelId, difficulty);
            if (level && level.config) {
                // 达到目标分数获得2星
                if (result.score >= level.config.targetScore) {
                    stars = 2;
                }
                
                // 在时间限制内完成获得3星
                if (result.time <= level.config.timeLimit * 0.7) {
                    stars = 3;
                }
            }
            
            currentProgress.stars = Math.max(currentProgress.stars, stars);
        }
        
        // 保存进度
        this.levelProgress.set(progressKey, currentProgress);
        
        if (window.storageService) {
            storageService.updateLevelProgress(progressKey, currentProgress);
        }
        
        console.log(`📈 已更新关卡进度: ${levelId} (${difficulty}) - ${currentProgress.stars}星`);
        
        // 检查是否解锁新关卡
        this.checkLevelUnlocks(levelId, difficulty, currentProgress);
    }

    /**
     * 检查关卡解锁
     * @param {string} completedLevelId - 完成的关卡ID
     * @param {string} difficulty - 难度
     * @param {Object} progress - 进度数据
     */
    checkLevelUnlocks(completedLevelId, difficulty, progress) {
        // 解锁逻辑：完成前一个关卡解锁下一个关卡
        const levelIds = Array.from(this.builtinLevels.keys());
        const currentIndex = levelIds.indexOf(completedLevelId);
        
        if (currentIndex >= 0 && currentIndex < levelIds.length - 1 && progress.completed) {
            const nextLevelId = levelIds[currentIndex + 1];
            
            // 解锁下一个关卡
            if (window.levelSelect) {
                levelSelect.unlockLevel(nextLevelId);
            }
            
            console.log(`🔓 已解锁新关卡: ${nextLevelId}`);
        }
    }

    /**
     * 获取关卡列表
     * @param {boolean} includeCustom - 是否包含自定义关卡
     * @returns {Array} 关卡列表
     */
    getLevelList(includeCustom = true) {
        const levels = [];
        
        // 添加内置关卡
        this.builtinLevels.forEach((level, id) => {
            levels.push({
                ...level,
                type: 'builtin',
                progress: this.getLevelProgress(id)
            });
        });
        
        // 添加自定义关卡
        if (includeCustom) {
            this.customLevels.forEach((level, id) => {
                levels.push({
                    ...level,
                    type: 'custom',
                    progress: this.getLevelProgress(id)
                });
            });
        }
        
        return levels;
    }

    /**
     * 获取关卡进度
     * @param {string} levelId - 关卡ID
     * @returns {Object} 进度数据
     */
    getLevelProgress(levelId) {
        const progress = {};
        
        // 获取所有难度的进度
        ['easy', 'normal', 'hard', 'expert'].forEach(difficulty => {
            const progressKey = `${levelId}_${difficulty}`;
            const difficultyProgress = this.levelProgress.get(progressKey);
            
            if (difficultyProgress) {
                progress[difficulty] = difficultyProgress;
            }
        });
        
        return progress;
    }

    /**
     * 获取当前关卡
     * @returns {Object|null} 当前关卡数据
     */
    getCurrentLevel() {
        return this.currentLevel;
    }

    /**
     * 获取当前难度
     * @returns {string} 当前难度
     */
    getCurrentDifficulty() {
        return this.currentDifficulty;
    }

    /**
     * 注册关卡包
     * @param {string} packId - 关卡包ID
     * @param {Object} packConfig - 关卡包配置
     */
    registerLevelPack(packId, packConfig) {
        try {
            console.log(`📦 注册关卡包: ${packId} - ${packConfig.name}`);

            // 验证关卡包配置
            if (!packConfig.name || !packConfig.levels || !Array.isArray(packConfig.levels)) {
                throw new Error(`关卡包配置无效: ${packId}`);
            }

            // 注册包中的所有关卡
            packConfig.levels.forEach((levelConfig, index) => {
                const levelId = `${packId}_${index}`;
                const fullLevelConfig = {
                    id: levelId,
                    name: levelConfig.name || `关卡 ${index + 1}`,
                    description: levelConfig.description || '',
                    author: packConfig.author || 'Unknown',
                    version: '1.0',
                    packId: packId,
                    packName: packConfig.name,
                    difficulty: {
                        easy: {
                            particles: levelConfig.particles || [],
                            connections: levelConfig.connections || [],
                            objectives: levelConfig.objectives || [],
                            timeLimit: levelConfig.timeLimit || 60,
                            targetScore: levelConfig.targetScore || 1000,
                            maxMoves: levelConfig.maxMoves || null
                        }
                    },
                    theme: levelConfig.theme || 'default',
                    music: levelConfig.music || 'default.mp3'
                };

                // 添加到内置关卡
                this.builtinLevels.set(levelId, fullLevelConfig);
                console.log(`  ✅ 注册关卡: ${levelId} - ${fullLevelConfig.name}`);
            });

            console.log(`✅ 关卡包 ${packId} 注册完成，包含 ${packConfig.levels.length} 个关卡`);
            return true;

        } catch (error) {
            console.error(`❌ 关卡包注册失败: ${packId}`, error);
            return false;
        }
    }

    /**
     * 获取关卡包信息
     * @param {string} packId - 关卡包ID
     * @returns {Object|null} 关卡包信息
     */
    getLevelPackInfo(packId) {
        const packLevels = [];

        // 查找属于该包的所有关卡
        for (const [levelId, levelConfig] of this.builtinLevels) {
            if (levelConfig.packId === packId) {
                packLevels.push({
                    id: levelId,
                    name: levelConfig.name,
                    description: levelConfig.description
                });
            }
        }

        if (packLevels.length > 0) {
            return {
                id: packId,
                name: packLevels[0].packName || packId,
                levels: packLevels
            };
        }

        return null;
    }

    /**
     * 获取所有关卡包
     * @returns {Array} 关卡包列表
     */
    getAllLevelPacks() {
        const packs = new Map();

        // 遍历所有内置关卡，按包分组
        for (const [levelId, levelConfig] of this.builtinLevels) {
            if (levelConfig.packId) {
                if (!packs.has(levelConfig.packId)) {
                    packs.set(levelConfig.packId, {
                        id: levelConfig.packId,
                        name: levelConfig.packName || levelConfig.packId,
                        levels: []
                    });
                }

                packs.get(levelConfig.packId).levels.push({
                    id: levelId,
                    name: levelConfig.name,
                    description: levelConfig.description
                });
            }
        }

        return Array.from(packs.values());
    }

    /**
     * 销毁关卡管理器
     */
    destroy() {
        this.currentLevel = null;
        this.builtinLevels.clear();
        this.customLevels.clear();
        this.levelProgress.clear();

        this.isInitialized = false;
        console.log('🎯 关卡管理器已销毁');
    }
}

// 创建全局关卡管理器实例
window.levelManager = new LevelManager();
