# Split-Second Spark 静态部署包

> 捕捉决定性瞬间，引燃无限可能 - 静态部署包

## 🎮 游戏列表

### 时空织梦者
- **路径**: `/时空织梦者/index.html`
- **描述**: 时间操控解谜游戏
- **特性**: 时间操控, 策略解谜, 梦境编织

### 瞬光捕手
- **路径**: `/瞬光捕手/index.html`
- **描述**: 反应速度挑战游戏
- **特性**: 精准时机, 连击系统, 反应挑战

### 量子共鸣者
- **路径**: `/量子共鸣者/index.html`
- **描述**: 音乐节奏物理模拟游戏
- **特性**: 量子共鸣, 音乐节奏, 物理模拟

## 🚀 快速部署

### 方法一：直接访问
1. 将整个文件夹上传到Web服务器
2. 访问 `http://your-domain.com/index.html`

### 方法二：本地测试
```bash
# Python 3
python -m http.server 8000

# Python 2  
python -m SimpleHTTPServer 8000

# Node.js
npx http-server -p 8000

# 访问 http://localhost:8000
```

## 📋 系统要求

- **浏览器**: Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **Web服务器**: 任何静态文件服务器 (Nginx, Apache, IIS等)
- **HTTPS**: 推荐使用HTTPS (PWA和音频功能需要)
- **存储**: 支持localStorage和IndexedDB
- **音频**: 支持Web Audio API (量子共鸣者需要)

## ✨ 功能特性

- **PWA支持**: 支持PWA安装
- **离线游戏**: 支持离线游戏
- **响应式设计**: 响应式设计，支持移动端
- **多语言**: 支持中英文切换
- **数据存储**: 本地数据存储

## 📦 构建信息

- **构建时间**: 2025-08-03 18:29:45
- **构建工具**: Python Static Packager
- **Python版本**: 3.11.2

---

**🎯 享受游戏体验！**
