/**
 * Split-Second Spark - 响应式样式
 * 针对不同设备尺寸的样式适配
 */

/* ===== 平板设备 (768px - 1024px) ===== */
@media (max-width: 1024px) {
    .games-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-lg);
    }
    
    .game-card {
        min-height: 380px;
    }
    
    .card-content {
        padding: var(--spacing-lg);
    }
    
    .game-icon {
        width: 70px;
        height: 70px;
        margin-bottom: var(--spacing-md);
    }
    
    .time-symbol,
    .spark-symbol,
    .quantum-symbol {
        font-size: 1.8rem;
    }
}

/* ===== 小平板设备 (600px - 768px) ===== */
@media (max-width: 768px) {
    .main-container {
        padding: 0 var(--spacing-sm);
    }
    
    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    .main-logo {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .logo-spark {
        width: 50px;
        height: 50px;
    }
    
    .logo-text .title {
        font-size: var(--font-xl);
    }
    
    .header-controls {
        justify-content: center;
    }
    
    .section-title {
        font-size: var(--font-xl);
    }
    
    .section-description {
        font-size: var(--font-md);
    }
    
    .games-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        max-width: 500px;
    }
    
    .game-card {
        min-height: 350px;
    }
    
    .footer-content {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    .footer-links {
        justify-content: center;
    }
    
    /* 模态框适配 */
    .modal-content {
        width: 95%;
        max-height: 90vh;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-md);
    }
    
    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .setting-select {
        width: 100%;
    }
}

/* ===== 手机设备 (480px - 600px) ===== */
@media (max-width: 600px) {
    .main-header {
        padding: var(--spacing-md) 0;
        margin-bottom: var(--spacing-xl);
    }
    
    .logo-text .title {
        font-size: var(--font-lg);
    }
    
    .logo-text .subtitle {
        font-size: var(--font-xs);
    }
    
    .control-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-xs);
    }
    
    .control-btn .btn-text {
        display: none;
    }
    
    .section-header {
        margin-bottom: var(--spacing-xl);
    }
    
    .section-title {
        font-size: var(--font-lg);
    }
    
    .section-description {
        font-size: var(--font-sm);
    }
    
    .game-card {
        min-height: 320px;
    }
    
    .card-content {
        padding: var(--spacing-md);
    }
    
    .game-icon {
        width: 60px;
        height: 60px;
        margin-bottom: var(--spacing-sm);
    }
    
    .time-symbol,
    .spark-symbol,
    .quantum-symbol {
        font-size: 1.5rem;
    }
    
    .game-title {
        font-size: var(--font-lg);
    }
    
    .game-subtitle {
        font-size: var(--font-xs);
    }
    
    .game-description {
        font-size: var(--font-xs);
        margin-bottom: var(--spacing-md);
    }
    
    .feature-tag {
        font-size: 0.7rem;
        padding: 2px var(--spacing-xs);
    }
    
    .card-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .play-btn,
    .preview-btn {
        max-width: none;
        width: 100%;
    }
}

/* ===== 小手机设备 (320px - 480px) ===== */
@media (max-width: 480px) {
    :root {
        --spacing-xs: 0.2rem;
        --spacing-sm: 0.4rem;
        --spacing-md: 0.8rem;
        --spacing-lg: 1.2rem;
        --spacing-xl: 1.6rem;
        --spacing-xxl: 2rem;
    }
    
    .main-container {
        padding: 0 var(--spacing-xs);
    }
    
    .main-header {
        padding: var(--spacing-sm) 0;
        margin-bottom: var(--spacing-lg);
    }
    
    .logo-spark {
        width: 40px;
        height: 40px;
    }
    
    .spark-core {
        width: 20px;
        height: 20px;
    }
    
    .ray {
        height: 15px;
    }
    
    .ray:nth-child(1) { transform: rotate(0deg) translateY(-25px); }
    .ray:nth-child(2) { transform: rotate(90deg) translateY(-25px); }
    .ray:nth-child(3) { transform: rotate(180deg) translateY(-25px); }
    .ray:nth-child(4) { transform: rotate(270deg) translateY(-25px); }
    
    .logo-text .title {
        font-size: var(--font-md);
    }
    
    .logo-text .subtitle {
        display: none;
    }
    
    .control-btn {
        padding: var(--spacing-xs);
        min-width: 40px;
        justify-content: center;
    }
    
    .control-btn .btn-icon {
        font-size: var(--font-md);
    }
    
    .section-title {
        font-size: var(--font-md);
    }
    
    .section-description {
        font-size: var(--font-xs);
        display: none;
    }
    
    .games-grid {
        gap: var(--spacing-md);
    }
    
    .game-card {
        min-height: 280px;
        border-radius: 12px;
    }
    
    .card-content {
        padding: var(--spacing-sm);
    }
    
    .game-icon {
        width: 50px;
        height: 50px;
        margin-bottom: var(--spacing-xs);
    }
    
    .time-symbol,
    .spark-symbol,
    .quantum-symbol {
        font-size: 1.2rem;
    }
    
    .game-title {
        font-size: var(--font-md);
        margin-bottom: 2px;
    }
    
    .game-subtitle {
        font-size: 0.7rem;
        margin-bottom: var(--spacing-xs);
    }
    
    .game-description {
        font-size: 0.7rem;
        line-height: 1.4;
        margin-bottom: var(--spacing-xs);
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .game-features {
        gap: 2px;
        margin-bottom: var(--spacing-xs);
    }
    
    .feature-tag {
        font-size: 0.6rem;
        padding: 1px 4px;
    }
    
    .play-btn,
    .preview-btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.7rem;
    }
    
    .btn-icon {
        font-size: 0.8rem;
    }
    
    /* 模态框适配 */
    .modal-content {
        width: 98%;
        max-height: 95vh;
        border-radius: 12px;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-sm);
    }
    
    .modal-title {
        font-size: var(--font-md);
    }
    
    .modal-close {
        font-size: var(--font-lg);
    }
    
    .settings-section-title {
        font-size: var(--font-sm);
    }
    
    .setting-label {
        font-size: var(--font-xs);
    }
    
    .setting-select {
        font-size: var(--font-xs);
        padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    .modal-footer {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .btn {
        width: 100%;
        justify-content: center;
        font-size: var(--font-xs);
        padding: var(--spacing-sm);
    }
    
    /* 底部信息适配 */
    .main-footer {
        padding: var(--spacing-lg) 0 var(--spacing-sm);
    }
    
    .footer-content {
        gap: var(--spacing-sm);
    }
    
    .footer-links {
        gap: var(--spacing-md);
        flex-wrap: wrap;
    }
    
    .footer-link {
        font-size: var(--font-xs);
    }
    
    .copyright,
    .version {
        font-size: 0.6rem;
    }
}

/* ===== 横屏模式适配 ===== */
@media (max-height: 600px) and (orientation: landscape) {
    .main-header {
        padding: var(--spacing-sm) 0;
        margin-bottom: var(--spacing-md);
    }
    
    .section-header {
        margin-bottom: var(--spacing-md);
    }
    
    .games-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-md);
    }
    
    .game-card {
        min-height: 250px;
    }
    
    .card-content {
        padding: var(--spacing-sm);
    }
    
    .game-icon {
        width: 40px;
        height: 40px;
        margin-bottom: var(--spacing-xs);
    }
    
    .main-footer {
        padding: var(--spacing-sm) 0;
    }
}

/* ===== 高分辨率屏幕适配 ===== */
@media (min-width: 1400px) {
    .games-grid {
        grid-template-columns: repeat(3, 1fr);
        max-width: 1300px;
    }
    
    .game-card {
        min-height: 450px;
    }
    
    .card-content {
        padding: var(--spacing-xxl);
    }
    
    .game-icon {
        width: 100px;
        height: 100px;
        margin-bottom: var(--spacing-xl);
    }
    
    .time-symbol,
    .spark-symbol,
    .quantum-symbol {
        font-size: 2.5rem;
    }
}
