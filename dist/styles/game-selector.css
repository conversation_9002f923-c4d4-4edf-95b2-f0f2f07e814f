/**
 * Split-Second Spark - 游戏选择器样式
 * 包含主界面、游戏卡片、模态框等组件样式
 */

/* ===== 主界面布局 ===== */
#main-screen {
    position: relative;
    overflow-y: auto;
    align-items: flex-start;
    padding: var(--spacing-md) 0;
}

.main-container {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* ===== 头部样式 ===== */
.main-header {
    padding: var(--spacing-lg) 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--spacing-xxl);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-lg);
}

.main-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.logo-spark {
    position: relative;
    width: 60px;
    height: 60px;
    flex-shrink: 0;
}

.logo-text .title {
    font-size: var(--font-title);
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-xs);
}

.logo-text .subtitle {
    font-size: var(--font-sm);
    color: var(--text-secondary);
}

.header-controls {
    display: flex;
    gap: var(--spacing-md);
}

.control-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    text-decoration: none;
    font-size: var(--font-sm);
    cursor: pointer;
    transition: all var(--transition-fast) ease;
    backdrop-filter: blur(10px);
}

.control-btn:hover {
    border-color: var(--border-hover);
    background: var(--bg-secondary);
    transform: translateY(-1px);
}

.control-btn .btn-icon {
    font-size: var(--font-lg);
}

/* ===== 游戏选择区域 ===== */
.games-section {
    flex: 1;
    margin-bottom: var(--spacing-xxl);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xxl);
}

.section-title {
    font-size: var(--font-title);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-description {
    font-size: var(--font-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* ===== 游戏网格 ===== */
.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    max-width: 1200px;
    margin: 0 auto;
}

/* ===== 游戏卡片 ===== */
.game-card {
    position: relative;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    overflow: hidden;
    transition: all var(--transition-normal) ease;
    backdrop-filter: blur(20px);
    cursor: pointer;
    min-height: 400px;
}

.game-card:hover {
    transform: translateY(-8px);
    border-color: var(--border-hover);
    box-shadow: var(--shadow-heavy);
}

.card-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.1;
    transition: opacity var(--transition-normal) ease;
}

.game-card:hover .card-background {
    opacity: 0.2;
}

.card-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.temporal-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.spark-gradient {
    background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
}

.quantum-gradient {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

/* ===== 卡片动画元素 ===== */
.card-particles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: float 3s ease-in-out infinite;
}

.particle:nth-child(1) {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.particle:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 1s;
}

.particle:nth-child(3) {
    top: 80%;
    left: 40%;
    animation-delay: 2s;
}

.card-sparks {
    position: absolute;
    width: 100%;
    height: 100%;
}

.spark {
    position: absolute;
    width: 2px;
    height: 20px;
    background: linear-gradient(to top, transparent, rgba(255, 255, 255, 0.8));
    animation: sparkle 2s ease-in-out infinite;
}

.spark:nth-child(1) {
    top: 30%;
    left: 30%;
    animation-delay: 0s;
}

.spark:nth-child(2) {
    top: 50%;
    left: 70%;
    animation-delay: 0.5s;
}

.spark:nth-child(3) {
    top: 70%;
    left: 20%;
    animation-delay: 1s;
}

.spark:nth-child(4) {
    top: 20%;
    left: 80%;
    animation-delay: 1.5s;
}

.card-quantum {
    position: absolute;
    width: 100%;
    height: 100%;
}

.quantum-orbit {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    transform: translate(-50%, -50%);
    animation: rotate 10s linear infinite;
}

.electron {
    position: absolute;
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 50%;
    animation: orbit 3s linear infinite;
}

.electron:nth-child(1) {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0s;
}

.electron:nth-child(2) {
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    animation-delay: 1s;
}

.electron:nth-child(3) {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 2s;
}

/* ===== 卡片内容 ===== */
.card-content {
    position: relative;
    z-index: 2;
    padding: var(--spacing-xl);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.game-icon {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.time-symbol,
.spark-symbol,
.quantum-symbol {
    font-size: 2rem;
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.dream-waves,
.spark-rings,
.resonance-waves {
    position: absolute;
    width: 100%;
    height: 100%;
}

.wave,
.ring {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    animation: ripple 2s ease-out infinite;
}

.wave:nth-child(1),
.ring:nth-child(1) {
    width: 100%;
    height: 100%;
    animation-delay: 0s;
}

.wave:nth-child(2),
.ring:nth-child(2) {
    width: 120%;
    height: 120%;
    top: -10%;
    left: -10%;
    animation-delay: 0.7s;
}

.wave:nth-child(3) {
    width: 140%;
    height: 140%;
    top: -20%;
    left: -20%;
    animation-delay: 1.4s;
}

.game-info {
    text-align: center;
    flex: 1;
    margin-bottom: var(--spacing-lg);
}

.game-title {
    font-size: var(--font-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.game-subtitle {
    font-size: var(--font-sm);
    color: var(--text-muted);
    margin-bottom: var(--spacing-md);
    font-style: italic;
}

.game-description {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.game-features {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    justify-content: center;
}

.feature-tag {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    font-size: var(--font-xs);
    color: var(--text-secondary);
    backdrop-filter: blur(5px);
}

.card-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
}

.play-btn,
.preview-btn {
    flex: 1;
    max-width: 120px;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: 8px;
    font-size: var(--font-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast) ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
}

.play-btn {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-medium);
}

.play-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.preview-btn {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.preview-btn:hover {
    border-color: var(--border-hover);
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.05);
}

/* ===== 动画定义 ===== */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes sparkle {
    0%, 100% { opacity: 0; transform: scale(0); }
    50% { opacity: 1; transform: scale(1); }
}

@keyframes orbit {
    from { transform: rotate(0deg) translateX(40px) rotate(0deg); }
    to { transform: rotate(360deg) translateX(40px) rotate(-360deg); }
}

@keyframes ripple {
    0% {
        opacity: 1;
        transform: scale(0.8);
    }
    100% {
        opacity: 0;
        transform: scale(1.2);
    }
}

/* ===== 底部信息 ===== */
.main-footer {
    margin-top: auto;
    padding: var(--spacing-xl) 0;
    border-top: 1px solid var(--border-color);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-lg);
}

.footer-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.copyright,
.version {
    font-size: var(--font-xs);
    color: var(--text-muted);
}

.footer-links {
    display: flex;
    gap: var(--spacing-lg);
}

.footer-link {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--transition-fast) ease;
}

.footer-link:hover {
    color: var(--text-accent);
}

/* ===== 模态框样式 ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal) ease;
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-modal);
    backdrop-filter: blur(10px);
    cursor: pointer;
}

.modal-content {
    position: relative;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: var(--shadow-heavy);
    transform: scale(0.9);
    transition: transform var(--transition-normal) ease;
}

.modal.active .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    font-size: var(--font-xl);
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-xxl);
    color: var(--text-muted);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: 4px;
    transition: all var(--transition-fast) ease;
    line-height: 1;
}

.modal-close:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: var(--spacing-lg);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

/* ===== 设置模态框 ===== */
.settings-section {
    margin-bottom: var(--spacing-xl);
}

.settings-section:last-child {
    margin-bottom: 0;
}

.settings-section-title {
    font-size: var(--font-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-label {
    font-size: var(--font-md);
    color: var(--text-secondary);
    font-weight: 500;
}

.setting-select {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    font-size: var(--font-sm);
    min-width: 120px;
    cursor: pointer;
    transition: all var(--transition-fast) ease;
}

.setting-select:hover,
.setting-select:focus {
    border-color: var(--border-hover);
    outline: none;
}

.setting-select option {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}
