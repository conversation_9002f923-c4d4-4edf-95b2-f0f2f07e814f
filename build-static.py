#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Split-Second Spark 静态部署打包脚本
自动清理开发文件，生成生产环境部署包
"""

import os
import shutil
import zipfile
import json
from pathlib import Path
import re
from datetime import datetime

class StaticPackager:
    def __init__(self, source_dir=".", output_dir="dist"):
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.package_name = "split-second-spark-static"
        
        # 需要排除的文件和目录模式
        self.exclude_patterns = [
            # 测试和调试文件
            r'.*test.*\.html$',
            r'.*debug.*\.html$',
            r'.*demo.*\.html$',
            r'.*测试.*\.html$',
            r'.*修复.*\.html$',
            r'.*验证.*\.html$',
            r'.*诊断.*\.html$',
            r'.*检查.*\.html$',
            r'.*fix.*\.html$',
            r'.*verification.*\.html$',
            r'.*diagnostic.*\.html$',

            # JavaScript测试和调试文件
            r'.*test.*\.js$',
            r'.*debug.*\.js$',
            r'.*fix.*\.js$',
            r'.*verification.*\.js$',
            r'.*diagnostic.*\.js$',
            r'.*测试.*\.js$',
            r'.*修复.*\.js$',
            r'.*验证.*\.js$',
            r'.*compatibility.*\.js$',
            r'.*performance.*\.js$',

            # 文档文件
            r'.*\.md$',
            r'.*/docs/.*',
            r'.*说明.*\.md$',
            r'.*指南.*\.md$',
            r'.*报告.*\.md$',

            # 开发工具
            r'.*\.py$',
            r'.*\.bat$',
            r'.*\.sh$',
            r'.*server\.py$',
            r'.*build.*\.py$',

            # 临时文件和缓存
            r'.*/__pycache__/.*',
            r'.*\.log$',
            r'.*\.tmp$',
            r'.*\.cache$',

            # 版本控制
            r'.*\.git/.*',
            r'.*\.gitignore$',
            r'.*\.gitattributes$',

            # IDE文件
            r'.*\.vscode/.*',
            r'.*\.idea/.*',
            r'.*\.DS_Store$',

            # 构建输出
            r'.*/dist/.*',
            r'.*/build/.*',
            r'.*/node_modules/.*',

            # 特定的开发文件
            r'.*create-.*\.html$',
            r'.*generate-.*\.html$',
            r'.*generate-.*\.js$',
            r'.*优化示例.*\.html$',
        ]
        
        # 必须保留的核心文件
        self.core_files = [
            'index.html',
            'manifest.json',
        ]
        
        # 必须保留的目录
        self.core_dirs = [
            'styles',
            'js',
            'assets',
            '时空织梦者',
            '瞬光捕手',
            '量子共鸣者',
        ]

    def should_exclude(self, file_path):
        """检查文件是否应该被排除"""
        file_str = str(file_path)

        # 检查排除模式
        for pattern in self.exclude_patterns:
            if re.search(pattern, file_str, re.IGNORECASE):
                return True

        # 特殊处理：只保留游戏目录中的核心文件
        if any(game_dir in file_str for game_dir in ['时空织梦者', '瞬光捕手', '量子共鸣者']):
            # 获取相对于游戏目录的路径
            parts = Path(file_str).parts
            if len(parts) > 1:
                game_dir_index = -1
                for i, part in enumerate(parts):
                    if part in ['时空织梦者', '瞬光捕手', '量子共鸣者']:
                        game_dir_index = i
                        break

                if game_dir_index >= 0:
                    # 获取游戏目录内的相对路径
                    relative_parts = parts[game_dir_index + 1:]
                    if relative_parts:
                        relative_path = '/'.join(relative_parts)

                        # 只保留核心文件和目录
                        allowed_patterns = [
                            r'^index\.html$',           # 主入口文件
                            r'^manifest\.json$',        # PWA配置
                            r'^js/.*\.js$',            # js目录下的所有JS文件
                            r'^styles/.*\.css$',       # styles目录下的所有CSS文件
                            r'^assets/.*',             # assets目录下的所有文件
                        ]

                        # 检查是否匹配允许的模式
                        for allowed_pattern in allowed_patterns:
                            if re.match(allowed_pattern, relative_path):
                                return False

                        # 如果不匹配任何允许的模式，则排除
                        return True

        return False

    def copy_file_if_needed(self, src_file, dst_file):
        """如果需要，复制文件"""
        if self.should_exclude(src_file):
            return False
        
        # 确保目标目录存在
        dst_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 复制文件
        shutil.copy2(src_file, dst_file)
        return True

    def package_static_files(self):
        """打包静态文件"""
        print(f"🚀 开始打包 Split-Second Spark 静态部署包...")
        print(f"   源目录: {self.source_dir}")
        print(f"   输出目录: {self.output_dir}")
        
        # 清理输出目录
        if self.output_dir.exists():
            print(f"🧹 清理现有输出目录...")
            shutil.rmtree(self.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 统计信息
        total_files = 0
        copied_files = 0
        excluded_files = 0
        
        # 遍历源目录
        for root, dirs, files in os.walk(self.source_dir):
            root_path = Path(root)
            
            # 跳过输出目录本身
            if root_path == self.output_dir or self.output_dir in root_path.parents:
                continue
            
            for file in files:
                src_file = root_path / file
                rel_path = src_file.relative_to(self.source_dir)
                dst_file = self.output_dir / rel_path
                
                total_files += 1
                
                if self.copy_file_if_needed(src_file, dst_file):
                    copied_files += 1
                    print(f"✅ 复制: {rel_path}")
                else:
                    excluded_files += 1
                    print(f"❌ 排除: {rel_path}")
        
        print(f"\n📊 打包统计:")
        print(f"   总文件数: {total_files}")
        print(f"   复制文件: {copied_files}")
        print(f"   排除文件: {excluded_files}")
        print(f"   压缩比例: {excluded_files/total_files*100:.1f}% 文件被排除")
        
        return copied_files > 0

    def create_deployment_info(self):
        """创建部署信息文件"""
        deployment_info = {
            "name": "Split-Second Spark",
            "version": "1.0.0",
            "description": "捕捉决定性瞬间，引燃无限可能 - 静态部署包",
            "build_time": datetime.now().isoformat(),
            "games": [
                {
                    "name": "时空织梦者",
                    "path": "/时空织梦者/index.html",
                    "description": "时间操控解谜游戏",
                    "features": ["时间操控", "策略解谜", "梦境编织"]
                },
                {
                    "name": "瞬光捕手", 
                    "path": "/瞬光捕手/index.html",
                    "description": "反应速度挑战游戏",
                    "features": ["精准时机", "连击系统", "反应挑战"]
                },
                {
                    "name": "量子共鸣者",
                    "path": "/量子共鸣者/index.html", 
                    "description": "音乐节奏物理模拟游戏",
                    "features": ["量子共鸣", "音乐节奏", "物理模拟"]
                }
            ],
            "deployment": {
                "type": "static",
                "entry_point": "/index.html",
                "requirements": {
                    "web_server": "任何静态文件服务器 (Nginx, Apache, IIS等)",
                    "https": "推荐使用HTTPS (PWA和音频功能需要)",
                    "browser": "Chrome 60+, Firefox 55+, Safari 12+, Edge 79+",
                    "storage": "支持localStorage和IndexedDB",
                    "audio": "支持Web Audio API (量子共鸣者需要)"
                },
                "features": {
                    "pwa": "支持PWA安装",
                    "offline": "支持离线游戏",
                    "responsive": "响应式设计，支持移动端",
                    "i18n": "支持中英文切换",
                    "storage": "本地数据存储"
                }
            },
            "build_info": {
                "packager": "Python Static Packager",
                "build_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "platform": os.name,
                "python_version": f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}"
            }
        }
        
        info_file = self.output_dir / "deployment-info.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(deployment_info, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建部署信息: {info_file}")
        return deployment_info

    def create_readme(self, deployment_info):
        """创建部署说明文件"""
        readme_content = f"""# Split-Second Spark 静态部署包

> {deployment_info['description']}

## 🎮 游戏列表

"""
        
        for game in deployment_info['games']:
            readme_content += f"### {game['name']}\n"
            readme_content += f"- **路径**: `{game['path']}`\n"
            readme_content += f"- **描述**: {game['description']}\n"
            readme_content += f"- **特性**: {', '.join(game['features'])}\n\n"
        
        readme_content += f"""## 🚀 快速部署

### 方法一：直接访问
1. 将整个文件夹上传到Web服务器
2. 访问 `http://your-domain.com/index.html`

### 方法二：本地测试
```bash
# Python 3
python -m http.server 8000

# Python 2  
python -m SimpleHTTPServer 8000

# Node.js
npx http-server -p 8000

# 访问 http://localhost:8000
```

## 📋 系统要求

- **浏览器**: {deployment_info['deployment']['requirements']['browser']}
- **Web服务器**: {deployment_info['deployment']['requirements']['web_server']}
- **HTTPS**: {deployment_info['deployment']['requirements']['https']}
- **存储**: {deployment_info['deployment']['requirements']['storage']}
- **音频**: {deployment_info['deployment']['requirements']['audio']}

## ✨ 功能特性

- **PWA支持**: {deployment_info['deployment']['features']['pwa']}
- **离线游戏**: {deployment_info['deployment']['features']['offline']}
- **响应式设计**: {deployment_info['deployment']['features']['responsive']}
- **多语言**: {deployment_info['deployment']['features']['i18n']}
- **数据存储**: {deployment_info['deployment']['features']['storage']}

## 📦 构建信息

- **构建时间**: {deployment_info['build_info']['build_date']}
- **构建工具**: {deployment_info['build_info']['packager']}
- **Python版本**: {deployment_info['build_info']['python_version']}

---

**🎯 享受游戏体验！**
"""
        
        readme_file = self.output_dir / "README.md"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"✅ 创建部署说明: {readme_file}")

    def create_zip_package(self):
        """创建ZIP压缩包"""
        zip_file = self.output_dir.parent / f"{self.package_name}.zip"
        
        print(f"📦 正在创建压缩包...")
        
        with zipfile.ZipFile(zip_file, 'w', zipfile.ZIP_DEFLATED) as zf:
            for root, dirs, files in os.walk(self.output_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_path = file_path.relative_to(self.output_dir)
                    zf.write(file_path, arc_path)
        
        file_size_mb = zip_file.stat().st_size / 1024 / 1024
        print(f"✅ 压缩包创建完成: {zip_file}")
        print(f"   文件大小: {file_size_mb:.2f} MB")
        
        return zip_file

    def run(self):
        """执行完整的打包流程"""
        try:
            print("=" * 60)
            print("🎮 Split-Second Spark 静态部署打包工具")
            print("=" * 60)
            
            # 1. 打包静态文件
            if not self.package_static_files():
                print("❌ 打包失败：没有文件被复制")
                return False
            
            # 2. 创建部署信息
            deployment_info = self.create_deployment_info()
            
            # 3. 创建部署说明
            self.create_readme(deployment_info)
            
            # 4. 创建压缩包
            zip_file = self.create_zip_package()
            
            print("\n" + "=" * 60)
            print("🎉 打包完成！")
            print("=" * 60)
            print(f"📁 部署目录: {self.output_dir}")
            print(f"📦 压缩包: {zip_file}")
            print(f"\n📋 部署说明:")
            print(f"   1. 解压 {zip_file.name} 到Web服务器")
            print(f"   2. 配置Web服务器指向解压目录")
            print(f"   3. 访问 http://your-domain.com/index.html")
            print(f"   4. 推荐使用HTTPS以支持PWA和音频功能")
            print(f"\n🌐 支持的部署平台:")
            print(f"   • GitHub Pages")
            print(f"   • Vercel")
            print(f"   • Netlify")
            print(f"   • 阿里云OSS")
            print(f"   • 腾讯云COS")
            print(f"   • 任何静态文件服务器")
            
            return True
            
        except Exception as e:
            print(f"❌ 打包过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    packager = StaticPackager()
    success = packager.run()
    exit(0 if success else 1)
